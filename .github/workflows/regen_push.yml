name: Regenerate Files

permissions:
  contents: write

on:
  push:
    branches:
    - master
    - develop

jobs:
  regen:
    runs-on: ubuntu-latest

    container: ghcr.io/qmk/qmk_cli

    steps:
    - name: Disable safe.directory check
      run : git config --global --add safe.directory '*'

    - uses: actions/checkout@v4

    - name: Run qmk generators
      run: |
        util/regen.sh
        git diff

    - uses: rlespinasse/github-slug-action@v5

    - name: Become QMK Bot
      run: |
        git config user.name 'QMK Bot'
        git config user.email '<EMAIL>'

    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v7
      if: ${{ github.repository == 'qmk/qmk_firmware'}}
      with:
        token: ${{ secrets.QMK_BOT_TOKEN }}
        delete-branch: true
        branch: bugfix/regen_${{ env.GITHUB_REF_SLUG }}
        author: <PERSON><PERSON><PERSON> <<EMAIL>>
        committer: QM<PERSON> <<EMAIL>>
        commit-message: Regenerate Files
        title: '[CI] Regenerate Files'
