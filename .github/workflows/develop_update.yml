name: Update develop after master merge

permissions:
  contents: write

on:
  push:
    branches:
    - master

jobs:
  develop_update:
    runs-on: ubuntu-latest

    if: github.repository == 'qmk/qmk_firmware'

    steps:
    - uses: actions/checkout@v4
      with:
        token: ${{ secrets.QMK_BOT_TOKEN }}
        fetch-depth: 0

    - name: Disable automatic eol conversion
      run: |
        echo "* -text" > .git/info/attributes

    - name: Checkout develop
      run: |
        git fetch origin master develop
        git checkout develop

    - name: Update develop from master
      run: |
        git config --global user.name "QMK Bot"
        git config --global user.email "<EMAIL>"
        git merge origin/master
        git push origin develop
