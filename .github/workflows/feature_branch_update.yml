name: Update feature branches after develop merge

permissions:
  contents: write

on:
  push:
    branches:
    - develop

jobs:
  feature_branch_update:
    runs-on: ubuntu-latest

    if: github.repository == 'qmk/qmk_firmware'

    strategy:
      matrix:
        branch:
        - xap
        - riot

    steps:
    - uses: actions/checkout@v4
      with:
        token: ${{ secrets.QMK_BOT_TOKEN }}
        fetch-depth: 0

    - name: Disable automatic eol conversion
      run: |
        echo "* -text" > .git/info/attributes

    - name: Checkout branch
      run: |
        git fetch origin develop ${{ matrix.branch }}
        git checkout ${{ matrix.branch }}

    - name: Update branch from develop
      run: |
        git config --global user.name "QMK Bot"
        git config --global user.email "<EMAIL>"
        git merge origin/develop
        git push origin ${{ matrix.branch }}
