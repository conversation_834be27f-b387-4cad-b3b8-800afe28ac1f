name: CLI CI

permissions:
  contents: read

on:
  push:
    branches:
    - master
    - develop
  pull_request:
    paths:
    - 'lib/python/**'
    - 'requirements.txt'
    - '.github/workflows/cli.yml'

jobs:
  test:
    runs-on: ubuntu-latest

    container: ghcr.io/qmk/qmk_cli

    steps:
    - name: Disable safe.directory check
      run : git config --global --add safe.directory '*'

    - uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Install dependencies
      run: pip3 install -r requirements-dev.txt
    - name: Run tests
      run: qmk pytest
