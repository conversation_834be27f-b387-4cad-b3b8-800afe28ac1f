name: Essential files modified

permissions:
  contents: write

on:
  workflow_dispatch:
  push:
    branches:
    - master
    paths:
    - builddefs/**/*
    - drivers/**/*
    - platforms/**/*
    - quantum/**/*
    - tests/**/*
    - tmk_core/**/*
    - lib/python/**/*
    - util/**/*
    - Makefile
    - '*.mk'

jobs:
  tag:
    runs-on: ubuntu-latest

    # protect against those who develop with their fork on master
    if: github.repository == 'qmk/qmk_firmware'

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Bump version and push tag
      uses: anothrNick/github-tag-action@1.66.0
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        DEFAULT_BUMP: 'patch'
