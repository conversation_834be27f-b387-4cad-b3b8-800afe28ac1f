/* Copyright 2021 <PERSON> (@tzarc)
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * ME<PERSON>HANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

#pragma once

typedef enum {
    USBPD_500MA,
    USBPD_1500MA,
    USBPD_3000MA,
} usbpd_allowance_t;

// Initialises the USBPD subsystem
void usbpd_init(void);

// Gets the current state of the USBPD allowance
usbpd_allowance_t usbpd_get_allowance(void);
