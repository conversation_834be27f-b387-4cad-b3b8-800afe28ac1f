// Copyright 2024 <PERSON> (@smbranam)
// SPDX-License-Identifier: GPL-2.0-or-later
#pragma once

#define SH1107_DISPLAY_ON 0xAF
#define SH1107_DISPLAY_OFF 0xAE
#define SH1107_SET_OSC_DIVFREQ 0xD5
#define SH1107_SET_MUX_RATIO 0xA8
#define SH1107_DISPLAY_OFFSET 0xD3
#define SH1107_SET_START_LINE 0xDC // Key/sole difference from SH1106 (which uses 0x40)
#define SH1107_SET_CHARGE_PUMP 0x8D
#define SH1107_SET_SEGMENT_REMAP_NORMAL 0xA0
#define SH1107_SET_SEGMENT_REMAP_INV 0xA1
#define SH1107_COM_SCAN_DIR_INC 0xC0
#define SH1107_COM_SCAN_DIR_DEC 0xC8
#define SH1107_COM_PADS_HW_CFG 0xDA
#define SH1107_SET_CONTRAST 0x81
#define SH1107_SET_PRECHARGE_PERIOD 0xD9
#define SH1107_VCOM_DESELECT_LEVEL 0xDB
#define SH1107_ALL_ON_RESUME 0xA4
#define SH1107_NON_INVERTING_DISPLAY 0xA6
#define SH1107_DEACTIVATE_SCROLL 0x2E
#define SH1107_SETCOLUMN_LSB 0x00
#define SH1107_SETCOLUMN_MSB 0x10
#define SH1107_PAGE_ADDR 0xB0
