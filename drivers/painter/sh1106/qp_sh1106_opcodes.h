// Copyright 2023 <PERSON> (@tzarc)
// SPDX-License-Identifier: GPL-2.0-or-later
#pragma once

#define SH1106_DISPLAY_ON 0xAF
#define SH1106_DISPLAY_OFF 0xAE
#define SH1106_SET_OSC_DIVFREQ 0xD5
#define SH1106_SET_MUX_RATIO 0xA8
#define SH1106_DISPLAY_OFFSET 0xD3
#define SH1106_DISPLAY_START_LINE 0x40
#define SH1106_SET_CHARGE_PUMP 0x8D
#define SH1106_SET_SEGMENT_REMAP_NORMAL 0xA0
#define SH1106_SET_SEGMENT_REMAP_INV 0xA1
#define SH1106_COM_SCAN_DIR_INC 0xC0
#define SH1106_COM_SCAN_DIR_DEC 0xC8
#define SH1106_COM_PADS_HW_CFG 0xDA
#define SH1106_SET_CONTRAST 0x81
#define SH1106_SET_PRECHARGE_PERIOD 0xD9
#define SH1106_VCOM_DESELECT_LEVEL 0xDB
#define SH1106_ALL_ON_RESUME 0xA4
#define SH1106_NON_INVERTING_DISPLAY 0xA6
#define SH1106_DEACTIVATE_SCROLL 0x2E

#define SH1106_SETCOLUMN_LSB 0x00
#define SH1106_SETCOLUMN_MSB 0x10
#define SH1106_PAGE_ADDR 0xB0
