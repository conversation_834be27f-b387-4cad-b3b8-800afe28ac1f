// Copyright 2023 <PERSON><PERSON> (@daskygit)
// SPDX-License-Identifier: GPL-2.0-or-later

typedef enum {
    LD7032_SOFTRES          = 0x01,
    LD7032_DISP_ON_OFF      = 0x02,
    LD7032_DATA_RW          = 0x08,
    LD7032_DISP_DIRECTION   = 0x09,
    LD7032_IFMODE           = 0x0D,
    LD7032_PEAK_WIDTH       = 0x10,
    LD7032_DOT_CURRENT      = 0x12,
    LD7032_SCAN_MODE        = 0x13,
    LD7032_DISP_STBY_ON_OFF = 0x14,
    LD7032_PEAK_DELAY       = 0x16,
    LD7032_ROW_SCAN         = 0x17,
    LD7032_PRE_C_WIDTH      = 0x18,
    LD7032_DFRAME           = 0x1A,
    LD7032_DATA_REVERSE     = 0x1C,
    LD7032_WRITE_DIRECTION  = 0x1D,
    LD7032_READREG          = 0x20,
    LD7032_DISP_SIZE_X      = 0x30,
    LD7032_DISP_SIZE_Y      = 0x32,
    LD7032_X_BOX_ADR_START  = 0x34,
    LD7032_X_BOX_ADR_END    = 0x35,
    LD7032_Y_BOX_ADR_START  = 0x36,
    LD7032_Y_BOX_ADR_END    = 0x37,
    LD7032_X_DISP_START     = 0x38,
    LD7032_Y_DISP_START     = 0x39,
    LD7032_XTALK_EN         = 0x3A,
    LD7032_XTALK_REF        = 0x3B,
    LD7032_AGING_EN         = 0x3C,
    LD7032_VDD_SEL          = 0x3D,
    LD7032_TESTCNT0         = 0x3E,
    LD7032_VCC_R_SEL        = 0x3F,
    LD7032_PRE_C_SELECT     = 0x44,
    LD7032_ROW_OVERLAP      = 0x48,
    LD7032_S_SLEEP_TIMER    = 0xC0,
    LD7032_S_SLEEP_START    = 0xC2,
    LD7032_S_STEP_TIMER     = 0xC3,
    LD7032_S_STEP_UNIT      = 0xC4,
    LD7032_S_CONDITION      = 0xCC,
    LD7032_S_START_STOP     = 0xCD,
    LD7032_S_SELECT         = 0xCE,
    LD7032_TESTCNT1         = 0xF0, //-0xFF
} ld7032_opcodes;
