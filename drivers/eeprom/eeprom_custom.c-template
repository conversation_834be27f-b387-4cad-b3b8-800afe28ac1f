/* Copyright 2019 <PERSON> (tzarc)
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

#include <stdint.h>
#include <string.h>

#include "eeprom_driver.h"

void eeprom_driver_init(void) {
    /* Any initialisation code */
 }

void eeprom_driver_format(bool erase) {
    /* If erase=false, then only do the absolute minimum initialisation necessary
       to make sure that the eeprom driver is usable. It doesn't need to guarantee
       that the content of the eeprom is reset to any particular value. For many
       eeprom drivers this may be a no-op.

       If erase=true, then in addition to making sure the eeprom driver is in a
       usable state, also make sure that it is erased.
     */
}

void eeprom_driver_erase(void) {
    /* Wipe out the EEPROM, setting values to zero */
}

void eeprom_read_block(void *buf, const void *addr, size_t len) {
    /*
        Read a block of data:
            buf: target buffer
            addr: 0-based offset within the EEPROM
            len: length to read
     */
}

void eeprom_write_block(const void *buf, void *addr, size_t len) {
    /*
        Write a block of data:
            buf: target buffer
            addr: 0-based offset within the EEPROM
            len: length to write
     */
}
