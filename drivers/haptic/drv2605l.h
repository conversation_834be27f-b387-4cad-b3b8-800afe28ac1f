/* Copyright 2018 ishtob
 * Driver for DRV2605L written for QMK
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

#pragma once

#include <stdint.h>

/* Initialization settings

 * Feedback Control Settings */
#ifndef DRV2605L_FB_ERM_LRA
#    define DRV2605L_FB_ERM_LRA 1 /* For ERM:0 or LRA:1*/
#endif
#ifndef DRV2605L_FB_BRAKEFACTOR
#    define DRV2605L_FB_BRAKEFACTOR 3 /* For 1x:0, 2x:1, 3x:2, 4x:3, 6x:4, 8x:5, 16x:6, Disable Braking:7 */
#endif
#ifndef DRV2605L_FB_LOOPGAIN
#    define DRV2605L_FB_LOOPGAIN 1 /* For  Low:0, Medium:1, High:2, Very High:3 */
#endif

/* LRA specific settings */
#if DRV2605L_FB_ERM_LRA == 1
#    ifndef DRV2605L_V_RMS
#        define DRV2605L_V_RMS 2.0
#    endif
#    ifndef DRV2605L_V_PEAK
#        define DRV2605L_V_PEAK 2.1
#    endif
#    ifndef DRV2605L_F_LRA
#        define DRV2605L_F_LRA 205
#    endif
#    ifndef DRV2605L_RATED_VOLTAGE
#        define DRV2605L_RATED_VOLTAGE 2 /* 2v as safe range in case device voltage is not set */
#    endif
#endif

#ifndef DRV2605L_RATED_VOLTAGE
#    define DRV2605L_RATED_VOLTAGE 2 /* 2v as safe range in case device voltage is not set */
#endif
#ifndef DRV2605L_V_PEAK
#    define DRV2605L_V_PEAK 2.8
#endif

/* Library Selection */
#ifndef DRV2605L_LIBRARY
#    if DRV2605L_FB_ERM_LRA == 1
#        define DRV2605L_LIBRARY 6 /* For Empty:0' TS2200 library A to D:1-5, LRA Library: 6 */
#    else
#        define DRV2605L_LIBRARY 1
#    endif
#endif

#ifndef DRV2605L_GREETING
#    define DRV2605L_GREETING DRV2605L_EFFECT_750_MS_ALERT_100
#endif
#ifndef DRV2605L_DEFAULT_MODE
#    define DRV2605L_DEFAULT_MODE DRV2605L_EFFECT_STRONG_CLICK_1_100
#endif

/* Control 1 register settings */
#ifndef DRV2605L_DRIVE_TIME
#    define DRV2605L_DRIVE_TIME 25
#endif
#ifndef DRV2605L_AC_COUPLE
#    define DRV2605L_AC_COUPLE 0
#endif
#ifndef DRV2605L_STARTUP_BOOST
#    define DRV2605L_STARTUP_BOOST 1
#endif

/* Control 2 Settings */
#ifndef DRV2605L_BIDIR_INPUT
#    define DRV2605L_BIDIR_INPUT 1
#endif
#ifndef DRV2605L_BRAKE_STAB
#    define DRV2605L_BRAKE_STAB 1 /* Loopgain is reduced when braking is almost complete to improve stability */
#endif
#ifndef DRV2605L_SAMPLE_TIME
#    define DRV2605L_SAMPLE_TIME 3
#endif
#ifndef DRV2605L_BLANKING_TIME
#    define DRV2605L_BLANKING_TIME 1
#endif
#ifndef DRV2605L_IDISS_TIME
#    define DRV2605L_IDISS_TIME 1
#endif

/* Control 3 settings */
#ifndef DRV2605L_NG_THRESH
#    define DRV2605L_NG_THRESH 2
#endif
#ifndef DRV2605L_ERM_OPEN_LOOP
#    define DRV2605L_ERM_OPEN_LOOP 1
#endif
#ifndef DRV2605L_SUPPLY_COMP_DIS
#    define DRV2605L_SUPPLY_COMP_DIS 0
#endif
#ifndef DRV2605L_DATA_FORMAT_RTO
#    define DRV2605L_DATA_FORMAT_RTO 0
#endif
#ifndef DRV2605L_LRA_DRIVE_MODE
#    define DRV2605L_LRA_DRIVE_MODE 0
#endif
#ifndef DRV2605L_N_PWM_ANALOG
#    define DRV2605L_N_PWM_ANALOG 0
#endif
#ifndef DRV2605L_LRA_OPEN_LOOP
#    define DRV2605L_LRA_OPEN_LOOP 0
#endif

/* Control 4 settings */
#ifndef DRV2605L_ZC_DET_TIME
#    define DRV2605L_ZC_DET_TIME 0
#endif
#ifndef DRV2605L_AUTO_CAL_TIME
#    define DRV2605L_AUTO_CAL_TIME 3
#endif

#define DRV2605L_I2C_ADDRESS 0x5A

#define DRV2605L_REG_STATUS 0x00
#define DRV2605L_REG_MODE 0x01
#define DRV2605L_REG_RTP_INPUT 0x02
#define DRV2605L_REG_LIBRARY_SELECTION 0x03
#define DRV2605L_REG_WAVEFORM_SEQUENCER_1 0x04
#define DRV2605L_REG_WAVEFORM_SEQUENCER_2 0x05
#define DRV2605L_REG_WAVEFORM_SEQUENCER_3 0x06
#define DRV2605L_REG_WAVEFORM_SEQUENCER_4 0x07
#define DRV2605L_REG_WAVEFORM_SEQUENCER_5 0x08
#define DRV2605L_REG_WAVEFORM_SEQUENCER_6 0x09
#define DRV2605L_REG_WAVEFORM_SEQUENCER_7 0x0A
#define DRV2605L_REG_WAVEFORM_SEQUENCER_8 0x0B
#define DRV2605L_REG_GO 0x0C
#define DRV2605L_REG_OVERDRIVE_TIME_OFFSET 0x0D
#define DRV2605L_REG_SUSTAIN_TIME_OFFSET_P 0x0E
#define DRV2605L_REG_SUSTAIN_TIME_OFFSET_N 0x0F
#define DRV2605L_REG_BRAKE_TIME_OFFSET 0x10
#define DRV2605L_REG_AUDIO_TO_VIBE_CTRL 0x11
#define DRV2605L_REG_AUDIO_TO_VIBE_MIN_INPUT 0x12
#define DRV2605L_REG_AUDIO_TO_VIBE_MAX_INPUT 0x13
#define DRV2605L_REG_AUDIO_TO_VIBE_MIN_OUTPUT_DRIVE 0x14
#define DRV2605L_REG_AUDIO_TO_VIBE_MAX_OUTPUT_DRIVE 0x15
#define DRV2605L_REG_RATED_VOLTAGE 0x16
#define DRV2605L_REG_OVERDRIVE_CLAMP_VOLTAGE 0x17
#define DRV2605L_REG_AUTO_CALIBRATION_COMPENSATION_RESULT 0x18
#define DRV2605L_REG_AUTO_CALIBRATION_BACK_EMF_RESULT 0x19
#define DRV2605L_REG_FEEDBACK_CTRL 0x1A
#define DRV2605L_REG_CTRL1 0x1B
#define DRV2605L_REG_CTRL2 0x1C
#define DRV2605L_REG_CTRL3 0x1D
#define DRV2605L_REG_CTRL4 0x1E
#define DRV2605L_REG_CTRL5 0x1F
#define DRV2605L_REG_LRA_OPEN_LOOP_PERIOD 0x20
#define DRV2605L_REG_VBAT_VOLTAGE_MONITOR 0x21
#define DRV2605L_REG_LRA_RESONANCE_PERIOD 0x22

void    drv2605l_init(void);
void    drv2605l_write(const uint8_t reg_addr, const uint8_t data);
uint8_t drv2605l_read(const uint8_t reg_addr);
void    drv2605l_rtp_init(void);
void    drv2605l_amplitude(const uint8_t amplitude);
void    drv2605l_pulse(const uint8_t sequence);

typedef enum drv2605l_effect_t {
    DRV2605L_EFFECT_CLEAR_SEQUENCE,
    DRV2605L_EFFECT_STRONG_CLICK_100,
    DRV2605L_EFFECT_STRONG_CLICK_60,
    DRV2605L_EFFECT_STRONG_CLICK_30,
    DRV2605L_EFFECT_SHARP_CLICK_100,
    DRV2605L_EFFECT_SHARP_CLICK_60,
    DRV2605L_EFFECT_SHARP_CLICK_30,
    DRV2605L_EFFECT_SOFT_BUMP_100,
    DRV2605L_EFFECT_SOFT_BUMP_60,
    DRV2605L_EFFECT_SOFT_BUMP_30,
    DRV2605L_EFFECT_DOUBLE_CLICK_100,
    DRV2605L_EFFECT_DOUBLE_CLICK_60,
    DRV2605L_EFFECT_TRIPLE_CLICK_100,
    DRV2605L_EFFECT_SOFT_FUZZ_60,
    DRV2605L_EFFECT_STRONG_BUZZ_100,
    DRV2605L_EFFECT_750_MS_ALERT_100,
    DRV2605L_EFFECT_1000_MS_ALERT_100,
    DRV2605L_EFFECT_STRONG_CLICK_1_100,
    DRV2605L_EFFECT_STRONG_CLICK_2_80,
    DRV2605L_EFFECT_STRONG_CLICK_3_60,
    DRV2605L_EFFECT_STRONG_CLICK_4_30,
    DRV2605L_EFFECT_MEDIUM_CLICK_1_100,
    DRV2605L_EFFECT_MEDIUM_CLICK_2_80,
    DRV2605L_EFFECT_MEDIUM_CLICK_3_60,
    DRV2605L_EFFECT_SHARP_TICK_1_100,
    DRV2605L_EFFECT_SHARP_TICK_2_80,
    DRV2605L_EFFECT_SHARP_TICK_3_60,
    DRV2605L_EFFECT_SHORT_DOUBLE_CLICK_STRONG_1_100,
    DRV2605L_EFFECT_SHORT_DOUBLE_CLICK_STRONG_2_80,
    DRV2605L_EFFECT_SHORT_DOUBLE_CLICK_STRONG_3_60,
    DRV2605L_EFFECT_SHORT_DOUBLE_CLICK_STRONG_4_30,
    DRV2605L_EFFECT_SHORT_DOUBLE_CLICK_MEDIUM_1_100,
    DRV2605L_EFFECT_SHORT_DOUBLE_CLICK_MEDIUM_2_80,
    DRV2605L_EFFECT_SHORT_DOUBLE_CLICK_MEDIUM_3_60,
    DRV2605L_EFFECT_SHORT_DOUBLE_SHARP_TICK_1_100,
    DRV2605L_EFFECT_SHORT_DOUBLE_SHARP_TICK_2_80,
    DRV2605L_EFFECT_SHORT_DOUBLE_SHARP_TICK_3_60,
    DRV2605L_EFFECT_LONG_DOUBLE_SHARP_CLICK_STRONG_1_100,
    DRV2605L_EFFECT_LONG_DOUBLE_SHARP_CLICK_STRONG_2_80,
    DRV2605L_EFFECT_LONG_DOUBLE_SHARP_CLICK_STRONG_3_60,
    DRV2605L_EFFECT_LONG_DOUBLE_SHARP_CLICK_STRONG_4_30,
    DRV2605L_EFFECT_LONG_DOUBLE_SHARP_CLICK_MEDIUM_1_100,
    DRV2605L_EFFECT_LONG_DOUBLE_SHARP_CLICK_MEDIUM_2_80,
    DRV2605L_EFFECT_LONG_DOUBLE_SHARP_CLICK_MEDIUM_3_60,
    DRV2605L_EFFECT_LONG_DOUBLE_SHARP_TICK_1_100,
    DRV2605L_EFFECT_LONG_DOUBLE_SHARP_TICK_2_80,
    DRV2605L_EFFECT_LONG_DOUBLE_SHARP_TICK_3_60,
    DRV2605L_EFFECT_BUZZ_1_100,
    DRV2605L_EFFECT_BUZZ_2_80,
    DRV2605L_EFFECT_BUZZ_3_60,
    DRV2605L_EFFECT_BUZZ_4_40,
    DRV2605L_EFFECT_BUZZ_5_20,
    DRV2605L_EFFECT_PULSING_STRONG_1_100,
    DRV2605L_EFFECT_PULSING_STRONG_2_60,
    DRV2605L_EFFECT_PULSING_MEDIUM_1_100,
    DRV2605L_EFFECT_PULSING_MEDIUM_2_60,
    DRV2605L_EFFECT_PULSING_SHARP_1_100,
    DRV2605L_EFFECT_PULSING_SHARP_2_60,
    DRV2605L_EFFECT_TRANSITION_CLICK_1_100,
    DRV2605L_EFFECT_TRANSITION_CLICK_2_80,
    DRV2605L_EFFECT_TRANSITION_CLICK_3_60,
    DRV2605L_EFFECT_TRANSITION_CLICK_4_40,
    DRV2605L_EFFECT_TRANSITION_CLICK_5_20,
    DRV2605L_EFFECT_TRANSITION_CLICK_6_10,
    DRV2605L_EFFECT_TRANSITION_HUM_1_100,
    DRV2605L_EFFECT_TRANSITION_HUM_2_80,
    DRV2605L_EFFECT_TRANSITION_HUM_3_60,
    DRV2605L_EFFECT_TRANSITION_HUM_4_40,
    DRV2605L_EFFECT_TRANSITION_HUM_5_20,
    DRV2605L_EFFECT_TRANSITION_HUM_6_10,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_LONG_SMOOTH_1_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_LONG_SMOOTH_2_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_MEDIUM_SMOOTH_1_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_MEDIUM_SMOOTH_2_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_SHORT_SMOOTH_1_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_SHORT_SMOOTH_2_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_LONG_SHARP_1_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_LONG_SHARP_2_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_MEDIUM_SHARP_1_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_MEDIUM_SHARP_2_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_SHORT_SHARP_1_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_SHORT_SHARP_2_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_LONG_SMOOTH_1_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_LONG_SMOOTH_2_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_MEDIUM_SMOOTH_1_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_MEDIUM_SMOOTH_2_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_SHORT_SMOOTH_1_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_SHORT_SMOOTH_2_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_LONG_SHARP_1_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_LONG_SHARP_2_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_MEDIUM_SHARP_1_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_MEDIUM_SHARP_2_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_SHORT_SHARP_1_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_SHORT_SHARP_2_100,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_LONG_SMOOTH_1_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_LONG_SMOOTH_2_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_MEDIUM_SMOOTH_1_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_MEDIUM_SMOOTH_2_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_SHORT_SMOOTH_1_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_SHORT_SMOOTH_2_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_LONG_SHARP_1_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_LONG_SHARP_2_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_MEDIUM_SHARP_1_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_MEDIUM_SHARP_2_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_SHORT_SHARP_1_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_DOWN_SHORT_SHARP_2_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_LONG_SMOOTH_1_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_LONG_SMOOTH_2_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_MEDIUM_SMOOTH_1_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_MEDIUM_SMOOTH_2_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_SHORT_SMOOTH_1_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_SHORT_SMOOTH_2_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_LONG_SHARP_1_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_LONG_SHARP_2_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_MEDIUM_SHARP_1_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_MEDIUM_SHARP_2_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_SHORT_SHARP_1_50,
    DRV2605L_EFFECT_TRANSITION_RAMP_UP_SHORT_SHARP_2_50,
    DRV2605L_EFFECT_LONG_BUZZ_FOR_PROGRAMMATIC_STOPPING,
    DRV2605L_EFFECT_SMOOTH_HUM_1_50,
    DRV2605L_EFFECT_SMOOTH_HUM_2_40,
    DRV2605L_EFFECT_SMOOTH_HUM_3_30,
    DRV2605L_EFFECT_SMOOTH_HUM_4_20,
    DRV2605L_EFFECT_SMOOTH_HUM_5_10,
    DRV2605L_EFFECT_COUNT
} drv2605l_effect_t;

/* Register bit array unions */

typedef union { /* register 0x1A */
    uint8_t raw;
    struct {
        uint8_t BEMF_GAIN : 2;
        uint8_t LOOP_GAIN : 2;
        uint8_t BRAKE_FACTOR : 3;
        uint8_t ERM_LRA : 1;
    } bits;
} drv2605l_reg_feedback_ctrl_t;

typedef union { /* register 0x1B */
    uint8_t raw;
    struct {
        uint8_t C1_DRIVE_TIME : 5;
        uint8_t C1_AC_COUPLE : 1;
        uint8_t : 1;
        uint8_t C1_STARTUP_BOOST : 1;
    } bits;
} drv2605l_reg_ctrl1_t;

typedef union { /* register 0x1C */
    uint8_t raw;
    struct {
        uint8_t C2_IDISS_TIME : 2;
        uint8_t C2_BLANKING_TIME : 2;
        uint8_t C2_SAMPLE_TIME : 2;
        uint8_t C2_BRAKE_STAB : 1;
        uint8_t C2_BIDIR_INPUT : 1;
    } bits;
} drv2605l_reg_ctrl2_t;

typedef union { /* register 0x1D */
    uint8_t raw;
    struct {
        uint8_t C3_LRA_OPEN_LOOP : 1;
        uint8_t C3_N_PWM_ANALOG : 1;
        uint8_t C3_LRA_DRIVE_MODE : 1;
        uint8_t C3_DATA_FORMAT_RTO : 1;
        uint8_t C3_SUPPLY_COMP_DIS : 1;
        uint8_t C3_ERM_OPEN_LOOP : 1;
        uint8_t C3_NG_THRESH : 2;
    } bits;
} drv2605l_reg_ctrl3_t;

typedef union { /* register 0x1E */
    uint8_t raw;
    struct {
        uint8_t C4_OTP_PROGRAM : 1;
        uint8_t : 1;
        uint8_t C4_OTP_STATUS : 1;
        uint8_t : 1;
        uint8_t C4_AUTO_CAL_TIME : 2;
        uint8_t C4_ZC_DET_TIME : 2;
    } bits;
} drv2605l_reg_ctrl4_t;
