{
    "ranges": {
        "0x0000/0x00FF": {
            "define": "QK_BASIC"
        },
        "0x0100/0x1EFF": {
            "define": "QK_MODS"
        },
        "0x2000/0x1FFF": {
            "define": "QK_MOD_TAP"
        },
        "0x4000/0x0FFF": {
            "define": "QK_LAYER_TAP"
        },
        "0x5000/0x01FF": {
            "define": "QK_LAYER_MOD"
        },
        "0x5200/0x001F": {
            "define": "QK_TO"
        },
        "0x5220/0x001F": {
            "define": "QK_MOMENTARY"
        },
        "0x5240/0x001F": {
            "define": "QK_DEF_LAYER"
        },
        "0x5260/0x001F": {
            "define": "QK_TOGGLE_LAYER"
        },
        "0x5280/0x001F": {
            "define": "QK_ONE_SHOT_LAYER"
        },
        "0x52A0/0x001F": {
            "define": "QK_ONE_SHOT_MOD"
        },
        "0x52C0/0x001F": {
            "define": "QK_LAYER_TAP_TOGGLE"
        },
        // 0x52E0/0x001F - UNUSED
        // 0x5300/0x02FF - UNUSED
        "0x5600/0x00FF": {
            "define": "QK_SWAP_HANDS"
        },
        "0x5700/0x00FF": {
            "define": "QK_TAP_DANCE"
        },
        // 0x5800/0x17FF - UNUSED
        "0x7000/0x00FF": {
            "define": "QK_MAGIC"
        },
        "0x7100/0x00FF": {
            "define": "QK_MIDI"
        },
        "0x7200/0x01FF": {
            "define": "QK_SEQUENCER"
        },
        "0x7400/0x003F": {
            "define": "QK_JOYSTICK"
        },
        "0x7440/0x003F": {
            "define": "QK_PROGRAMMABLE_BUTTON"
        },
        "0x7480/0x003F": {
            "define": "QK_AUDIO"
        },
        "0x74C0/0x003F": {
            "define": "QK_STENO"
        },
        // 0x7500/0x01FF - UNUSED
        "0x7700/0x007F": {
            "define": "QK_MACRO"
        },
        // 0x7780/0x007F - UNUSED
        "0x7800/0x00FF": {
            "define": "QK_LIGHTING"
        },
        // 0x7900/0x02FF - UNUSED
        "0x7C00/0x01FF": {
            "define": "QK_QUANTUM"
        },
        "0x7E00/0x00FF": {
            "define": "QK_KB"
        },
        "0x7F00/0x00FF": {
            "define": "QK_USER"
        },
        "0x8000/0x7FFF": {
            "define": "QK_UNICODE"
        }
    },
    "keycodes": {
        "0x7E00": {
            "key": "SAFE_RANGE"
        }
    }
}
