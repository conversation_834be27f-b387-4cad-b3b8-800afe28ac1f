{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ $ │ " │ « │ » │ ( │ ) │ @ │ + │ - │ / │ * │ = │ % │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ B │ É │ P │ O │ È │ ^ │ V │ D │ L │ J │ Z │ W │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ U │ I │ E │ , │ C │ T │ S │ R │ N │ M │ Ç │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ Ê │ À │ Y │ X │ . │ K │ ' │ Q │ G │ H │ F │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "BP_DLR",
            "label": "$",
        }
        "KC_1": {
            "key": "BP_DQUO",
            "label": "\"",
        }
        "KC_2": {
            "key": "BP_LDAQ",
            "label": "«",
        }
        "KC_3": {
            "key": "BP_RDAQ",
            "label": "»",
        }
        "KC_4": {
            "key": "BP_LPRN",
            "label": "(",
        }
        "KC_5": {
            "key": "BP_RPRN",
            "label": ")",
        }
        "KC_6": {
            "key": "BP_AT",
            "label": "@",
        }
        "KC_7": {
            "key": "BP_PLUS",
            "label": "+",
        }
        "KC_8": {
            "key": "BP_MINS",
            "label": "-",
        }
        "KC_9": {
            "key": "BP_SLSH",
            "label": "/",
        }
        "KC_0": {
            "key": "BP_ASTR",
            "label": "*",
        }
        "KC_MINS": {
            "key": "BP_EQL",
            "label": "=",
        }
        "KC_EQL": {
            "key": "BP_PERC",
            "label": "%",
        }
        "KC_Q": {
            "key": "BP_B",
            "label": "B",
        }
        "KC_W": {
            "key": "BP_EACU",
            "label": "É",
        }
        "KC_E": {
            "key": "BP_P",
            "label": "P",
        }
        "KC_R": {
            "key": "BP_O",
            "label": "O",
        }
        "KC_T": {
            "key": "BP_EGRV",
            "label": "È",
        }
        "KC_Y": {
            "key": "BP_DCIR",
            "label": "^ (dead)",
        }
        "KC_U": {
            "key": "BP_V",
            "label": "V",
        }
        "KC_I": {
            "key": "BP_D",
            "label": "D",
        }
        "KC_O": {
            "key": "BP_L",
            "label": "L",
        }
        "KC_P": {
            "key": "BP_J",
            "label": "J",
        }
        "KC_LBRC": {
            "key": "BP_Z",
            "label": "Z",
        }
        "KC_RBRC": {
            "key": "BP_W",
            "label": "W",
        }
        "KC_A": {
            "key": "BP_A",
            "label": "A",
        }
        "KC_S": {
            "key": "BP_U",
            "label": "U",
        }
        "KC_D": {
            "key": "BP_I",
            "label": "I",
        }
        "KC_F": {
            "key": "BP_E",
            "label": "E",
        }
        "KC_G": {
            "key": "BP_COMM",
            "label": ",",
        }
        "KC_H": {
            "key": "BP_C",
            "label": "C",
        }
        "KC_J": {
            "key": "BP_T",
            "label": "T",
        }
        "KC_K": {
            "key": "BP_S",
            "label": "S",
        }
        "KC_L": {
            "key": "BP_R",
            "label": "R",
        }
        "KC_SCLN": {
            "key": "BP_N",
            "label": "N",
        }
        "KC_QUOT": {
            "key": "BP_M",
            "label": "M",
        }
        "KC_BSLS": {
            "key": "BP_CCED",
            "label": "Ç",
        }
        "KC_NUBS": {
            "key": "BP_ECIR",
            "label": "Ê",
        }
        "KC_Z": {
            "key": "BP_AGRV",
            "label": "À",
        }
        "KC_X": {
            "key": "BP_Y",
            "label": "Y",
        }
        "KC_C": {
            "key": "BP_X",
            "label": "X",
        }
        "KC_V": {
            "key": "BP_DOT",
            "label": ".",
        }
        "KC_B": {
            "key": "BP_K",
            "label": "K",
        }
        "KC_N": {
            "key": "BP_QUOT",
            "label": "'",
        }
        "KC_M": {
            "key": "BP_Q",
            "label": "Q",
        }
        "KC_COMM": {
            "key": "BP_G",
            "label": "G",
        }
        "KC_DOT": {
            "key": "BP_H",
            "label": "H",
        }
        "KC_SLSH": {
            "key": "BP_F",
            "label": "F",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ # │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ ° │ ` │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │ ! │   │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │ ; │   │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │ : │   │ ? │   │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(BP_DLR)": {
            "key": "BP_HASH",
            "label": "#",
        }
        "S(BP_DQUO)": {
            "key": "BP_1",
            "label": "1",
        }
        "S(BP_LDAQ)": {
            "key": "BP_2",
            "label": "2",
        }
        "S(BP_RDAQ)": {
            "key": "BP_3",
            "label": "3",
        }
        "S(BP_LPRN)": {
            "key": "BP_4",
            "label": "4",
        }
        "S(BP_RPRN)": {
            "key": "BP_5",
            "label": "5",
        }
        "S(BP_AT)": {
            "key": "BP_6",
            "label": "6",
        }
        "S(BP_PLUS)": {
            "key": "BP_7",
            "label": "7",
        }
        "S(BP_MINS)": {
            "key": "BP_8",
            "label": "8",
        }
        "S(BP_SLSH)": {
            "key": "BP_9",
            "label": "9",
        }
        "S(BP_ASTR)": {
            "key": "BP_0",
            "label": "0",
        }
        "S(BP_EQL)": {
            "key": "BP_DEG",
            "label": "°",
        }
        "S(BP_PERC)": {
            "key": "BP_GRV",
            "label": "`",
        }
        "S(BP_DCIR)": {
            "key": "BP_EXLM",
            "label": "!",
        }
        "S(BP_COMM)": {
            "key": "BP_SCLN",
            "label": ";",
        }
        "S(BP_DOT)": {
            "key": "BP_COLN",
            "label": ":",
        }
        "S(BP_QUOT)": {
            "key": "BP_QUES",
            "label": "?",
        }
        "S(KC_SPC)": {
            "key": "BP_NBSP",
            "label": "(non-breaking space)",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ – │ — │ < │ > │ [ │ ] │ ^ │ ± │ − │ ÷ │ × │ ≠ │ ‰ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ | │ ´ │ & │ Œ │ ` │ ¡ │ ˇ │ Ð │ / │ Ĳ │ Ə │ ˘ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ Æ │ Ù │ ¨ │ € │   │ © │ Þ │ ẞ │ ® │ ~ │ ¯ │ ¸ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │ \ │ { │ } │ … │ ~ │ ¿ │ ° │   │ † │ ˛ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │           _            │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(BP_DLR)": {
            "key": "BP_NDSH",
            "label": "–",
        }
        "ALGR(BP_DQUO)": {
            "key": "BP_MDSH",
            "label": "—",
        }
        "ALGR(BP_LDAQ)": {
            "key": "BP_LABK",
            "label": "<",
        }
        "ALGR(BP_RDAQ)": {
            "key": "BP_RABK",
            "label": ">",
        }
        "ALGR(BP_LPRN)": {
            "key": "BP_LBRC",
            "label": "[",
        }
        "ALGR(BP_RPRN)": {
            "key": "BP_RBRC",
            "label": "]",
        }
        "ALGR(BP_AT)": {
            "key": "BP_CIRC",
            "label": "^",
        }
        "ALGR(BP_PLUS)": {
            "key": "BP_PLMN",
            "label": "±",
        }
        "ALGR(BP_MINS)": {
            "key": "BP_MMNS",
            "label": "−",
        }
        "ALGR(BP_SLSH)": {
            "key": "BP_DIV",
            "label": "÷",
        }
        "ALGR(BP_ASTR)": {
            "key": "BP_MUL",
            "label": "×",
        }
        "ALGR(BP_EQL)": {
            "key": "BP_NEQL",
            "label": "≠",
        }
        "ALGR(BP_PERC)": {
            "key": "BP_PERM",
            "label": "‰",
        }
        "ALGR(BP_B)": {
            "key": "BP_PIPE",
            "label": "|",
        }
        "ALGR(BP_EACU)": {
            "key": "BP_ACUT",
            "label": "´ (dead)",
        }
        "ALGR(BP_P)": {
            "key": "BP_AMPR",
            "label": "&",
        }
        "ALGR(BP_O)": {
            "key": "BP_OE",
            "label": "Œ",
        }
        "ALGR(BP_EGRV)": {
            "key": "BP_DGRV",
            "label": "` (dead)",
        }
        "ALGR(BP_DCIR)": {
            "key": "BP_IEXL",
            "label": "¡",
        }
        "ALGR(BP_V)": {
            "key": "BP_CARN",
            "label": "ˇ (dead)",
        }
        "ALGR(BP_D)": {
            "key": "BP_ETH",
            "label": "Ð",
        }
        "ALGR(BP_L)": {
            "key": "BP_DSLS",
            "label": "/ (dead)",
        }
        "ALGR(BP_J)": {
            "key": "BP_IJ",
            "label": "Ĳ",
        }
        "ALGR(BP_Z)": {
            "key": "BP_SCHW",
            "label": "Ə",
        }
        "ALGR(BP_W)": {
            "key": "BP_BREV",
            "label": "˘ (dead)",
        }
        "ALGR(BP_A)": {
            "key": "BP_AE",
            "label": "Æ",
        }
        "ALGR(BP_U)": {
            "key": "BP_UGRV",
            "label": "Ù",
        }
        "ALGR(BP_I)": {
            "key": "BP_DIAE",
            "label": "¨ (dead)",
        }
        "ALGR(BP_E)": {
            "key": "BP_EURO",
            "label": "€",
        }
        "ALGR(BP_C)": {
            "key": "BP_COPY",
            "label": "©",
        }
        "ALGR(BP_T)": {
            "key": "BP_THRN",
            "label": "Þ",
        }
        "ALGR(BP_S)": {
            "key": "BP_SS",
            "label": "ẞ",
        }
        "ALGR(BP_R)": {
            "key": "BP_REGD",
            "label": "®",
        }
        "ALGR(BP_N)": {
            "key": "BP_DTIL",
            "label": "~ (dead)",
        }
        "ALGR(BP_M)": {
            "key": "BP_MACR",
            "label": "¯ (dead)",
        }
        "ALGR(BP_CCED)": {
            "key": "BP_CEDL",
            "label": "¸ (dead)",
        }
        "ALGR(BP_AGRV)": {
            "key": "BP_BSLS",
            "label": "\\",
        }
        "ALGR(BP_Y)": {
            "key": "BP_LCBR",
            "label": "{",
        }
        "ALGR(BP_X)": {
            "key": "BP_RCBR",
            "label": "}",
        }
        "ALGR(BP_DOT)": {
            "key": "BP_ELLP",
            "label": "…",
        }
        "ALGR(BP_K)": {
            "key": "BP_TILD",
            "label": "~",
        }
        "ALGR(BP_QUES)": {
            "key": "BP_IQUE",
            "label": "¿",
        }
        "ALGR(BP_Q)": {
            "key": "BP_RNGA",
            "label": "° (dead)",
        }
        "ALGR(BP_G)": {
            "key": "BP_DGRK",
            "label": "µ (dead Greek key)",
        }
        "ALGR(BP_H)": {
            "key": "BP_DAGG",
            "label": "†",
        }
        "ALGR(BP_F)": {
            "key": "BP_OGON",
            "label": "˛ (dead)",
        }
        "ALGR(KC_SPC)": {
            "key": "BP_UNDS",
            "label": "_",
        }
/* Shift+AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ¶ │ „ │ “ │ ” │ ≤ │ ≥ │   │ ¬ │ ¼ │ ½ │ ¾ │ ′ │ ″ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ ¦ │ ˝ │ § │   │   │   │   │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │ ˙ │ ¤ │ ̛  │ ſ │   │   │ ™ │   │ º │ , │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │ ‘ │ ’ │ · │ ⌨ │ ̉  │ ̣  │   │ ‡ │ ª │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(ALGR(BP_DLR))": {
            "key": "BP_PARA",
            "label": "¶",
        }
        "S(ALGR(BP_DQUO))": {
            "key": "BP_DLQU",
            "label": "„",
        }
        "S(ALGR(BP_LDAQ))": {
            "key": "BP_LDQU",
            "label": "“",
        }
        "S(ALGR(BP_RDAQ))": {
            "key": "BP_RDQU",
            "label": "”",
        }
        "S(ALGR(BP_LPRN))": {
            "key": "BP_LEQL",
            "label": "≤",
        }
        "S(ALGR(BP_RPRN))": {
            "key": "BP_GEQL",
            "label": "≥",
        }
        "S(ALGR(BP_PLUS))": {
            "key": "BP_NOT",
            "label": "¬",
        }
        "S(ALGR(BP_MINS))": {
            "key": "BP_QRTR",
            "label": "¼",
        }
        "S(ALGR(BP_SLSH))": {
            "key": "BP_HALF",
            "label": "½",
        }
        "S(ALGR(BP_ASTR))": {
            "key": "BP_TQTR",
            "label": "¾",
        }
        "S(ALGR(BP_EQL))": {
            "key": "BP_PRIM",
            "label": "′",
        }
        "S(ALGR(BP_PERC))": {
            "key": "BP_DPRM",
            "label": "″",
        }
        "S(ALGR(BP_B))": {
            "key": "BP_BRKP",
            "label": "¦",
        }
        "S(ALGR(BP_EACU))": {
            "key": "BP_DACU",
            "label": "˝ (dead)",
        }
        "S(ALGR(BP_P))": {
            "key": "BP_SECT",
            "label": "§",
        }
        "S(ALGR(BP_I))": {
            "key": "BP_DOTA",
            "label": "˙ (dead)",
        }
        "S(ALGR(BP_E))": {
            "key": "BP_CURR",
            "label": "¤ (dead)",
        }
        "S(ALGR(BP_COMM))": {
            "key": "BP_HORN",
            "label": "̛ (dead)",
        }
        "S(ALGR(BP_C))": {
            "key": "BP_LNGS",
            "label": "ſ",
        }
        "S(ALGR(BP_R))": {
            "key": "BP_TM",
            "label": "™",
        }
        "S(ALGR(BP_M))": {
            "key": "BP_MORD",
            "label": "º",
        }
        "S(ALGR(BP_CCED))": {
            "key": "BP_DCMM",
            "label": ", (dead)",
        }
        "S(ALGR(BP_Y))": {
            "key": "BP_LSQU",
            "label": "‘",
        }
        "S(ALGR(BP_X))": {
            "key": "BP_RSQU",
            "label": "’",
        }
        "S(ALGR(BP_DOT))": {
            "key": "BP_MDDT",
            "label": "·",
        }
        "S(ALGR(BP_K))": {
            "key": "BP_KEYB",
            "label": "⌨",
        }
        "S(ALGR(BP_QUOT))": {
            "key": "BP_HOKA",
            "label": "̉ (dead)",
        }
        "S(ALGR(BP_Q))": {
            "key": "BP_DOTB",
            "label": "̣ (dead)",
        }
        "S(ALGR(BP_H))": {
            "key": "BP_DDAG",
            "label": "‡",
        }
        "S(ALGR(BP_F))": {
            "key": "BP_FORD",
            "label": "ª",
        }
        "S(ALGR(KC_SPC))": {
            "key": "BP_NNBS",
            "label": "(narrow non-breaking space)",
        }
    }
}
