{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ½ │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ + │ ´ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ Å │ ¨ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ Æ │ Ø │ ' │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ - │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "DK_HALF",
            "label": "½",
        }
        "KC_1": {
            "key": "DK_1",
            "label": "1",
        }
        "KC_2": {
            "key": "DK_2",
            "label": "2",
        }
        "KC_3": {
            "key": "DK_3",
            "label": "3",
        }
        "KC_4": {
            "key": "DK_4",
            "label": "4",
        }
        "KC_5": {
            "key": "DK_5",
            "label": "5",
        }
        "KC_6": {
            "key": "DK_6",
            "label": "6",
        }
        "KC_7": {
            "key": "DK_7",
            "label": "7",
        }
        "KC_8": {
            "key": "DK_8",
            "label": "8",
        }
        "KC_9": {
            "key": "DK_9",
            "label": "9",
        }
        "KC_0": {
            "key": "DK_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "DK_PLUS",
            "label": "+",
        }
        "KC_EQL": {
            "key": "DK_ACUT",
            "label": "´ (dead)",
        }
        "KC_Q": {
            "key": "DK_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "DK_W",
            "label": "W",
        }
        "KC_E": {
            "key": "DK_E",
            "label": "E",
        }
        "KC_R": {
            "key": "DK_R",
            "label": "R",
        }
        "KC_T": {
            "key": "DK_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "DK_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "DK_U",
            "label": "U",
        }
        "KC_I": {
            "key": "DK_I",
            "label": "I",
        }
        "KC_O": {
            "key": "DK_O",
            "label": "O",
        }
        "KC_P": {
            "key": "DK_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "DK_ARNG",
            "label": "Å",
        }
        "KC_RBRC": {
            "key": "DK_DIAE",
            "label": "¨ (dead)",
        }
        "KC_A": {
            "key": "DK_A",
            "label": "A",
        }
        "KC_S": {
            "key": "DK_S",
            "label": "S",
        }
        "KC_D": {
            "key": "DK_D",
            "label": "D",
        }
        "KC_F": {
            "key": "DK_F",
            "label": "F",
        }
        "KC_G": {
            "key": "DK_G",
            "label": "G",
        }
        "KC_H": {
            "key": "DK_H",
            "label": "H",
        }
        "KC_J": {
            "key": "DK_J",
            "label": "J",
        }
        "KC_K": {
            "key": "DK_K",
            "label": "K",
        }
        "KC_L": {
            "key": "DK_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "DK_AE",
            "label": "Æ",
        }
        "KC_QUOT": {
            "key": "DK_OSTR",
            "label": "Ø",
        }
        "KC_NUHS": {
            "key": "DK_QUOT",
            "label": "'",
        }
        "KC_NUBS": {
            "key": "DK_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "DK_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "DK_X",
            "label": "X",
        }
        "KC_C": {
            "key": "DK_C",
            "label": "C",
        }
        "KC_V": {
            "key": "DK_V",
            "label": "V",
        }
        "KC_B": {
            "key": "DK_B",
            "label": "B",
        }
        "KC_N": {
            "key": "DK_N",
            "label": "N",
        }
        "KC_M": {
            "key": "DK_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "DK_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "DK_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "DK_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ § │ ! │ " │ # │ ¤ │ % │ & │ / │ ( │ ) │ = │ ? │ ` │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │ ^ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │ * │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │   │ ; │ : │ _ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(DK_HALF)": {
            "key": "DK_SECT",
            "label": "§",
        }
        "S(DK_1)": {
            "key": "DK_EXLM",
            "label": "!",
        }
        "S(DK_2)": {
            "key": "DK_DQUO",
            "label": "\"",
        }
        "S(DK_3)": {
            "key": "DK_HASH",
            "label": "#",
        }
        "S(DK_4)": {
            "key": "DK_CURR",
            "label": "¤",
        }
        "S(DK_5)": {
            "key": "DK_PERC",
            "label": "%",
        }
        "S(DK_6)": {
            "key": "DK_AMPR",
            "label": "&",
        }
        "S(DK_7)": {
            "key": "DK_SLSH",
            "label": "/",
        }
        "S(DK_8)": {
            "key": "DK_LPRN",
            "label": "(",
        }
        "S(DK_9)": {
            "key": "DK_RPRN",
            "label": ")",
        }
        "S(DK_0)": {
            "key": "DK_EQL",
            "label": "=",
        }
        "S(DK_PLUS)": {
            "key": "DK_QUES",
            "label": "?",
        }
        "S(DK_ACUT)": {
            "key": "DK_GRV",
            "label": "` (dead)",
        }
        "S(DK_DIAE)": {
            "key": "DK_CIRC",
            "label": "^ (dead)",
        }
        "S(DK_QUOT)": {
            "key": "DK_ASTR",
            "label": "*",
        }
        "S(DK_LABK)": {
            "key": "DK_RABK",
            "label": ">",
        }
        "S(DK_COMM)": {
            "key": "DK_SCLN",
            "label": ";",
        }
        "S(DK_DOT)": {
            "key": "DK_COLN",
            "label": ":",
        }
        "S(DK_MINS)": {
            "key": "DK_UNDS",
            "label": "_",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │   │ @ │ £ │ $ │ € │   │ { │ [ │ ] │ } │   │ | │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │ ~ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ \ │   │   │   │   │   │   │ µ │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(DK_2)": {
            "key": "DK_AT",
            "label": "@",
        }
        "ALGR(DK_3)": {
            "key": "DK_PND",
            "label": "£",
        }
        "ALGR(DK_4)": {
            "key": "DK_DLR",
            "label": "$",
        }
        "ALGR(DK_5)": {
            "key": "DK_EURO",
            "label": "€",
        }
        "ALGR(DK_7)": {
            "key": "DK_LCBR",
            "label": "{",
        }
        "ALGR(DK_8)": {
            "key": "DK_LBRC",
            "label": "[",
        }
        "ALGR(DK_9)": {
            "key": "DK_RBRC",
            "label": "]",
        }
        "ALGR(DK_0)": {
            "key": "DK_RCBR",
            "label": "}",
        }
        "ALGR(DK_ACUT)": {
            "key": "DK_PIPE",
            "label": "|",
        }
        "ALGR(DK_DIAE)": {
            "key": "DK_TILD",
            "label": "~ (dead)",
        }
        "ALGR(DK_LABK)": {
            "key": "DK_BSLS",
            "label": "\\",
        }
        "ALGR(DK_M)": {
            "key": "DK_MICR",
            "label": "µ",
        }
    }
}
