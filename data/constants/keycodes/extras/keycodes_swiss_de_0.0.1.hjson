{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ § │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ ' │ ^ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Z │ U │ I │ O │ P │ ü │ ¨ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ ö │ ä │ $ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ Y │ X │ C │ V │ B │ N │ M │ , │ . │ - │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "CH_SECT",
            "label": "§",
        }
        "KC_1": {
            "key": "CH_1",
            "label": "1",
        }
        "KC_2": {
            "key": "CH_2",
            "label": "2",
        }
        "KC_3": {
            "key": "CH_3",
            "label": "3",
        }
        "KC_4": {
            "key": "CH_4",
            "label": "4",
        }
        "KC_5": {
            "key": "CH_5",
            "label": "5",
        }
        "KC_6": {
            "key": "CH_6",
            "label": "6",
        }
        "KC_7": {
            "key": "CH_7",
            "label": "7",
        }
        "KC_8": {
            "key": "CH_8",
            "label": "8",
        }
        "KC_9": {
            "key": "CH_9",
            "label": "9",
        }
        "KC_0": {
            "key": "CH_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "CH_QUOT",
            "label": "'",
        }
        "KC_EQL": {
            "key": "CH_CIRC",
            "label": "^ (dead)",
        }
        "KC_Q": {
            "key": "CH_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "CH_W",
            "label": "W",
        }
        "KC_E": {
            "key": "CH_E",
            "label": "E",
        }
        "KC_R": {
            "key": "CH_R",
            "label": "R",
        }
        "KC_T": {
            "key": "CH_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "CH_Z",
            "label": "Z",
        }
        "KC_U": {
            "key": "CH_U",
            "label": "U",
        }
        "KC_I": {
            "key": "CH_I",
            "label": "I",
        }
        "KC_O": {
            "key": "CH_O",
            "label": "O",
        }
        "KC_P": {
            "key": "CH_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "CH_UDIA",
            "label": "ü",
        }
        "KC_RBRC": {
            "key": "CH_DIAE",
            "label": "¨ (dead)",
        }
        "KC_A": {
            "key": "CH_A",
            "label": "A",
        }
        "KC_S": {
            "key": "CH_S",
            "label": "S",
        }
        "KC_D": {
            "key": "CH_D",
            "label": "D",
        }
        "KC_F": {
            "key": "CH_F",
            "label": "F",
        }
        "KC_G": {
            "key": "CH_G",
            "label": "G",
        }
        "KC_H": {
            "key": "CH_H",
            "label": "H",
        }
        "KC_J": {
            "key": "CH_J",
            "label": "J",
        }
        "KC_K": {
            "key": "CH_K",
            "label": "K",
        }
        "KC_L": {
            "key": "CH_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "CH_ODIA",
            "label": "ö",
        }
        "KC_QUOT": {
            "key": "CH_ADIA",
            "label": "ä",
        }
        "KC_NUHS": {
            "key": "CH_DLR",
            "label": "$",
        }
        "KC_NUBS": {
            "key": "CH_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "CH_Y",
            "label": "Y",
        }
        "KC_X": {
            "key": "CH_X",
            "label": "X",
        }
        "KC_C": {
            "key": "CH_C",
            "label": "C",
        }
        "KC_V": {
            "key": "CH_V",
            "label": "V",
        }
        "KC_B": {
            "key": "CH_B",
            "label": "B",
        }
        "KC_N": {
            "key": "CH_N",
            "label": "N",
        }
        "KC_M": {
            "key": "CH_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "CH_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "CH_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "CH_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ° │ + │ " │ * │ ç │ % │ & │ / │ ( │ ) │ = │ ? │ ` │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ è │ ! │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │ é │ à │ £ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │   │ ; │ : │ _ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(CH_SECT)": {
            "key": "CH_DEG",
            "label": "°",
        }
        "S(CH_1)": {
            "key": "CH_PLUS",
            "label": "+",
        }
        "S(CH_2)": {
            "key": "CH_DQUO",
            "label": "\"",
        }
        "S(CH_3)": {
            "key": "CH_ASTR",
            "label": "*",
        }
        "S(CH_4)": {
            "key": "CH_CCED",
            "label": "ç",
        }
        "S(CH_5)": {
            "key": "CH_PERC",
            "label": "%",
        }
        "S(CH_6)": {
            "key": "CH_AMPR",
            "label": "&",
        }
        "S(CH_7)": {
            "key": "CH_SLSH",
            "label": "/",
        }
        "S(CH_8)": {
            "key": "CH_LPRN",
            "label": "(",
        }
        "S(CH_9)": {
            "key": "CH_RPRN",
            "label": ")",
        }
        "S(CH_0)": {
            "key": "CH_EQL",
            "label": "=",
        }
        "S(CH_QUOT)": {
            "key": "CH_QUES",
            "label": "?",
        }
        "S(CH_CIRC)": {
            "key": "CH_GRV",
            "label": "` (dead)",
        }
        "S(CH_UDIA)": {
            "key": "CH_EGRV",
            "label": "è",
        }
        "S(CH_DIAE)": {
            "key": "CH_EXLM",
            "label": "!",
        }
        "S(CH_ODIA)": {
            "key": "CH_EACU",
            "label": "é",
        }
        "S(CH_ADIA)": {
            "key": "CH_AGRV",
            "label": "à",
        }
        "S(CH_DLR)": {
            "key": "CH_PND",
            "label": "£",
        }
        "S(CH_LABK)": {
            "key": "CH_RABK",
            "label": ">",
        }
        "S(CH_COMM)": {
            "key": "CH_SCLN",
            "label": ";",
        }
        "S(CH_DOT)": {
            "key": "CH_COLN",
            "label": ":",
        }
        "S(CH_MINS)": {
            "key": "CH_UNDS",
            "label": "_",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │ ¦ │ @ │ # │   │   │ ¬ │ | │ ¢ │   │   │ ´ │ ~ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │ € │   │   │   │   │   │   │   │ [ │ ] │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │ { │ } │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ \ │   │   │   │   │   │   │   │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(CH_1)": {
            "key": "CH_BRKP",
            "label": "¦",
        }
        "ALGR(CH_2)": {
            "key": "CH_AT",
            "label": "@",
        }
        "ALGR(CH_3)": {
            "key": "CH_HASH",
            "label": "#",
        }
        "ALGR(CH_6)": {
            "key": "CH_NOT",
            "label": "¬",
        }
        "ALGR(CH_7)": {
            "key": "CH_PIPE",
            "label": "|",
        }
        "ALGR(CH_8)": {
            "key": "CH_CENT",
            "label": "¢",
        }
        "ALGR(CH_QUOT)": {
            "key": "CH_ACUT",
            "label": "´ (dead)",
        }
        "ALGR(CH_CIRC)": {
            "key": "CH_TILD",
            "label": "~ (dead)",
        }
        "ALGR(CH_E)": {
            "key": "CH_EURO",
            "label": "€",
        }
        "ALGR(CH_UDIA)": {
            "key": "CH_LBRC",
            "label": "[",
        }
        "ALGR(CH_DIAE)": {
            "key": "CH_RBRC",
            "label": "]",
        }
        "ALGR(CH_ADIA)": {
            "key": "CH_LCBR",
            "label": "{",
        }
        "ALGR(CH_DLR)": {
            "key": "CH_RCBR",
            "label": "}",
        }
        "ALGR(CH_LABK)": {
            "key": "CH_BSLS",
            "label": "\\",
        }
    }
}
