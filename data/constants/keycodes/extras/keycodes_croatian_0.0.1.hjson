{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ¸ │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ ' │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Z │ U │ I │ O │ P │ Š │ Đ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ Č │ Ć │ Ž │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ Y │ X │ C │ V │ B │ N │ M │ , │ . │ - │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "HR_CEDL",
            "label": "¸ (dead)",
        }
        "KC_1": {
            "key": "HR_1",
            "label": "1",
        }
        "KC_2": {
            "key": "HR_2",
            "label": "2",
        }
        "KC_3": {
            "key": "HR_3",
            "label": "3",
        }
        "KC_4": {
            "key": "HR_4",
            "label": "4",
        }
        "KC_5": {
            "key": "HR_5",
            "label": "5",
        }
        "KC_6": {
            "key": "HR_6",
            "label": "6",
        }
        "KC_7": {
            "key": "HR_7",
            "label": "7",
        }
        "KC_8": {
            "key": "HR_8",
            "label": "8",
        }
        "KC_9": {
            "key": "HR_9",
            "label": "9",
        }
        "KC_0": {
            "key": "HR_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "HR_QUOT",
            "label": "'",
        }
        "KC_EQL": {
            "key": "HR_PLUS",
            "label": "+",
        }
        "KC_Q": {
            "key": "HR_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "HR_W",
            "label": "W",
        }
        "KC_E": {
            "key": "HR_E",
            "label": "E",
        }
        "KC_R": {
            "key": "HR_R",
            "label": "R",
        }
        "KC_T": {
            "key": "HR_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "HR_Z",
            "label": "Z",
        }
        "KC_U": {
            "key": "HR_U",
            "label": "U",
        }
        "KC_I": {
            "key": "HR_I",
            "label": "I",
        }
        "KC_O": {
            "key": "HR_O",
            "label": "O",
        }
        "KC_P": {
            "key": "HR_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "HR_SCAR",
            "label": "Š",
        }
        "KC_RBRC": {
            "key": "HR_DSTR",
            "label": "Đ",
        }
        "KC_A": {
            "key": "HR_A",
            "label": "A",
        }
        "KC_S": {
            "key": "HR_S",
            "label": "S",
        }
        "KC_D": {
            "key": "HR_D",
            "label": "D",
        }
        "KC_F": {
            "key": "HR_F",
            "label": "F",
        }
        "KC_G": {
            "key": "HR_G",
            "label": "G",
        }
        "KC_H": {
            "key": "HR_H",
            "label": "H",
        }
        "KC_J": {
            "key": "HR_J",
            "label": "J",
        }
        "KC_K": {
            "key": "HR_K",
            "label": "K",
        }
        "KC_L": {
            "key": "HR_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "HR_CCAR",
            "label": "Č",
        }
        "KC_QUOT": {
            "key": "HR_CACU",
            "label": "Ć",
        }
        "KC_NUHS": {
            "key": "HR_ZCAR",
            "label": "Ž",
        }
        "KC_NUBS": {
            "key": "HR_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "HR_Y",
            "label": "Y",
        }
        "KC_X": {
            "key": "HR_X",
            "label": "X",
        }
        "KC_C": {
            "key": "HR_C",
            "label": "C",
        }
        "KC_V": {
            "key": "HR_V",
            "label": "V",
        }
        "KC_B": {
            "key": "HR_B",
            "label": "B",
        }
        "KC_N": {
            "key": "HR_N",
            "label": "N",
        }
        "KC_M": {
            "key": "HR_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "HR_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "HR_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "HR_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ¨ │ ! │ " │ # │ $ │ % │ & │ / │ ( │ ) │ = │ ? │ * │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │   │ ; │ : │ _ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(HR_CEDL)": {
            "key": "HR_DIAE",
            "label": "¨ (dead)",
        }
        "S(HR_1)": {
            "key": "HR_EXLM",
            "label": "!",
        }
        "S(HR_2)": {
            "key": "HR_DQUO",
            "label": "\"",
        }
        "S(HR_3)": {
            "key": "HR_HASH",
            "label": "#",
        }
        "S(HR_4)": {
            "key": "HR_DLR",
            "label": "$",
        }
        "S(HR_5)": {
            "key": "HR_PERC",
            "label": "%",
        }
        "S(HR_6)": {
            "key": "HR_AMPR",
            "label": "&",
        }
        "S(HR_7)": {
            "key": "HR_SLSH",
            "label": "/",
        }
        "S(HR_8)": {
            "key": "HR_LPRN",
            "label": "(",
        }
        "S(HR_9)": {
            "key": "HR_RPRN",
            "label": ")",
        }
        "S(HR_0)": {
            "key": "HR_EQL",
            "label": "=",
        }
        "S(HR_QUOT)": {
            "key": "HR_QUES",
            "label": "?",
        }
        "S(HR_PLUS)": {
            "key": "HR_ASTR",
            "label": "*",
        }
        "S(HR_LABK)": {
            "key": "HR_RABK",
            "label": ">",
        }
        "S(HR_COMM)": {
            "key": "HR_SCLN",
            "label": ";",
        }
        "S(HR_DOT)": {
            "key": "HR_COLN",
            "label": ":",
        }
        "S(HR_MINS)": {
            "key": "HR_UNDS",
            "label": "_",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │ ~ │ ˇ │ ^ │ ˘ │ ° │ ˛ │ ` │ ˙ │ ´ │ ˝ │   │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ \ │ | │ € │   │   │   │   │   │   │   │ ÷ │ × │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │ [ │ ] │   │   │ ł │ Ł │   │ ß │ ¤ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │ @ │ { │ } │ § │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(HR_1)": {
            "key": "HR_TILD",
            "label": "~",
        }
        "ALGR(HR_2)": {
            "key": "HR_CARN",
            "label": "ˇ (dead)",
        }
        "ALGR(HR_3)": {
            "key": "HR_CIRC",
            "label": "^ (dead)",
        }
        "ALGR(HR_4)": {
            "key": "HR_BREV",
            "label": "˘ (dead)",
        }
        "ALGR(HR_5)": {
            "key": "HR_RNGA",
            "label": "° (dead)",
        }
        "ALGR(HR_6)": {
            "key": "HR_OGON",
            "label": "˛ (dead)",
        }
        "ALGR(HR_7)": {
            "key": "HR_GRV",
            "label": "`",
        }
        "ALGR(HR_8)": {
            "key": "HR_DOTA",
            "label": "˙ (dead)",
        }
        "ALGR(HR_9)": {
            "key": "HR_ACUT",
            "label": "´ (dead)",
        }
        "ALGR(HR_0)": {
            "key": "HR_DACU",
            "label": "˝ (dead)",
        }
        "ALGR(HR_Q)": {
            "key": "HR_BSLS",
            "label": "\\",
        }
        "ALGR(HR_W)": {
            "key": "HR_PIPE",
            "label": "|",
        }
        "ALGR(HR_E)": {
            "key": "HR_EURO",
            "label": "€",
        }
        "ALGR(HR_SCAR)": {
            "key": "HR_DIV",
            "label": "÷",
        }
        "ALGR(HR_DSTR)": {
            "key": "HR_MUL",
            "label": "×",
        }
        "ALGR(HR_F)": {
            "key": "HR_LBRC",
            "label": "[",
        }
        "ALGR(HR_G)": {
            "key": "HR_RBRC",
            "label": "]",
        }
        "ALGR(HR_K)": {
            "key": "HR_LLST",
            "label": "ł",
        }
        "ALGR(HR_L)": {
            "key": "HR_CLST",
            "label": "Ł",
        }
        "ALGR(HR_CACU)": {
            "key": "HR_SS",
            "label": "ß",
        }
        "ALGR(HR_ZCAR)": {
            "key": "HR_CURR",
            "label": "¤",
        }
        "ALGR(HR_V)": {
            "key": "HR_AT",
            "label": "@",
        }
        "ALGR(HR_B)": {
            "key": "HR_LCBR",
            "label": "{",
        }
        "ALGR(HR_N)": {
            "key": "HR_RCBR",
            "label": "}",
        }
        "ALGR(HR_M)": {
            "key": "HR_SECT",
            "label": "§",
        }
    }
}
