{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ º │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ ' │ ¡ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ . │ , │ Ñ │ P │ Y │ F │ G │ C │ H │ L │ ` │ + │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ O │ E │ U │ I │ D │ R │ T │ N │ S │ ´ │ Ç │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ - │ Q │ J │ K │ X │ B │ M │ W │ V │ Z │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "DV_MORD",
            "label": "º",
        }
        "KC_1": {
            "key": "DV_1",
            "label": "1",
        }
        "KC_2": {
            "key": "DV_2",
            "label": "2",
        }
        "KC_3": {
            "key": "DV_3",
            "label": "3",
        }
        "KC_4": {
            "key": "DV_4",
            "label": "4",
        }
        "KC_5": {
            "key": "DV_5",
            "label": "5",
        }
        "KC_6": {
            "key": "DV_6",
            "label": "6",
        }
        "KC_7": {
            "key": "DV_7",
            "label": "7",
        }
        "KC_8": {
            "key": "DV_8",
            "label": "8",
        }
        "KC_9": {
            "key": "DV_9",
            "label": "9",
        }
        "KC_0": {
            "key": "DV_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "DV_QUOT",
            "label": "'",
        }
        "KC_EQL": {
            "key": "DV_IEXL",
            "label": "¡",
        }
        "KC_Q": {
            "key": "DV_DOT",
            "label": ".",
        }
        "KC_W": {
            "key": "DV_COMM",
            "label": ",",
        }
        "KC_E": {
            "key": "DV_NTIL",
            "label": "Ñ",
        }
        "KC_R": {
            "key": "DV_P",
            "label": "P",
        }
        "KC_T": {
            "key": "DV_Y",
            "label": "Y",
        }
        "KC_Y": {
            "key": "DV_F",
            "label": "F",
        }
        "KC_U": {
            "key": "DV_G",
            "label": "G",
        }
        "KC_I": {
            "key": "DV_C",
            "label": "C",
        }
        "KC_O": {
            "key": "DV_H",
            "label": "H",
        }
        "KC_P": {
            "key": "DV_L",
            "label": "L",
        }
        "KC_LBRC": {
            "key": "DV_GRV",
            "label": "` (dead)",
        }
        "KC_RBRC": {
            "key": "DV_PLUS",
            "label": "+",
        }
        "KC_A": {
            "key": "DV_A",
            "label": "A",
        }
        "KC_S": {
            "key": "DV_O",
            "label": "O",
        }
        "KC_D": {
            "key": "DV_E",
            "label": "E",
        }
        "KC_F": {
            "key": "DV_U",
            "label": "U",
        }
        "KC_G": {
            "key": "DV_I",
            "label": "I",
        }
        "KC_H": {
            "key": "DV_D",
            "label": "D",
        }
        "KC_J": {
            "key": "DV_R",
            "label": "R",
        }
        "KC_K": {
            "key": "DV_T",
            "label": "T",
        }
        "KC_L": {
            "key": "DV_N",
            "label": "N",
        }
        "KC_SCLN": {
            "key": "DV_S",
            "label": "S",
        }
        "KC_QUOT": {
            "key": "DV_ACUT",
            "label": "´ (dead)",
        }
        "KC_NUHS": {
            "key": "DV_CCED",
            "label": "Ç",
        }
        "KC_NUBS": {
            "key": "DV_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "DV_MINS",
            "label": "-",
        }
        "KC_X": {
            "key": "DV_Q",
            "label": "Q",
        }
        "KC_C": {
            "key": "DV_J",
            "label": "J",
        }
        "KC_V": {
            "key": "DV_K",
            "label": "K",
        }
        "KC_B": {
            "key": "DV_X",
            "label": "X",
        }
        "KC_N": {
            "key": "DV_B",
            "label": "B",
        }
        "KC_M": {
            "key": "DV_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "DV_W",
            "label": "W",
        }
        "KC_DOT": {
            "key": "DV_V",
            "label": "V",
        }
        "KC_SLSH": {
            "key": "DV_Z",
            "label": "Z",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ª │ ! │ " │ · │ $ │ % │ & │ / │ ( │ ) │ = │ ? │ ¿ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ : │ ; │   │   │   │   │   │   │   │   │ ^ │ * │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │ ¨ │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │ _ │   │   │   │   │   │   │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(DV_MORD)": {
            "key": "DV_FORD",
            "label": "ª",
        }
        "S(DV_1)": {
            "key": "DV_EXLM",
            "label": "!",
        }
        "S(DV_2)": {
            "key": "DV_DQUO",
            "label": "\"",
        }
        "S(DV_3)": {
            "key": "DV_BULT",
            "label": "·",
        }
        "S(DV_4)": {
            "key": "DV_DLR",
            "label": "$",
        }
        "S(DV_5)": {
            "key": "DV_PERC",
            "label": "%",
        }
        "S(DV_6)": {
            "key": "DV_AMPR",
            "label": "&",
        }
        "S(DV_7)": {
            "key": "DV_SLSH",
            "label": "/",
        }
        "S(DV_8)": {
            "key": "DV_LPRN",
            "label": "(",
        }
        "S(DV_9)": {
            "key": "DV_RPRN",
            "label": ")",
        }
        "S(DV_0)": {
            "key": "DV_EQL",
            "label": "=",
        }
        "S(DV_QUOT)": {
            "key": "DV_QUES",
            "label": "?",
        }
        "S(DV_IEXL)": {
            "key": "DV_IQUE",
            "label": "¿",
        }
        "S(DV_DOT)": {
            "key": "DV_COLN",
            "label": ":",
        }
        "S(DV_COMM)": {
            "key": "DV_SCLN",
            "label": ";",
        }
        "S(DV_GRV)": {
            "key": "DV_CIRC",
            "label": "^ (dead)",
        }
        "S(DV_PLUS)": {
            "key": "DV_ASTR",
            "label": "*",
        }
        "S(DV_ACUT)": {
            "key": "DV_DIAE",
            "label": "¨ (dead)",
        }
        "S(DV_LABK)": {
            "key": "DV_RABK",
            "label": ">",
        }
        "S(DV_MINS)": {
            "key": "DV_UNDS",
            "label": "_",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ \ │ | │ @ │ # │ ~ │ € │ ¬ │   │   │   │   │   │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ [ │ ] │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │ { │ } │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │   │   │   │   │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(DV_MORD)": {
            "key": "DV_BSLS",
            "label": "\\",
        }
        "ALGR(DV_1)": {
            "key": "DV_PIPE",
            "label": "|",
        }
        "ALGR(DV_2)": {
            "key": "DV_AT",
            "label": "@",
        }
        "ALGR(DV_3)": {
            "key": "DV_HASH",
            "label": "#",
        }
        "ALGR(DV_4)": {
            "key": "DV_TILD",
            "label": "~",
        }
        "ALGR(DV_5)": {
            "key": "DV_EURO",
            "label": "€",
        }
        "ALGR(DV_6)": {
            "key": "DV_NOT",
            "label": "¬",
        }
        "ALGR(DV_GRV)": {
            "key": "DV_LBRC",
            "label": "[",
        }
        "ALGR(DV_PLUS)": {
            "key": "DV_RBRC",
            "label": "]",
        }
        "ALGR(DV_ACUT)": {
            "key": "DV_LCBR",
            "label": "{",
        }
        "ALGR(DV_CCED)": {
            "key": "DV_RCBR",
            "label": "}",
        }
    }
}
