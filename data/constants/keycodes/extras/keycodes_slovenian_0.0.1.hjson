{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ¸ │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ ' │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Z │ U │ I │ O │ P │ Š │ Đ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ Č │ Ć │ Ž │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ Y │ X │ C │ V │ B │ N │ M │ , │ . │ - │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "SI_CEDL",
            "label": "¸ (dead)",
        }
        "KC_1": {
            "key": "SI_1",
            "label": "1",
        }
        "KC_2": {
            "key": "SI_2",
            "label": "2",
        }
        "KC_3": {
            "key": "SI_3",
            "label": "3",
        }
        "KC_4": {
            "key": "SI_4",
            "label": "4",
        }
        "KC_5": {
            "key": "SI_5",
            "label": "5",
        }
        "KC_6": {
            "key": "SI_6",
            "label": "6",
        }
        "KC_7": {
            "key": "SI_7",
            "label": "7",
        }
        "KC_8": {
            "key": "SI_8",
            "label": "8",
        }
        "KC_9": {
            "key": "SI_9",
            "label": "9",
        }
        "KC_0": {
            "key": "SI_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "SI_QUOT",
            "label": "'",
        }
        "KC_EQL": {
            "key": "SI_PLUS",
            "label": "+",
        }
        "KC_Q": {
            "key": "SI_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "SI_W",
            "label": "W",
        }
        "KC_E": {
            "key": "SI_E",
            "label": "E",
        }
        "KC_R": {
            "key": "SI_R",
            "label": "R",
        }
        "KC_T": {
            "key": "SI_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "SI_Z",
            "label": "Z",
        }
        "KC_U": {
            "key": "SI_U",
            "label": "U",
        }
        "KC_I": {
            "key": "SI_I",
            "label": "I",
        }
        "KC_O": {
            "key": "SI_O",
            "label": "O",
        }
        "KC_P": {
            "key": "SI_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "SI_SCAR",
            "label": "Š",
        }
        "KC_RBRC": {
            "key": "SI_DSTR",
            "label": "Đ",
        }
        "KC_A": {
            "key": "SI_A",
            "label": "A",
        }
        "KC_S": {
            "key": "SI_S",
            "label": "S",
        }
        "KC_D": {
            "key": "SI_D",
            "label": "D",
        }
        "KC_F": {
            "key": "SI_F",
            "label": "F",
        }
        "KC_G": {
            "key": "SI_G",
            "label": "G",
        }
        "KC_H": {
            "key": "SI_H",
            "label": "H",
        }
        "KC_J": {
            "key": "SI_J",
            "label": "J",
        }
        "KC_K": {
            "key": "SI_K",
            "label": "K",
        }
        "KC_L": {
            "key": "SI_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "SI_CCAR",
            "label": "Č",
        }
        "KC_QUOT": {
            "key": "SI_CACU",
            "label": "Ć",
        }
        "KC_NUHS": {
            "key": "SI_ZCAR",
            "label": "Ž",
        }
        "KC_NUBS": {
            "key": "SI_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "SI_Y",
            "label": "Y",
        }
        "KC_X": {
            "key": "SI_X",
            "label": "X",
        }
        "KC_C": {
            "key": "SI_C",
            "label": "C",
        }
        "KC_V": {
            "key": "SI_V",
            "label": "V",
        }
        "KC_B": {
            "key": "SI_B",
            "label": "B",
        }
        "KC_N": {
            "key": "SI_N",
            "label": "N",
        }
        "KC_M": {
            "key": "SI_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "SI_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "SI_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "SI_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ¨ │ ! │ " │ # │ $ │ % │ & │ / │ ( │ ) │ = │ ? │ * │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │   │ ; │ : │ _ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(SI_CEDL)": {
            "key": "SI_DIAE",
            "label": "¨ (dead)",
        }
        "S(SI_1)": {
            "key": "SI_EXLM",
            "label": "!",
        }
        "S(SI_2)": {
            "key": "SI_DQUO",
            "label": "\"",
        }
        "S(SI_3)": {
            "key": "SI_HASH",
            "label": "#",
        }
        "S(SI_4)": {
            "key": "SI_DLR",
            "label": "$",
        }
        "S(SI_5)": {
            "key": "SI_PERC",
            "label": "%",
        }
        "S(SI_6)": {
            "key": "SI_AMPR",
            "label": "&",
        }
        "S(SI_7)": {
            "key": "SI_SLSH",
            "label": "/",
        }
        "S(SI_8)": {
            "key": "SI_LPRN",
            "label": "(",
        }
        "S(SI_9)": {
            "key": "SI_RPRN",
            "label": ")",
        }
        "S(SI_0)": {
            "key": "SI_EQL",
            "label": "=",
        }
        "S(SI_QUOT)": {
            "key": "SI_QUES",
            "label": "?",
        }
        "S(SI_PLUS)": {
            "key": "SI_ASTR",
            "label": "*",
        }
        "S(SI_LABK)": {
            "key": "SI_RABK",
            "label": ">",
        }
        "S(SI_COMM)": {
            "key": "SI_SCLN",
            "label": ";",
        }
        "S(SI_DOT)": {
            "key": "SI_COLN",
            "label": ":",
        }
        "S(SI_MINS)": {
            "key": "SI_UNDS",
            "label": "_",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │ ~ │ ˇ │ ^ │ ˘ │ ° │ ˛ │ ` │ ˙ │ ´ │ ˝ │   │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ \ │ | │ € │   │   │   │   │   │   │   │ ÷ │ × │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │ [ │ ] │   │   │ ł │ Ł │   │ ß │ ¤ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │ @ │ { │ } │ § │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(SI_1)": {
            "key": "SI_TILD",
            "label": "~",
        }
        "ALGR(SI_2)": {
            "key": "SI_CARN",
            "label": "ˇ (dead)",
        }
        "ALGR(SI_3)": {
            "key": "SI_CIRC",
            "label": "^ (dead)",
        }
        "ALGR(SI_4)": {
            "key": "SI_BREV",
            "label": "˘ (dead)",
        }
        "ALGR(SI_5)": {
            "key": "SI_RNGA",
            "label": "° (dead)",
        }
        "ALGR(SI_6)": {
            "key": "SI_OGON",
            "label": "˛ (dead)",
        }
        "ALGR(SI_7)": {
            "key": "SI_GRV",
            "label": "`",
        }
        "ALGR(SI_8)": {
            "key": "SI_DOTA",
            "label": "˙ (dead)",
        }
        "ALGR(SI_9)": {
            "key": "SI_ACUT",
            "label": "´ (dead)",
        }
        "ALGR(SI_0)": {
            "key": "SI_DACU",
            "label": "˝ (dead)",
        }
        "ALGR(SI_Q)": {
            "key": "SI_BSLS",
            "label": "\\",
        }
        "ALGR(SI_W)": {
            "key": "SI_PIPE",
            "label": "|",
        }
        "ALGR(SI_E)": {
            "key": "SI_EURO",
            "label": "€",
        }
        "ALGR(SI_SCAR)": {
            "key": "SI_DIV",
            "label": "÷",
        }
        "ALGR(SI_DSTR)": {
            "key": "SI_MUL",
            "label": "×",
        }
        "ALGR(SI_F)": {
            "key": "SI_LBRC",
            "label": "[",
        }
        "ALGR(SI_G)": {
            "key": "SI_RBRC",
            "label": "]",
        }
        "ALGR(SI_K)": {
            "key": "SI_LLST",
            "label": "ł",
        }
        "ALGR(SI_L)": {
            "key": "SI_CLST",
            "label": "Ł",
        }
        "ALGR(SI_CACU)": {
            "key": "SI_SS",
            "label": "ß",
        }
        "ALGR(SI_ZCAR)": {
            "key": "SI_CURR",
            "label": "¤",
        }
        "ALGR(SI_V)": {
            "key": "SI_AT",
            "label": "@",
        }
        "ALGR(SI_B)": {
            "key": "SI_LCBR",
            "label": "{",
        }
        "ALGR(SI_N)": {
            "key": "SI_RCBR",
            "label": "}",
        }
        "ALGR(SI_M)": {
            "key": "SI_SECT",
            "label": "§",
        }
    }
}
