{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ` │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ - │ = │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ F │ P │ G │ J │ L │ U │ Y │ ; │ [ │ ] │  \  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │ A │ R │ S │ T │ D │ H │ N │ E │ I │ O │ ' │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │ Z │ X │ C │ V │ B │ K │ M │ , │ . │ / │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "CM_GRV",
            "label": "`",
        }
        "KC_1": {
            "key": "CM_1",
            "label": "1",
        }
        "KC_2": {
            "key": "CM_2",
            "label": "2",
        }
        "KC_3": {
            "key": "CM_3",
            "label": "3",
        }
        "KC_4": {
            "key": "CM_4",
            "label": "4",
        }
        "KC_5": {
            "key": "CM_5",
            "label": "5",
        }
        "KC_6": {
            "key": "CM_6",
            "label": "6",
        }
        "KC_7": {
            "key": "CM_7",
            "label": "7",
        }
        "KC_8": {
            "key": "CM_8",
            "label": "8",
        }
        "KC_9": {
            "key": "CM_9",
            "label": "9",
        }
        "KC_0": {
            "key": "CM_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "CM_MINS",
            "label": "-",
        }
        "KC_EQL": {
            "key": "CM_EQL",
            "label": "=",
        }
        "KC_Q": {
            "key": "CM_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "CM_W",
            "label": "W",
        }
        "KC_E": {
            "key": "CM_F",
            "label": "F",
        }
        "KC_R": {
            "key": "CM_P",
            "label": "P",
        }
        "KC_T": {
            "key": "CM_G",
            "label": "G",
        }
        "KC_Y": {
            "key": "CM_J",
            "label": "J",
        }
        "KC_U": {
            "key": "CM_L",
            "label": "L",
        }
        "KC_I": {
            "key": "CM_U",
            "label": "U",
        }
        "KC_O": {
            "key": "CM_Y",
            "label": "Y",
        }
        "KC_P": {
            "key": "CM_SCLN",
            "label": ";",
        }
        "KC_LBRC": {
            "key": "CM_LBRC",
            "label": "[",
        }
        "KC_RBRC": {
            "key": "CM_RBRC",
            "label": "]",
        }
        "KC_BSLS": {
            "key": "CM_BSLS",
            "label": "\\",
        }
        "KC_A": {
            "key": "CM_A",
            "label": "A",
        }
        "KC_S": {
            "key": "CM_R",
            "label": "R",
        }
        "KC_D": {
            "key": "CM_S",
            "label": "S",
        }
        "KC_F": {
            "key": "CM_T",
            "label": "T",
        }
        "KC_G": {
            "key": "CM_D",
            "label": "D",
        }
        "KC_H": {
            "key": "CM_H",
            "label": "H",
        }
        "KC_J": {
            "key": "CM_N",
            "label": "N",
        }
        "KC_K": {
            "key": "CM_E",
            "label": "E",
        }
        "KC_L": {
            "key": "CM_I",
            "label": "I",
        }
        "KC_SCLN": {
            "key": "CM_O",
            "label": "O",
        }
        "KC_QUOT": {
            "key": "CM_QUOT",
            "label": "'",
        }
        "KC_Z": {
            "key": "CM_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "CM_X",
            "label": "X",
        }
        "KC_C": {
            "key": "CM_C",
            "label": "C",
        }
        "KC_V": {
            "key": "CM_V",
            "label": "V",
        }
        "KC_B": {
            "key": "CM_B",
            "label": "B",
        }
        "KC_N": {
            "key": "CM_K",
            "label": "K",
        }
        "KC_M": {
            "key": "CM_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "CM_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "CM_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "CM_SLSH",
            "label": "/",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ~ │ ! │ @ │ # │ $ │ % │ ^ │ & │ * │ ( │ ) │ _ │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │ : │ { │ } │  |  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │   │   │   │   │   │   │   │   │   │   │ " │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │   │   │   │   │   │   │   │ < │ > │ ? │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(CM_GRV)": {
            "key": "CM_TILD",
            "label": "~",
        }
        "S(CM_1)": {
            "key": "CM_EXLM",
            "label": "!",
        }
        "S(CM_2)": {
            "key": "CM_AT",
            "label": "@",
        }
        "S(CM_3)": {
            "key": "CM_HASH",
            "label": "#",
        }
        "S(CM_4)": {
            "key": "CM_DLR",
            "label": "$",
        }
        "S(CM_5)": {
            "key": "CM_PERC",
            "label": "%",
        }
        "S(CM_6)": {
            "key": "CM_CIRC",
            "label": "^",
        }
        "S(CM_7)": {
            "key": "CM_AMPR",
            "label": "&",
        }
        "S(CM_8)": {
            "key": "CM_ASTR",
            "label": "*",
        }
        "S(CM_9)": {
            "key": "CM_LPRN",
            "label": "(",
        }
        "S(CM_0)": {
            "key": "CM_RPRN",
            "label": ")",
        }
        "S(CM_MINS)": {
            "key": "CM_UNDS",
            "label": "_",
        }
        "S(CM_EQL)": {
            "key": "CM_PLUS",
            "label": "+",
        }
        "S(CM_SCLN)": {
            "key": "CM_COLN",
            "label": ":",
        }
        "S(CM_LBRC)": {
            "key": "CM_LCBR",
            "label": "{",
        }
        "S(CM_RBRC)": {
            "key": "CM_RCBR",
            "label": "}",
        }
        "S(CM_BSLS)": {
            "key": "CM_PIPE",
            "label": "|",
        }
        "S(CM_QUOT)": {
            "key": "CM_DQUO",
            "label": "\"",
        }
        "S(CM_COMM)": {
            "key": "CM_LABK",
            "label": "<",
        }
        "S(CM_DOT)": {
            "key": "CM_RABK",
            "label": ">",
        }
        "S(CM_SLSH)": {
            "key": "CM_QUES",
            "label": "?",
        }
    }
}
