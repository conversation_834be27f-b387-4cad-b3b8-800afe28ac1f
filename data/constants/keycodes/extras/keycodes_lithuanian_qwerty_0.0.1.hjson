{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ` │ Ą │ Č │ Ę │ Ė │ Į │ Š │ Ų │ Ū │ 9 │ 0 │ - │ Ž │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ [ │ ] │  \  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ ; │ ' │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ / │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "LT_GRV",
            "label": "`",
        }
        "KC_1": {
            "key": "LT_AOGO",
            "label": "Ą",
        }
        "KC_2": {
            "key": "LT_CCAR",
            "label": "Č",
        }
        "KC_3": {
            "key": "LT_EOGO",
            "label": "Ę",
        }
        "KC_4": {
            "key": "LT_EDOT",
            "label": "Ė",
        }
        "KC_5": {
            "key": "LT_IOGO",
            "label": "Į",
        }
        "KC_6": {
            "key": "LT_SCAR",
            "label": "Š",
        }
        "KC_7": {
            "key": "LT_UOGO",
            "label": "Ų",
        }
        "KC_8": {
            "key": "LT_UMAC",
            "label": "Ū",
        }
        "KC_9": {
            "key": "LT_9",
            "label": "9",
        }
        "KC_0": {
            "key": "LT_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "LT_MINS",
            "label": "-",
        }
        "KC_EQL": {
            "key": "LT_ZCAR",
            "label": "Ž",
        }
        "KC_Q": {
            "key": "LT_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "LT_W",
            "label": "W",
        }
        "KC_E": {
            "key": "LT_E",
            "label": "E",
        }
        "KC_R": {
            "key": "LT_R",
            "label": "R",
        }
        "KC_T": {
            "key": "LT_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "LT_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "LT_U",
            "label": "U",
        }
        "KC_I": {
            "key": "LT_I",
            "label": "I",
        }
        "KC_O": {
            "key": "LT_O",
            "label": "O",
        }
        "KC_P": {
            "key": "LT_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "LT_LBRC",
            "label": "[",
        }
        "KC_RBRC": {
            "key": "LT_RBRC",
            "label": "]",
        }
        "KC_A": {
            "key": "LT_A",
            "label": "A",
        }
        "KC_S": {
            "key": "LT_S",
            "label": "S",
        }
        "KC_D": {
            "key": "LT_D",
            "label": "D",
        }
        "KC_F": {
            "key": "LT_F",
            "label": "F",
        }
        "KC_G": {
            "key": "LT_G",
            "label": "G",
        }
        "KC_H": {
            "key": "LT_H",
            "label": "H",
        }
        "KC_J": {
            "key": "LT_J",
            "label": "J",
        }
        "KC_K": {
            "key": "LT_K",
            "label": "K",
        }
        "KC_L": {
            "key": "LT_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "LT_SCLN",
            "label": ";",
        }
        "KC_QUOT": {
            "key": "LT_QUOT",
            "label": "'",
        }
        "KC_BSLS": {
            "key": "LT_BSLS",
            "label": "\\",
        }
        "KC_Z": {
            "key": "LT_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "LT_X",
            "label": "X",
        }
        "KC_C": {
            "key": "LT_C",
            "label": "C",
        }
        "KC_V": {
            "key": "LT_V",
            "label": "V",
        }
        "KC_B": {
            "key": "LT_B",
            "label": "B",
        }
        "KC_N": {
            "key": "LT_N",
            "label": "N",
        }
        "KC_M": {
            "key": "LT_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "LT_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "LT_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "LT_SLSH",
            "label": "/",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ~ │   │   │   │   │   │   │   │   │ ( │ ) │ _ │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ { │ } │  |  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │   │   │   │   │   │   │   │   │   │ : │ " │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │   │   │   │   │   │   │   │ < │ > │ ? │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(LT_GRV)": {
            "key": "LT_TILD",
            "label": "~",
        }
        "S(LT_9)": {
            "key": "LT_LPRN",
            "label": "(",
        }
        "S(LT_0)": {
            "key": "LT_RPRN",
            "label": ")",
        }
        "S(LT_MINS)": {
            "key": "LT_UNDS",
            "label": "_",
        }
        "S(LT_LBRC)": {
            "key": "LT_LCBR",
            "label": "{",
        }
        "S(LT_RBRC)": {
            "key": "LT_RCBR",
            "label": "}",
        }
        "S(LT_SCLN)": {
            "key": "LT_COLN",
            "label": ":",
        }
        "S(LT_QUOT)": {
            "key": "LT_DQUO",
            "label": "\"",
        }
        "S(LT_BSLS)": {
            "key": "LT_PIPE",
            "label": "|",
        }
        "S(LT_COMM)": {
            "key": "LT_LABK",
            "label": "<",
        }
        "S(LT_DOT)": {
            "key": "LT_RABK",
            "label": ">",
        }
        "S(LT_SLSH)": {
            "key": "LT_QUES",
            "label": "?",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │   │   │   │ = │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │ € │   │   │   │   │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │   │   │   │   │   │   │   │   │   │   │   │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │   │   │   │   │   │   │   │   │   │   │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(LT_AOGO)": {
            "key": "LT_1",
            "label": "1",
        }
        "ALGR(LT_CCAR)": {
            "key": "LT_2",
            "label": "2",
        }
        "ALGR(LT_EOGO)": {
            "key": "LT_3",
            "label": "3",
        }
        "ALGR(LT_EDOT)": {
            "key": "LT_4",
            "label": "4",
        }
        "ALGR(LT_IOGO)": {
            "key": "LT_5",
            "label": "5",
        }
        "ALGR(LT_SCAR)": {
            "key": "LT_6",
            "label": "6",
        }
        "ALGR(LT_UOGO)": {
            "key": "LT_7",
            "label": "7",
        }
        "ALGR(LT_UMAC)": {
            "key": "LT_8",
            "label": "8",
        }
        "ALGR(LT_ZCAR)": {
            "key": "LT_EQL",
            "label": "=",
        }
        "ALGR(LT_E)": {
            "key": "LT_EURO",
            "label": "€",
        }
/* Shift+AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │ ! │ @ │ # │ $ │ % │ ^ │ & │ * │   │   │   │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │   │   │   │   │   │   │   │   │   │   │   │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │   │   │   │   │   │   │   │   │   │   │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(ALGR(LT_AOGO))": {
            "key": "LT_EXLM",
            "label": "!",
        }
        "S(ALGR(LT_CCAR))": {
            "key": "LT_AT",
            "label": "@",
        }
        "S(ALGR(LT_EOGO))": {
            "key": "LT_HASH",
            "label": "#",
        }
        "S(ALGR(LT_EDOT))": {
            "key": "LT_DLR",
            "label": "$",
        }
        "S(ALGR(LT_IOGO))": {
            "key": "LT_PERC",
            "label": "%",
        }
        "S(ALGR(LT_SCAR))": {
            "key": "LT_CIRC",
            "label": "^",
        }
        "S(ALGR(LT_UOGO))": {
            "key": "LT_AMPR",
            "label": "&",
        }
        "S(ALGR(LT_UMAC))": {
            "key": "LT_ASTR",
            "label": "*",
        }
        "S(ALGR(LT_ZCAR))": {
            "key": "LT_PLUS",
            "label": "+",
        }
    }
}
