{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ " │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ * │ - │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ Ğ │ Ü │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ Ş │ İ │ , │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ Z │ X │ C │ V │ B │ N │ M │ Ö │ Ç │ . │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "TR_DQUO",
            "label": "\"",
        }
        "KC_1": {
            "key": "TR_1",
            "label": "1",
        }
        "KC_2": {
            "key": "TR_2",
            "label": "2",
        }
        "KC_3": {
            "key": "TR_3",
            "label": "3",
        }
        "KC_4": {
            "key": "TR_4",
            "label": "4",
        }
        "KC_5": {
            "key": "TR_5",
            "label": "5",
        }
        "KC_6": {
            "key": "TR_6",
            "label": "6",
        }
        "KC_7": {
            "key": "TR_7",
            "label": "7",
        }
        "KC_8": {
            "key": "TR_8",
            "label": "8",
        }
        "KC_9": {
            "key": "TR_9",
            "label": "9",
        }
        "KC_0": {
            "key": "TR_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "TR_ASTR",
            "label": "*",
        }
        "KC_EQL": {
            "key": "TR_MINS",
            "label": "-",
        }
        "KC_Q": {
            "key": "TR_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "TR_W",
            "label": "W",
        }
        "KC_E": {
            "key": "TR_E",
            "label": "E",
        }
        "KC_R": {
            "key": "TR_R",
            "label": "R",
        }
        "KC_T": {
            "key": "TR_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "TR_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "TR_U",
            "label": "U",
        }
        "KC_I": {
            "key": "TR_I",
            "label": "I",
        }
        "KC_O": {
            "key": "TR_O",
            "label": "O",
        }
        "KC_P": {
            "key": "TR_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "TR_GBRV",
            "label": "Ğ",
        }
        "KC_RBRC": {
            "key": "TR_UDIA",
            "label": "Ü",
        }
        "KC_A": {
            "key": "TR_A",
            "label": "A",
        }
        "KC_S": {
            "key": "TR_S",
            "label": "S",
        }
        "KC_D": {
            "key": "TR_D",
            "label": "D",
        }
        "KC_F": {
            "key": "TR_F",
            "label": "F",
        }
        "KC_G": {
            "key": "TR_G",
            "label": "G",
        }
        "KC_H": {
            "key": "TR_H",
            "label": "H",
        }
        "KC_J": {
            "key": "TR_J",
            "label": "J",
        }
        "KC_K": {
            "key": "TR_K",
            "label": "K",
        }
        "KC_L": {
            "key": "TR_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "TR_SCED",
            "label": "Ş",
        }
        "KC_QUOT": {
            "key": "TR_IDOT",
            "label": "İ",
        }
        "KC_NUHS": {
            "key": "TR_COMM",
            "label": ",",
        }
        "KC_NUBS": {
            "key": "TR_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "TR_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "TR_X",
            "label": "X",
        }
        "KC_C": {
            "key": "TR_C",
            "label": "C",
        }
        "KC_V": {
            "key": "TR_V",
            "label": "V",
        }
        "KC_B": {
            "key": "TR_B",
            "label": "B",
        }
        "KC_N": {
            "key": "TR_N",
            "label": "N",
        }
        "KC_M": {
            "key": "TR_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "TR_ODIA",
            "label": "Ö",
        }
        "KC_DOT": {
            "key": "TR_CCED",
            "label": "Ç",
        }
        "KC_SLSH": {
            "key": "TR_DOT",
            "label": ".",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ é │ ! │ ' │ ^ │ + │ % │ & │ / │ ( │ ) │ = │ ? │ _ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │ ; │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │   │   │   │ : │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(TR_DQUO)": {
            "key": "TR_EACU",
            "label": "é",
        }
        "S(TR_1)": {
            "key": "TR_EXLM",
            "label": "!",
        }
        "S(TR_2)": {
            "key": "TR_QUOT",
            "label": "'",
        }
        "S(TR_3)": {
            "key": "TR_CIRC",
            "label": "^ (dead)",
        }
        "S(TR_4)": {
            "key": "TR_PLUS",
            "label": "+",
        }
        "S(TR_5)": {
            "key": "TR_PERC",
            "label": "%",
        }
        "S(TR_6)": {
            "key": "TR_AMPR",
            "label": "&",
        }
        "S(TR_7)": {
            "key": "TR_SLSH",
            "label": "/",
        }
        "S(TR_8)": {
            "key": "TR_LPRN",
            "label": "(",
        }
        "S(TR_9)": {
            "key": "TR_RPRN",
            "label": ")",
        }
        "S(TR_0)": {
            "key": "TR_EQL",
            "label": "=",
        }
        "S(TR_ASTR)": {
            "key": "TR_QUES",
            "label": "?",
        }
        "S(TR_MINS)": {
            "key": "TR_UNDS",
            "label": "_",
        }
        "S(TR_COMM)": {
            "key": "TR_SCLN",
            "label": ";",
        }
        "S(TR_LABK)": {
            "key": "TR_RABK",
            "label": ">",
        }
        "S(TR_DOT)": {
            "key": "TR_COLN",
            "label": ":",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │   │ £ │ # │ $ │ ½ │   │ { │ [ │ ] │ } │ \ │ | │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ @ │   │ € │   │ ₺ │   │   │   │   │   │ ¨ │ ~ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ Æ │ ß │   │   │   │   │   │   │   │ ´ │   │ ` │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │   │   │   │   │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(TR_2)": {
            "key": "TR_PND",
            "label": "£",
        }
        "ALGR(TR_3)": {
            "key": "TR_HASH",
            "label": "#",
        }
        "ALGR(TR_4)": {
            "key": "TR_DLR",
            "label": "$",
        }
        "ALGR(TR_5)": {
            "key": "TR_HALF",
            "label": "½",
        }
        "ALGR(TR_7)": {
            "key": "TR_LCBR",
            "label": "{",
        }
        "ALGR(TR_8)": {
            "key": "TR_LBRC",
            "label": "[",
        }
        "ALGR(TR_9)": {
            "key": "TR_RBRC",
            "label": "]",
        }
        "ALGR(TR_0)": {
            "key": "TR_RCBR",
            "label": "}",
        }
        "ALGR(TR_ASTR)": {
            "key": "TR_BSLS",
            "label": "\\",
        }
        "ALGR(TR_MINS)": {
            "key": "TR_PIPE",
            "label": "|",
        }
        "ALGR(TR_Q)": {
            "key": "TR_AT",
            "label": "@",
        }
        "ALGR(TR_E)": {
            "key": "TR_EURO",
            "label": "€",
        }
        "ALGR(TR_T)": {
            "key": "TR_LIRA",
            "label": "₺",
        }
        "ALGR(TR_GBRV)": {
            "key": "TR_DIAE",
            "label": "¨ (dead)",
        }
        "ALGR(TR_UDIA)": {
            "key": "TR_TILD",
            "label": "~ (dead)",
        }
        "ALGR(TR_A)": {
            "key": "TR_AE",
            "label": "Æ",
        }
        "ALGR(TR_S)": {
            "key": "TR_SS",
            "label": "ß",
        }
        "ALGR(TR_SCED)": {
            "key": "TR_ACUT",
            "label": "´ (dead)",
        }
        "ALGR(TR_COMM)": {
            "key": "TR_GRV",
            "label": "` (dead)",
        }
    }
}
