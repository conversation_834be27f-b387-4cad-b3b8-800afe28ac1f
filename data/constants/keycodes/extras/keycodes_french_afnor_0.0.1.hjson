{
    "aliases": {
/* French AZERTY - AFNOR NF Z71-300
 *
 * A standard for the French keyboard
 *
 * The project was launched at the end of 2015 on the proposal of the General
 * Delegation for the French language and the languages of France (Ministry
 * of Culture), starting from the observation that the current "azerty"
 * keyboards constrain the writing of French, languages regional and European
 * languages with Latin alphabet.
 *
 * For the first time, a standard (NF Z71-300) defines the placement of
 * characters on the French keyboard. It offers two layouts, one of which
 * closely follows the QWERTY keyboard used by most people who write in French.
 *
 * However, it is in many ways superior to the old keyboard:
 *
 * - it contains all the characters required to enter text in French (for example É, œ and ")
 * - it is designed to be more ergonomic and allow faster typing
 * - it includes almost 60 additional characters for entering foreign languages, technical content, etc
 * - however, the characters remain easy to locate thanks to intuitive groupings
 *
 * Source: https://norme-azerty.fr
 */
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ @ │ à │ é │ è │ ê │ ( │ ) │ ‘ │ ’ │ « │ » │ ' │ ^ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ A │ Z │ E │ R │ T │ Y │ U │ I │ O │ P │ - │ + │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ Q │ S │ D │ F │ G │ H │ J │ K │ L │ M │ / │ * │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ W │ X │ C │ V │ B │ N │ . │ , │ : │ ; │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "FR_AT",
            "label": "@",
        }
        "KC_1": {
            "key": "FR_AGRV",
            "label": "à",
        }
        "KC_2": {
            "key": "FR_EACU",
            "label": "é",
        }
        "KC_3": {
            "key": "FR_EGRV",
            "label": "è",
        }
        "KC_4": {
            "key": "FR_ECIR",
            "label": "ê",
        }
        "KC_5": {
            "key": "FR_LPRN",
            "label": "(",
        }
        "KC_6": {
            "key": "FR_RPRN",
            "label": ")",
        }
        "KC_7": {
            "key": "FR_LSQU",
            "label": "‘",
        }
        "KC_8": {
            "key": "FR_RSQU",
            "label": "’",
        }
        "KC_9": {
            "key": "FR_LDAQ",
            "label": "«",
        }
        "KC_0": {
            "key": "FR_RDAQ",
            "label": "»",
        }
        "KC_MINS": {
            "key": "FR_QUOT",
            "label": "'",
        }
        "KC_EQL": {
            "key": "FR_DCIR",
            "label": "^ (dead)",
        }
        "KC_Q": {
            "key": "FR_A",
            "label": "A",
        }
        "KC_W": {
            "key": "FR_Z",
            "label": "Z",
        }
        "KC_E": {
            "key": "FR_E",
            "label": "E",
        }
        "KC_R": {
            "key": "FR_R",
            "label": "R",
        }
        "KC_T": {
            "key": "FR_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "FR_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "FR_U",
            "label": "U",
        }
        "KC_I": {
            "key": "FR_I",
            "label": "I",
        }
        "KC_O": {
            "key": "FR_O",
            "label": "O",
        }
        "KC_P": {
            "key": "FR_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "FR_MINS",
            "label": "-",
        }
        "KC_RBRC": {
            "key": "FR_PLUS",
            "label": "+",
        }
        "KC_A": {
            "key": "FR_Q",
            "label": "Q",
        }
        "KC_S": {
            "key": "FR_S",
            "label": "S",
        }
        "KC_D": {
            "key": "FR_D",
            "label": "D",
        }
        "KC_F": {
            "key": "FR_F",
            "label": "F",
        }
        "KC_G": {
            "key": "FR_G",
            "label": "G",
        }
        "KC_H": {
            "key": "FR_H",
            "label": "H",
        }
        "KC_J": {
            "key": "FR_J",
            "label": "J",
        }
        "KC_K": {
            "key": "FR_K",
            "label": "K",
        }
        "KC_L": {
            "key": "FR_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "FR_M",
            "label": "M",
        }
        "KC_QUOT": {
            "key": "FR_SLSH",
            "label": "/",
        }
        "KC_NUHS": {
            "key": "FR_ASTR",
            "label": "*",
        }
        "KC_NUBS": {
            "key": "FR_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "FR_W",
            "label": "W",
        }
        "KC_X": {
            "key": "FR_X",
            "label": "X",
        }
        "KC_C": {
            "key": "FR_C",
            "label": "C",
        }
        "KC_V": {
            "key": "FR_V",
            "label": "V",
        }
        "KC_B": {
            "key": "FR_B",
            "label": "B",
        }
        "KC_N": {
            "key": "FR_N",
            "label": "N",
        }
        "KC_M": {
            "key": "FR_DOT",
            "label": ".",
        }
        "KC_COMM": {
            "key": "FR_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "FR_COLN",
            "label": ":",
        }
        "KC_SLSH": {
            "key": "FR_SCLN",
            "label": ";",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ # │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ " │ ¨ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ – │ ± │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │ \ │ ½ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │ ? │ ! │ … │ = │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(FR_AT)": {
            "key": "FR_HASH",
            "label": "#",
        }
        "S(FR_AGRV)": {
            "key": "FR_1",
            "label": "1",
        }
        "S(FR_EACU)": {
            "key": "FR_2",
            "label": "2",
        }
        "S(FR_EGRV)": {
            "key": "FR_3",
            "label": "3",
        }
        "S(FR_ECIR)": {
            "key": "FR_4",
            "label": "4",
        }
        "S(FR_LPRN)": {
            "key": "FR_5",
            "label": "5",
        }
        "S(FR_RPRN)": {
            "key": "FR_6",
            "label": "6",
        }
        "S(FR_LSQU)": {
            "key": "FR_7",
            "label": "7",
        }
        "S(FR_RSQU)": {
            "key": "FR_8",
            "label": "8",
        }
        "S(FR_LDAQ)": {
            "key": "FR_9",
            "label": "9",
        }
        "S(FR_RDAQ)": {
            "key": "FR_0",
            "label": "0",
        }
        "S(FR_QUOT)": {
            "key": "FR_DQUO",
            "label": "\"",
        }
        "S(FR_DCIR)": {
            "key": "FR_DIAE",
            "label": "¨ (dead)",
        }
        "S(FR_MINS)": {
            "key": "FR_NDSH",
            "label": "–",
        }
        "S(FR_PLUS)": {
            "key": "FR_PLMN",
            "label": "±",
        }
        "S(FR_SLSH)": {
            "key": "FR_BSLS",
            "label": "\\",
        }
        "S(FR_ASTR)": {
            "key": "FR_HALF",
            "label": "½",
        }
        "S(FR_LABK)": {
            "key": "FR_RABK",
            "label": ">",
        }
        "S(FR_DOT)": {
            "key": "FR_QUES",
            "label": "?",
        }
        "S(FR_COMM)": {
            "key": "FR_EXLM",
            "label": "!",
        }
        "S(FR_COLN)": {
            "key": "FR_ELLP",
            "label": "…",
        }
        "S(FR_SCLN)": {
            "key": "FR_EQL",
            "label": "=",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ˘ │ § │ ´ │ ` │ & │ [ │ ] │ ¯ │ _ │ “ │ ” │ ° │ ˇ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ æ │ £ │ € │ ® │ { │ } │ ù │ ˙ │ œ │ % │ − │ † │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ θ │ ß │ $ │ ¤ │ µ │ Eu│   │ ∕ │ | │ ∞ │ ÷ │ × │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ ≤ │ ʒ │ © │ ç │ ¸ │ − │ ~ │ ¿ │ ¡ │ · │ ≃ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(FR_AT)": {
            "key": "FR_BREV",
            "label": "˘ (dead)",
        }
        "ALGR(FR_AGRV)": {
            "key": "FR_SECT",
            "label": "§",
        }
        "ALGR(FR_EACU)": {
            "key": "FR_ACUT",
            "label": "´ (dead)",
        }
        "ALGR(FR_EGRV)": {
            "key": "FR_GRV",
            "label": "` (dead)",
        }
        "ALGR(FR_ECIR)": {
            "key": "FR_AMPR",
            "label": "&",
        }
        "ALGR(FR_LPRN)": {
            "key": "FR_LBRC",
            "label": "[",
        }
        "ALGR(FR_RPRN)": {
            "key": "FR_RBRC",
            "label": "]",
        }
        "ALGR(FR_LSQU)": {
            "key": "FR_MACR",
            "label": "¯ (dead)",
        }
        "ALGR(FR_RSQU)": {
            "key": "FR_UNDS",
            "label": "_",
        }
        "ALGR(FR_LDAQ)": {
            "key": "FR_LDQU",
            "label": "“",
        }
        "ALGR(FR_RDAQ)": {
            "key": "FR_RDQU",
            "label": "”",
        }
        "ALGR(FR_QUOT)": {
            "key": "FR_DEG",
            "label": "°",
        }
        "ALGR(FR_DCIR)": {
            "key": "FR_CARN",
            "label": "ˇ (dead)",
        }
        "ALGR(FR_A)": {
            "key": "FR_AE",
            "label": "æ",
        }
        "ALGR(FR_Z)": {
            "key": "FR_PND",
            "label": "£",
        }
        "ALGR(FR_E)": {
            "key": "FR_EURO",
            "label": "€",
        }
        "ALGR(FR_R)": {
            "key": "FR_REGD",
            "label": "®",
        }
        "ALGR(FR_T)": {
            "key": "FR_LCBR",
            "label": "{",
        }
        "ALGR(FR_Y)": {
            "key": "FR_RCBR",
            "label": "}",
        }
        "ALGR(FR_U)": {
            "key": "FR_UGRV",
            "label": "ù",
        }
        "ALGR(FR_I)": {
            "key": "FR_DOTA",
            "label": "˙ (dead)",
        }
        "ALGR(FR_O)": {
            "key": "FR_OE",
            "label": "œ",
        }
        "ALGR(FR_P)": {
            "key": "FR_PERC",
            "label": "%",
        }
        "ALGR(FR_MINS)": {
            "key": "FR_MMNS",
            "label": "−",
        }
        "ALGR(FR_PLUS)": {
            "key": "FR_DAGG",
            "label": "†",
        }
        "ALGR(FR_Q)": {
            "key": "FR_THET",
            "label": "θ",
        }
        "ALGR(FR_S)": {
            "key": "FR_SS",
            "label": "ß",
        }
        "ALGR(FR_D)": {
            "key": "FR_DLR",
            "label": "$",
        }
        "ALGR(FR_F)": {
            "key": "FR_CURR",
            "label": "¤ (dead monetary key)",
        }
        "ALGR(FR_G)": {
            "key": "FR_DGRK",
            "label": "µ (dead Greek key)",
        }
        "ALGR(FR_H)": {
            "key": "FR_EU",
            "label": "Eu (dead European symbol key)",
        }
        "ALGR(FR_K)": {
            "key": "FR_DSLS",
            "label": "∕ (dead)",
        }
        "ALGR(FR_L)": {
            "key": "FR_PIPE",
            "label": "|",
        }
        "ALGR(FR_M)": {
            "key": "FR_INFN",
            "label": "∞",
        }
        "ALGR(FR_SLSH)": {
            "key": "FR_DIV",
            "label": "÷",
        }
        "ALGR(FR_ASTR)": {
            "key": "FR_MUL",
            "label": "×",
        }
        "ALGR(FR_LABK)": {
            "key": "FR_LEQL",
            "label": "≤",
        }
        "ALGR(FR_W)": {
            "key": "FR_EZH",
            "label": "ʒ",
        }
        "ALGR(FR_X)": {
            "key": "FR_COPY",
            "label": "©",
        }
        "ALGR(FR_C)": {
            "key": "FR_CCED",
            "label": "ç",
        }
        "ALGR(FR_V)": {
            "key": "FR_CEDL",
            "label": "¸ (dead)",
        }
        "ALGR(FR_B)": {
            "key": "FR_DMNS",
            "label": "− (dead)",
        }
        "ALGR(FR_N)": {
            "key": "FR_DTIL",
            "label": "~ (dead)",
        }
        "ALGR(FR_DOT)": {
            "key": "FR_IQUE",
            "label": "¿",
        }
        "ALGR(FR_COMM)": {
            "key": "FR_IEXL",
            "label": "¡",
        }
        "ALGR(FR_COLN)": {
            "key": "FR_MDDT",
            "label": "·",
        }
        "ALGR(FR_SCLN)": {
            "key": "FR_AEQL",
            "label": "≃",
        }
/* Shift+AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ̑  │   │   │   │   │ ˝ │ ̏  │   │ — │ ‹ │ › │ ˚ │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │ ™ │   │   │ ̣  │   │ ‰ │ ‑ │ ‡ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │ ˍ │   │   │   │   │ √ │ ¼ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ ≥ │   │   │   │ ˛ │   │   │   │ ̦  │   │ ≠ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(ALGR(FR_AT))": {
            "key": "FR_IBRV",
            "label": "̑ (dead)",
        }
        "S(ALGR(FR_LPRN))": {
            "key": "FR_DACU",
            "label": "˝ (dead)",
        }
        "S(ALGR(FR_RPRN))": {
            "key": "FR_DGRV",
            "label": "̏ (dead)",
        }
        "S(ALGR(FR_RSQU))": {
            "key": "FR_MDSH",
            "label": "—",
        }
        "S(ALGR(FR_LDAQ))": {
            "key": "FR_LSAQ",
            "label": "‹",
        }
        "S(ALGR(FR_RDAQ))": {
            "key": "FR_RSAQ",
            "label": "›",
        }
        "S(ALGR(FR_QUOT))": {
            "key": "FR_RNGA",
            "label": "˚ (dead)",
        }
        "S(ALGR(FR_T))": {
            "key": "FR_TM",
            "label": "™",
        }
        "S(ALGR(FR_I))": {
            "key": "FR_DOTB",
            "label": "̣ (dead)",
        }
        "S(ALGR(FR_P))": {
            "key": "FR_PERM",
            "label": "‰",
        }
        "S(ALGR(FR_MINS))": {
            "key": "FR_NBHY",
            "label": "‑ (non-breaking hyphen)",
        }
        "S(ALGR(FR_PLUS))": {
            "key": "FR_DDAG",
            "label": "‡",
        }
        "S(ALGR(FR_H))": {
            "key": "FR_MACB",
            "label": "ˍ (dead)",
        }
        "S(ALGR(FR_SLSH))": {
            "key": "FR_SQRT",
            "label": "√",
        }
        "S(ALGR(FR_ASTR))": {
            "key": "FR_QRTR",
            "label": "¼",
        }
        "S(ALGR(FR_LABK))": {
            "key": "FR_GEQL",
            "label": "≥",
        }
        "S(ALGR(FR_V))": {
            "key": "FR_OGON",
            "label": "˛ (dead)",
        }
        "S(ALGR(FR_COMM))": {
            "key": "FR_DCMM",
            "label": "̦ (dead)",
        }
        "S(ALGR(FR_SCLN))": {
            "key": "FR_NEQL",
            "label": "≠",
        }
    }
}
