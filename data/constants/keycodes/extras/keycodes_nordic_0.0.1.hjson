{
    "aliases": {
        "KC_GRV": {
            "key": "NO_HALF"
        }
        "KC_MINS": {
            "key": "NO_PLUS"
        }
        "KC_EQL": {
            "key": "NO_ACUT"
        }
        "KC_LBRC": {
            "key": "NO_AM"
        }
        "KC_RBRC": {
            "key": "NO_QUOT",
            "label": "this is the \"umlaut\" char on Nordic keyboards, Apple layout",
        }
        "KC_SCLN": {
            "key": "NO_AE"
        }
        "KC_QUOT": {
            "key": "NO_OSLH"
        }
        "KC_NUHS": {
            "key": "NO_APOS"
        }
        "KC_NUBS": {
            "key": "NO_LESS"
        }
        "KC_SLSH": {
            "key": "NO_MINS"
        }
        "LSFT(NO_HALF)": {
            "key": "NO_SECT"
        }
        "LSFT(KC_2)": {
            "key": "NO_QUO2"
        }
        "LSFT(KC_4)": {
            "key": "NO_BULT"
        }
        "LSFT(KC_6)": {
            "key": "NO_AMPR"
        }
        "LSFT(KC_7)": {
            "key": "NO_SLSH"
        }
        "LSFT(KC_8)": {
            "key": "NO_LPRN"
        }
        "LSFT(KC_9)": {
            "key": "NO_RPRN"
        }
        "LSFT(KC_0)": {
            "key": "NO_EQL"
        }
        "LSFT(NO_PLUS)": {
            "key": "NO_QUES"
        }
        "LSFT(NO_ACUT)": {
            "key": "NO_GRV"
        }
        "LSFT(NO_QUOT)": {
            "key": "NO_CIRC"
        }
        "LSFT(NO_LESS)": {
            "key": "NO_GRTR"
        }
        "LSFT(KC_COMM)": {
            "key": "NO_SCLN"
        }
        "LSFT(KC_DOT)": {
            "key": "NO_COLN"
        }
        "LSFT(NO_MINS)": {
            "key": "NO_UNDS"
        }
        "ALGR(KC_2)": {
            "key": "NO_AT"
        }
        "ALGR(KC_3)": {
            "key": "NO_PND"
        }
        "ALGR(KC_4)": {
            "key": "NO_DLR"
        }
        "ALGR(KC_7)": {
            "key": "NO_LCBR"
        }
        "ALGR(KC_8)": {
            "key": "NO_LBRC"
        }
        "ALGR(KC_9)": {
            "key": "NO_RBRC"
        }
        "ALGR(KC_0)": {
            "key": "NO_RCBR"
        }
        "ALGR(KC_NUBS)": {
            "key": "NO_PIPE"
        }
        "ALGR(KC_E)": {
            "key": "NO_EURO"
        }
        "ALGR(NO_QUOT)": {
            "key": "NO_TILD"
        }
        "ALGR(KC_MINS)": {
            "key": "NO_BSLS"
        }
        "ALGR(KC_M)": {
            "key": "NO_MU"
        }
    }
}
