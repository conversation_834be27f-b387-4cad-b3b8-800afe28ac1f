{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ' │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ - │ = │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Й │ Ц │ У │ К │ Е │ Н │ Г │ Ш │ Щ │ З │ Х │ Ї │  \  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │ Ф │ І │ В │ А │ П │ Р │ О │ Л │ Д │ Ж │ Є │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │ Я │ Ч │ С │ М │ И │ Т │ Ь │ Б │ Ю │ . │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "UA_QUOT",
            "label": "'",
        }
        "KC_1": {
            "key": "UA_1",
            "label": "1",
        }
        "KC_2": {
            "key": "UA_2",
            "label": "2",
        }
        "KC_3": {
            "key": "UA_3",
            "label": "3",
        }
        "KC_4": {
            "key": "UA_4",
            "label": "4",
        }
        "KC_5": {
            "key": "UA_5",
            "label": "5",
        }
        "KC_6": {
            "key": "UA_6",
            "label": "6",
        }
        "KC_7": {
            "key": "UA_7",
            "label": "7",
        }
        "KC_8": {
            "key": "UA_8",
            "label": "8",
        }
        "KC_9": {
            "key": "UA_9",
            "label": "9",
        }
        "KC_0": {
            "key": "UA_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "UA_MINS",
            "label": "-",
        }
        "KC_EQL": {
            "key": "UA_EQL",
            "label": "=",
        }
        "KC_Q": {
            "key": "UA_YOT",
            "label": "Й",
        }
        "KC_W": {
            "key": "UA_TSE",
            "label": "Ц",
        }
        "KC_E": {
            "key": "UA_U",
            "label": "У",
        }
        "KC_R": {
            "key": "UA_KA",
            "label": "К",
        }
        "KC_T": {
            "key": "UA_E",
            "label": "Е",
        }
        "KC_Y": {
            "key": "UA_EN",
            "label": "Н",
        }
        "KC_U": {
            "key": "UA_HE",
            "label": "Г",
        }
        "KC_I": {
            "key": "UA_SHA",
            "label": "Ш",
        }
        "KC_O": {
            "key": "UA_SHCH",
            "label": "Щ",
        }
        "KC_P": {
            "key": "UA_ZE",
            "label": "З",
        }
        "KC_LBRC": {
            "key": "UA_KHA",
            "label": "Х",
        }
        "KC_RBRC": {
            "key": "UA_YI",
            "label": "Ї",
        }
        "KC_BSLS": {
            "key": "UA_BSLS",
            "label": "\\",
        }
        "KC_A": {
            "key": "UA_EF",
            "label": "Ф",
        }
        "KC_S": {
            "key": "UA_I",
            "label": "І",
        }
        "KC_D": {
            "key": "UA_VE",
            "label": "В",
        }
        "KC_F": {
            "key": "UA_A",
            "label": "А",
        }
        "KC_G": {
            "key": "UA_PE",
            "label": "П",
        }
        "KC_H": {
            "key": "UA_ER",
            "label": "Р",
        }
        "KC_J": {
            "key": "UA_O",
            "label": "О",
        }
        "KC_K": {
            "key": "UA_EL",
            "label": "Л",
        }
        "KC_L": {
            "key": "UA_DE",
            "label": "Д",
        }
        "KC_SCLN": {
            "key": "UA_ZHE",
            "label": "Ж",
        }
        "KC_QUOT": {
            "key": "UA_YE",
            "label": "Є",
        }
        "KC_Z": {
            "key": "UA_YA",
            "label": "Я",
        }
        "KC_X": {
            "key": "UA_CHE",
            "label": "Ч",
        }
        "KC_C": {
            "key": "UA_ES",
            "label": "С",
        }
        "KC_V": {
            "key": "UA_EM",
            "label": "М",
        }
        "KC_B": {
            "key": "UA_Y",
            "label": "И",
        }
        "KC_N": {
            "key": "UA_TE",
            "label": "Т",
        }
        "KC_M": {
            "key": "UA_SOFT",
            "label": "Ь",
        }
        "KC_COMM": {
            "key": "UA_BE",
            "label": "Б",
        }
        "KC_DOT": {
            "key": "UA_YU",
            "label": "Ю",
        }
        "KC_SLSH": {
            "key": "UA_DOT",
            "label": ".",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ₴ │ ! │ " │ № │ ; │ % │ : │ ? │ * │ ( │ ) │ _ │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │   │  /  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │   │   │   │   │   │   │   │   │   │   │   │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │   │   │   │   │   │   │   │   │   │ , │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(UA_QUOT)": {
            "key": "UA_HRYV",
            "label": "₴",
        }
        "S(UA_1)": {
            "key": "UA_EXLM",
            "label": "!",
        }
        "S(UA_2)": {
            "key": "UA_DQUO",
            "label": "\"",
        }
        "S(UA_3)": {
            "key": "UA_NUM",
            "label": "№",
        }
        "S(UA_4)": {
            "key": "UA_SCLN",
            "label": ";",
        }
        "S(UA_5)": {
            "key": "UA_PERC",
            "label": "%",
        }
        "S(UA_6)": {
            "key": "UA_COLN",
            "label": ":",
        }
        "S(UA_7)": {
            "key": "UA_QUES",
            "label": "?",
        }
        "S(UA_8)": {
            "key": "UA_ASTR",
            "label": "*",
        }
        "S(UA_9)": {
            "key": "UA_LPRN",
            "label": "(",
        }
        "S(UA_0)": {
            "key": "UA_RPRN",
            "label": ")",
        }
        "S(UA_MINS)": {
            "key": "UA_UNDS",
            "label": "_",
        }
        "S(UA_EQL)": {
            "key": "UA_PLUS",
            "label": "+",
        }
        "S(UA_BSLS)": {
            "key": "UA_SLSH",
            "label": "/",
        }
        "S(UA_DOT)": {
            "key": "UA_COMM",
            "label": ",",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │   │   │   │   │   │   │   │   │   │   │   │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │ ґ │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │   │   │   │   │   │   │   │   │   │   │   │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │   │   │   │   │   │   │   │   │   │   │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(UA_HE)": {
            "key": "UA_GE",
            "label": "ґ",
        }
    }
}
