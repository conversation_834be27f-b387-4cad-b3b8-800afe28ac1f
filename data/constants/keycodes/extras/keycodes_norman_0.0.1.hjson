{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ` │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ - │ = │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ D │ F │ K │ J │ U │ R │ L │ ; │ [ │ ] │  \  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │ A │ S │ E │ T │ G │ Y │ N │ I │ O │ H │ ' │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │ Z │ X │ C │ V │ B │ P │ M │ , │ . │ / │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "NM_GRV",
            "label": "`",
        }
        "KC_1": {
            "key": "NM_1",
            "label": "1",
        }
        "KC_2": {
            "key": "NM_2",
            "label": "2",
        }
        "KC_3": {
            "key": "NM_3",
            "label": "3",
        }
        "KC_4": {
            "key": "NM_4",
            "label": "4",
        }
        "KC_5": {
            "key": "NM_5",
            "label": "5",
        }
        "KC_6": {
            "key": "NM_6",
            "label": "6",
        }
        "KC_7": {
            "key": "NM_7",
            "label": "7",
        }
        "KC_8": {
            "key": "NM_8",
            "label": "8",
        }
        "KC_9": {
            "key": "NM_9",
            "label": "9",
        }
        "KC_0": {
            "key": "NM_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "NM_MINS",
            "label": "-",
        }
        "KC_EQL": {
            "key": "NM_EQL",
            "label": "=",
        }
        "KC_Q": {
            "key": "NM_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "NM_W",
            "label": "W",
        }
        "KC_E": {
            "key": "NM_D",
            "label": "D",
        }
        "KC_R": {
            "key": "NM_F",
            "label": "F",
        }
        "KC_T": {
            "key": "NM_K",
            "label": "K",
        }
        "KC_Y": {
            "key": "NM_J",
            "label": "J",
        }
        "KC_U": {
            "key": "NM_U",
            "label": "U",
        }
        "KC_I": {
            "key": "NM_R",
            "label": "R",
        }
        "KC_O": {
            "key": "NM_L",
            "label": "L",
        }
        "KC_P": {
            "key": "NM_SCLN",
            "label": ";",
        }
        "KC_LBRC": {
            "key": "NM_LBRC",
            "label": "[",
        }
        "KC_RBRC": {
            "key": "NM_RBRC",
            "label": "]",
        }
        "KC_BSLS": {
            "key": "NM_BSLS",
            "label": "\\",
        }
        "KC_A": {
            "key": "NM_A",
            "label": "A",
        }
        "KC_S": {
            "key": "NM_S",
            "label": "S",
        }
        "KC_D": {
            "key": "NM_E",
            "label": "E",
        }
        "KC_F": {
            "key": "NM_T",
            "label": "T",
        }
        "KC_G": {
            "key": "NM_G",
            "label": "G",
        }
        "KC_H": {
            "key": "NM_Y",
            "label": "Y",
        }
        "KC_J": {
            "key": "NM_N",
            "label": "N",
        }
        "KC_K": {
            "key": "NM_I",
            "label": "I",
        }
        "KC_L": {
            "key": "NM_O",
            "label": "O",
        }
        "KC_SCLN": {
            "key": "NM_H",
            "label": "H",
        }
        "KC_QUOT": {
            "key": "NM_QUOT",
            "label": "'",
        }
        "KC_Z": {
            "key": "NM_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "NM_X",
            "label": "X",
        }
        "KC_C": {
            "key": "NM_C",
            "label": "C",
        }
        "KC_V": {
            "key": "NM_V",
            "label": "V",
        }
        "KC_B": {
            "key": "NM_B",
            "label": "B",
        }
        "KC_N": {
            "key": "NM_P",
            "label": "P",
        }
        "KC_M": {
            "key": "NM_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "NM_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "NM_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "NM_SLSH",
            "label": "/",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ~ │ ! │ @ │ # │ $ │ % │ ^ │ & │ * │ ( │ ) │ _ │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │ : │ { │ } │  |  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │   │   │   │   │   │   │   │   │   │   │ " │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │   │   │   │   │   │   │   │ < │ > │ ? │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(NM_GRV)": {
            "key": "NM_TILD",
            "label": "~",
        }
        "S(NM_1)": {
            "key": "NM_EXLM",
            "label": "!",
        }
        "S(NM_2)": {
            "key": "NM_AT",
            "label": "@",
        }
        "S(NM_3)": {
            "key": "NM_HASH",
            "label": "#",
        }
        "S(NM_4)": {
            "key": "NM_DLR",
            "label": "$",
        }
        "S(NM_5)": {
            "key": "NM_PERC",
            "label": "%",
        }
        "S(NM_6)": {
            "key": "NM_CIRC",
            "label": "^",
        }
        "S(NM_7)": {
            "key": "NM_AMPR",
            "label": "&",
        }
        "S(NM_8)": {
            "key": "NM_ASTR",
            "label": "*",
        }
        "S(NM_9)": {
            "key": "NM_LPRN",
            "label": "(",
        }
        "S(NM_0)": {
            "key": "NM_RPRN",
            "label": ")",
        }
        "S(NM_MINS)": {
            "key": "NM_UNDS",
            "label": "_",
        }
        "S(NM_EQL)": {
            "key": "NM_PLUS",
            "label": "+",
        }
        "S(NM_SCLN)": {
            "key": "NM_COLN",
            "label": ":",
        }
        "S(NM_LBRC)": {
            "key": "NM_LCBR",
            "label": "{",
        }
        "S(NM_RBRC)": {
            "key": "NM_RCBR",
            "label": "}",
        }
        "S(NM_BSLS)": {
            "key": "NM_PIPE",
            "label": "|",
        }
        "S(NM_QUOT)": {
            "key": "NM_DQUO",
            "label": "\"",
        }
        "S(NM_COMM)": {
            "key": "NM_LABK",
            "label": "<",
        }
        "S(NM_DOT)": {
            "key": "NM_RABK",
            "label": ">",
        }
        "S(NM_SLSH)": {
            "key": "NM_QUES",
            "label": "?",
        }
    }
}
