{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ; │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ - │ = │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ / │ ' │ פ │ ם │ ן │ ו │ ט │ א │ ר │ ק │ ] │ [ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ ף │ ך │ ל │ ח │ י │ ע │ כ │ ג │ ד │ ש │ , │ \ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │ ץ │ ת │ צ │ מ │ נ │ ה │ ב │ ס │ ז │ . │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "IL_SCLN",
            "label": ";",
        }
        "KC_1": {
            "key": "IL_1",
            "label": "1",
        }
        "KC_2": {
            "key": "IL_2",
            "label": "2",
        }
        "KC_3": {
            "key": "IL_3",
            "label": "3",
        }
        "KC_4": {
            "key": "IL_4",
            "label": "4",
        }
        "KC_5": {
            "key": "IL_5",
            "label": "5",
        }
        "KC_6": {
            "key": "IL_6",
            "label": "6",
        }
        "KC_7": {
            "key": "IL_7",
            "label": "7",
        }
        "KC_8": {
            "key": "IL_8",
            "label": "8",
        }
        "KC_9": {
            "key": "IL_9",
            "label": "9",
        }
        "KC_0": {
            "key": "IL_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "IL_MINS",
            "label": "-",
        }
        "KC_EQL": {
            "key": "IL_EQL",
            "label": "=",
        }
        "KC_Q": {
            "key": "IL_SLSH",
            "label": "/",
        }
        "KC_W": {
            "key": "IL_QUOT",
            "label": "'",
        }
        "KC_E": {
            "key": "IL_QOF",
            "label": "ק",
        }
        "KC_R": {
            "key": "IL_RESH",
            "label": "ר",
        }
        "KC_T": {
            "key": "IL_ALEF",
            "label": "א",
        }
        "KC_Y": {
            "key": "IL_TET",
            "label": "ט",
        }
        "KC_U": {
            "key": "IL_VAV",
            "label": "ו",
        }
        "KC_I": {
            "key": "IL_FNUN",
            "label": "ן",
        }
        "KC_O": {
            "key": "IL_FMEM",
            "label": "ם",
        }
        "KC_P": {
            "key": "IL_PE",
            "label": "פ",
        }
        "KC_LBRC": {
            "key": "IL_RBRC",
            "label": "]",
        }
        "KC_RBRC": {
            "key": "IL_LBRC",
            "label": "[",
        }
        "KC_A": {
            "key": "IL_SHIN",
            "label": "ש",
        }
        "KC_S": {
            "key": "IL_DALT",
            "label": "ד",
        }
        "KC_D": {
            "key": "IL_GIML",
            "label": "ג",
        }
        "KC_F": {
            "key": "IL_KAF",
            "label": "כ",
        }
        "KC_G": {
            "key": "IL_AYIN",
            "label": "ע",
        }
        "KC_H": {
            "key": "IL_YOD",
            "label": "י",
        }
        "KC_J": {
            "key": "IL_HET",
            "label": "ח",
        }
        "KC_K": {
            "key": "IL_LAMD",
            "label": "ל",
        }
        "KC_L": {
            "key": "IL_FKAF",
            "label": "ך",
        }
        "KC_SCLN": {
            "key": "IL_FPE",
            "label": "ף",
        }
        "KC_QUOT": {
            "key": "IL_COMM",
            "label": ",",
        }
        "KC_NUHS": {
            "key": "IL_BSLS",
            "label": "\\",
        }
        "KC_Z": {
            "key": "IL_ZAYN",
            "label": "ז",
        }
        "KC_X": {
            "key": "IL_SMKH",
            "label": "ס",
        }
        "KC_C": {
            "key": "IL_BET",
            "label": "ב",
        }
        "KC_V": {
            "key": "IL_HE",
            "label": "ה",
        }
        "KC_B": {
            "key": "IL_NUN",
            "label": "נ",
        }
        "KC_N": {
            "key": "IL_MEM",
            "label": "מ",
        }
        "KC_M": {
            "key": "IL_TSDI",
            "label": "צ",
        }
        "KC_COMM": {
            "key": "IL_TAV",
            "label": "ת",
        }
        "KC_DOT": {
            "key": "IL_FTSD",
            "label": "ץ",
        }
        "KC_SLSH": {
            "key": "IL_DOT",
            "label": ".",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ~ │ ! │ @ │ # │ $ │ % │ ^ │ & │ * │ ) │ ( │ _ │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ } │ { │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │ : │ " │ | │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │   │   │   │   │ > │ < │ ? │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(IL_SCLN)": {
            "key": "IL_TILD",
            "label": "~",
        }
        "S(IL_1)": {
            "key": "IL_EXLM",
            "label": "!",
        }
        "S(IL_2)": {
            "key": "IL_AT",
            "label": "@",
        }
        "S(IL_3)": {
            "key": "IL_PND",
            "label": "#",
        }
        "S(IL_4)": {
            "key": "IL_DLR",
            "label": "$",
        }
        "S(IL_5)": {
            "key": "IL_PERC",
            "label": "%",
        }
        "S(IL_6)": {
            "key": "IL_CIRC",
            "label": "^",
        }
        "S(IL_7)": {
            "key": "IL_AMPR",
            "label": "&",
        }
        "S(IL_8)": {
            "key": "IL_ASTR",
            "label": "*",
        }
        "S(IL_9)": {
            "key": "IL_RPRN",
            "label": ")",
        }
        "S(IL_0)": {
            "key": "IL_LPRN",
            "label": "(",
        }
        "S(IL_MINS)": {
            "key": "IL_UNDS",
            "label": "_",
        }
        "S(IL_EQL)": {
            "key": "IL_PLUS",
            "label": "+",
        }
        "S(IL_RBRC)": {
            "key": "IL_RCBR",
            "label": "}",
        }
        "S(IL_LBRC)": {
            "key": "IL_LCBR",
            "label": "{",
        }
        "S(IL_FPE)": {
            "key": "IL_COLN",
            "label": ":",
        }
        "S(IL_COMM)": {
            "key": "IL_DQUO",
            "label": "\"",
        }
        "S(IL_BSLS)": {
            "key": "IL_PIPE",
            "label": "|",
        }
        "S(IL_TAV)": {
            "key": "IL_RABK",
            "label": ">",
        }
        "S(IL_FTSD)": {
            "key": "IL_LABK",
            "label": "<",
        }
        "S(IL_DOT)": {
            "key": "IL_QUES",
            "label": "?",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │   │   │ € │ ₪ │ ° │   │   │ × │   │   │   │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │ װ │   │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │ ײ │ ױ │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │   │   │   │   │   │   │ ÷ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(IL_3)": {
            "key": "IL_EURO",
            "label": "€",
        }
        "ALGR(IL_4)": {
            "key": "IL_SHKL",
            "label": "₪",
        }
        "ALGR(IL_5)": {
            "key": "IL_DEG",
            "label": "°",
        }
        "ALGR(IL_8)": {
            "key": "IL_MUL",
            "label": "×",
        }
        "ALGR(IL_TET)": {
            "key": "IL_DVAV",
            "label": "װ",
        }
        "ALGR(IL_AYIN)": {
            "key": "IL_VYOD",
            "label": "ױ",
        }
        "ALGR(IL_YOD)": {
            "key": "IL_DYOD",
            "label": "ײ",
        }
        "ALGR(IL_DOT)": {
            "key": "IL_DIV",
            "label": "÷",
        }
    }
}
