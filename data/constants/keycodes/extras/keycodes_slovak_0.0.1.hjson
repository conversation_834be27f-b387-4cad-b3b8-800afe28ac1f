{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ; │ + │ ľ │ š │ č │ ť │ ž │ ý │ á │ í │ é │ = │ ´ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Z │ U │ I │ O │ P │ ú │ ä │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ ô │ § │ ň │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ & │ Y │ X │ C │ V │ B │ N │ M │ , │ . │ - │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "SK_SCLN",
            "label": ";",
        }
        "KC_1": {
            "key": "SK_PLUS",
            "label": "+",
        }
        "KC_2": {
            "key": "SK_LCAR",
            "label": "ľ",
        }
        "KC_3": {
            "key": "SK_SCAR",
            "label": "š",
        }
        "KC_4": {
            "key": "SK_CCAR",
            "label": "č",
        }
        "KC_5": {
            "key": "SK_TCAR",
            "label": "ť",
        }
        "KC_6": {
            "key": "SK_ZCAR",
            "label": "ž",
        }
        "KC_7": {
            "key": "SK_YACU",
            "label": "ý",
        }
        "KC_8": {
            "key": "SK_AACU",
            "label": "á",
        }
        "KC_9": {
            "key": "SK_IACU",
            "label": "í",
        }
        "KC_0": {
            "key": "SK_EACU",
            "label": "é",
        }
        "KC_MINS": {
            "key": "SK_EQL",
            "label": "=",
        }
        "KC_EQL": {
            "key": "SK_ACUT",
            "label": "´ (dead)",
        }
        "KC_Q": {
            "key": "SK_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "SK_W",
            "label": "W",
        }
        "KC_E": {
            "key": "SK_E",
            "label": "E",
        }
        "KC_R": {
            "key": "SK_R",
            "label": "R",
        }
        "KC_T": {
            "key": "SK_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "SK_Z",
            "label": "Z",
        }
        "KC_U": {
            "key": "SK_U",
            "label": "U",
        }
        "KC_I": {
            "key": "SK_I",
            "label": "I",
        }
        "KC_O": {
            "key": "SK_O",
            "label": "O",
        }
        "KC_P": {
            "key": "SK_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "SK_UACU",
            "label": "ú",
        }
        "KC_RBRC": {
            "key": "SK_ADIA",
            "label": "ä",
        }
        "KC_A": {
            "key": "SK_A",
            "label": "A",
        }
        "KC_S": {
            "key": "SK_S",
            "label": "S",
        }
        "KC_D": {
            "key": "SK_D",
            "label": "D",
        }
        "KC_F": {
            "key": "SK_F",
            "label": "F",
        }
        "KC_G": {
            "key": "SK_G",
            "label": "G",
        }
        "KC_H": {
            "key": "SK_H",
            "label": "H",
        }
        "KC_J": {
            "key": "SK_J",
            "label": "J",
        }
        "KC_K": {
            "key": "SK_K",
            "label": "K",
        }
        "KC_L": {
            "key": "SK_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "SK_OCIR",
            "label": "ô",
        }
        "KC_QUOT": {
            "key": "SK_SECT",
            "label": "§",
        }
        "KC_NUHS": {
            "key": "SK_NCAR",
            "label": "ň",
        }
        "KC_NUBS": {
            "key": "SK_AMPR",
            "label": "&",
        }
        "KC_Z": {
            "key": "SK_Y",
            "label": "Y",
        }
        "KC_X": {
            "key": "SK_X",
            "label": "X",
        }
        "KC_C": {
            "key": "SK_C",
            "label": "C",
        }
        "KC_V": {
            "key": "SK_V",
            "label": "V",
        }
        "KC_B": {
            "key": "SK_B",
            "label": "B",
        }
        "KC_N": {
            "key": "SK_N",
            "label": "N",
        }
        "KC_M": {
            "key": "SK_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "SK_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "SK_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "SK_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ° │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ % │ ˇ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ / │ ( │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │ " │ ! │ ) │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ * │   │   │   │   │   │   │   │ ? │ : │ _ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(SK_SCLN)": {
            "key": "SK_RNGA",
            "label": "° (dead)",
        }
        "S(SK_PLUS)": {
            "key": "SK_1",
            "label": "1",
        }
        "S(SK_LCAR)": {
            "key": "SK_2",
            "label": "2",
        }
        "S(SK_SCAR)": {
            "key": "SK_3",
            "label": "3",
        }
        "S(SK_CCAR)": {
            "key": "SK_4",
            "label": "4",
        }
        "S(SK_TCAR)": {
            "key": "SK_5",
            "label": "5",
        }
        "S(SK_ZCAR)": {
            "key": "SK_6",
            "label": "6",
        }
        "S(SK_YACU)": {
            "key": "SK_7",
            "label": "7",
        }
        "S(SK_AACU)": {
            "key": "SK_8",
            "label": "8",
        }
        "S(SK_IACU)": {
            "key": "SK_9",
            "label": "9",
        }
        "S(SK_EACU)": {
            "key": "SK_0",
            "label": "0",
        }
        "S(SK_EQL)": {
            "key": "SK_PERC",
            "label": "%",
        }
        "S(SK_ACUT)": {
            "key": "SK_CARN",
            "label": "ˇ (dead)",
        }
        "S(SK_UACU)": {
            "key": "SK_SLSH",
            "label": "/",
        }
        "S(SK_ADIA)": {
            "key": "SK_LPRN",
            "label": "(",
        }
        "S(SK_OCIR)": {
            "key": "SK_DQUO",
            "label": "\"",
        }
        "S(SK_SECT)": {
            "key": "SK_EXLM",
            "label": "!",
        }
        "S(SK_NCAR)": {
            "key": "SK_RPRN",
            "label": ")",
        }
        "S(SK_AMPR)": {
            "key": "SK_ASTR",
            "label": "*",
        }
        "S(SK_COMM)": {
            "key": "SK_QUES",
            "label": "?",
        }
        "S(SK_DOT)": {
            "key": "SK_COLN",
            "label": ":",
        }
        "S(SK_MINS)": {
            "key": "SK_UNDS",
            "label": "_",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │ ~ │   │ ^ │ ˘ │ ° │ ˛ │ ` │ ˙ │   │ ˝ │ ¨ │ ¸ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ \ │ | │ € │   │   │   │   │   │   │ ' │ ÷ │ × │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │ đ │ Đ │ [ │ ] │   │   │ ł │ Ł │ $ │ ß │ ¤ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ > │ # │   │ @ │ { │ } │   │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(SK_PLUS)": {
            "key": "SK_TILD",
            "label": "~",
        }
        "ALGR(SK_SCAR)": {
            "key": "SK_CIRC",
            "label": "^ (dead)",
        }
        "ALGR(SK_CCAR)": {
            "key": "SK_BREV",
            "label": "˘ (dead)",
        }
        "ALGR(SK_TCAR)": {
            "key": "SK_OGON",
            "label": "˛ (dead)",
        }
        "ALGR(SK_ZCAR)": {
            "key": "SK_GRV",
            "label": "`",
        }
        "ALGR(SK_YACU)": {
            "key": "SK_DOTA",
            "label": "˙ (dead)",
        }
        "ALGR(SK_EACU)": {
            "key": "SK_DACU",
            "label": "˝ (dead)",
        }
        "ALGR(SK_EQL)": {
            "key": "SK_DIAE",
            "label": "¨ (dead)",
        }
        "ALGR(SK_ACUT)": {
            "key": "SK_CEDL",
            "label": "¸ (dead)",
        }
        "ALGR(SK_Q)": {
            "key": "SK_BSLS",
            "label": "\\",
        }
        "ALGR(SK_W)": {
            "key": "SK_PIPE",
            "label": "|",
        }
        "ALGR(SK_E)": {
            "key": "SK_EURO",
            "label": "€",
        }
        "ALGR(SK_P)": {
            "key": "SK_QUOT",
            "label": "'",
        }
        "ALGR(SK_UACU)": {
            "key": "SK_DIV",
            "label": "÷",
        }
        "ALGR(SK_ADIA)": {
            "key": "SK_MUL",
            "label": "×",
        }
        "ALGR(SK_S)": {
            "key": "SK_LDST",
            "label": "đ",
        }
        "ALGR(SK_D)": {
            "key": "SK_CDST",
            "label": "Đ",
        }
        "ALGR(SK_F)": {
            "key": "SK_LBRC",
            "label": "[",
        }
        "ALGR(SK_G)": {
            "key": "SK_RBRC",
            "label": "]",
        }
        "ALGR(SK_K)": {
            "key": "SK_LLST",
            "label": "ł",
        }
        "ALGR(SK_L)": {
            "key": "SK_CLST",
            "label": "Ł",
        }
        "ALGR(SK_OCIR)": {
            "key": "SK_DLR",
            "label": "$",
        }
        "ALGR(SK_SECT)": {
            "key": "SK_SS",
            "label": "ß",
        }
        "ALGR(SK_NCAR)": {
            "key": "SK_CURR",
            "label": "¤",
        }
        "ALGR(SK_AMPR)": {
            "key": "SK_LABK",
            "label": "<",
        }
        "ALGR(SK_Y)": {
            "key": "SK_RABK",
            "label": ">",
        }
        "ALGR(SK_X)": {
            "key": "SK_HASH",
            "label": "#",
        }
        "ALGR(SK_V)": {
            "key": "SK_AT",
            "label": "@",
        }
        "ALGR(SK_B)": {
            "key": "SK_LCBR",
            "label": "{",
        }
        "ALGR(SK_N)": {
            "key": "SK_RCBR",
            "label": "}",
        }
    }
}
