{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ` │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ - │ = │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ [ │ ] │  \  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ ; │ ' │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ / │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "PL_GRV",
            "label": "`",
        }
        "KC_1": {
            "key": "PL_1",
            "label": "1",
        }
        "KC_2": {
            "key": "PL_2",
            "label": "2",
        }
        "KC_3": {
            "key": "PL_3",
            "label": "3",
        }
        "KC_4": {
            "key": "PL_4",
            "label": "4",
        }
        "KC_5": {
            "key": "PL_5",
            "label": "5",
        }
        "KC_6": {
            "key": "PL_6",
            "label": "6",
        }
        "KC_7": {
            "key": "PL_7",
            "label": "7",
        }
        "KC_8": {
            "key": "PL_8",
            "label": "8",
        }
        "KC_9": {
            "key": "PL_9",
            "label": "9",
        }
        "KC_0": {
            "key": "PL_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "PL_MINS",
            "label": "-",
        }
        "KC_EQL": {
            "key": "PL_EQL",
            "label": "=",
        }
        "KC_Q": {
            "key": "PL_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "PL_W",
            "label": "W",
        }
        "KC_E": {
            "key": "PL_E",
            "label": "E",
        }
        "KC_R": {
            "key": "PL_R",
            "label": "R",
        }
        "KC_T": {
            "key": "PL_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "PL_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "PL_U",
            "label": "U",
        }
        "KC_I": {
            "key": "PL_I",
            "label": "I",
        }
        "KC_O": {
            "key": "PL_O",
            "label": "O",
        }
        "KC_P": {
            "key": "PL_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "PL_LBRC",
            "label": "[",
        }
        "KC_RBRC": {
            "key": "PL_RBRC",
            "label": "]",
        }
        "KC_BSLS": {
            "key": "PL_BSLS",
            "label": "\\",
        }
        "KC_A": {
            "key": "PL_A",
            "label": "A",
        }
        "KC_S": {
            "key": "PL_S",
            "label": "S",
        }
        "KC_D": {
            "key": "PL_D",
            "label": "D",
        }
        "KC_F": {
            "key": "PL_F",
            "label": "F",
        }
        "KC_G": {
            "key": "PL_G",
            "label": "G",
        }
        "KC_H": {
            "key": "PL_H",
            "label": "H",
        }
        "KC_J": {
            "key": "PL_J",
            "label": "J",
        }
        "KC_K": {
            "key": "PL_K",
            "label": "K",
        }
        "KC_L": {
            "key": "PL_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "PL_SCLN",
            "label": ";",
        }
        "KC_QUOT": {
            "key": "PL_QUOT",
            "label": "'",
        }
        "KC_Z": {
            "key": "PL_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "PL_X",
            "label": "X",
        }
        "KC_C": {
            "key": "PL_C",
            "label": "C",
        }
        "KC_V": {
            "key": "PL_V",
            "label": "V",
        }
        "KC_B": {
            "key": "PL_B",
            "label": "B",
        }
        "KC_N": {
            "key": "PL_N",
            "label": "N",
        }
        "KC_M": {
            "key": "PL_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "PL_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "PL_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "PL_SLSH",
            "label": "/",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ~ │ ! │ @ │ # │ $ │ % │ ^ │ & │ * │ ( │ ) │ _ │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ { │ } │  |  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │   │   │   │   │   │   │   │   │   │ : │ " │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │   │   │   │   │   │   │   │ < │ > │ ? │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(PL_GRV)": {
            "key": "PL_TILD",
            "label": "~",
        }
        "S(PL_1)": {
            "key": "PL_EXLM",
            "label": "!",
        }
        "S(PL_2)": {
            "key": "PL_AT",
            "label": "@",
        }
        "S(PL_3)": {
            "key": "PL_HASH",
            "label": "#",
        }
        "S(PL_4)": {
            "key": "PL_DLR",
            "label": "$",
        }
        "S(PL_5)": {
            "key": "PL_PERC",
            "label": "%",
        }
        "S(PL_6)": {
            "key": "PL_CIRC",
            "label": "^",
        }
        "S(PL_7)": {
            "key": "PL_AMPR",
            "label": "&",
        }
        "S(PL_8)": {
            "key": "PL_ASTR",
            "label": "*",
        }
        "S(PL_9)": {
            "key": "PL_LPRN",
            "label": "(",
        }
        "S(PL_0)": {
            "key": "PL_RPRN",
            "label": ")",
        }
        "S(PL_MINS)": {
            "key": "PL_UNDS",
            "label": "_",
        }
        "S(PL_EQL)": {
            "key": "PL_PLUS",
            "label": "+",
        }
        "S(PL_LBRC)": {
            "key": "PL_LCBR",
            "label": "{",
        }
        "S(PL_RBRC)": {
            "key": "PL_RCBR",
            "label": "}",
        }
        "S(PL_BSLS)": {
            "key": "PL_PIPE",
            "label": "|",
        }
        "S(PL_SCLN)": {
            "key": "PL_COLN",
            "label": ":",
        }
        "S(PL_QUOT)": {
            "key": "PL_DQUO",
            "label": "\"",
        }
        "S(PL_COMM)": {
            "key": "PL_LABK",
            "label": "<",
        }
        "S(PL_DOT)": {
            "key": "PL_RABK",
            "label": ">",
        }
        "S(PL_SLSH)": {
            "key": "PL_QUES",
            "label": "?",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │   │   │   │   │   │   │   │   │   │   │   │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │ Ę │   │   │   │ € │   │ Ó │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │ Ą │ Ś │   │   │   │   │   │   │ Ł │   │   │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │ Ż │ Ź │ Ć │   │   │ Ń │   │   │   │   │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(PL_E)": {
            "key": "PL_EOGO",
            "label": "Ę",
        }
        "ALGR(PL_U)": {
            "key": "PL_EURO",
            "label": "€",
        }
        "ALGR(PL_O)": {
            "key": "PL_OACU",
            "label": "Ó",
        }
        "ALGR(PL_A)": {
            "key": "PL_AOGO",
            "label": "Ą",
        }
        "ALGR(PL_S)": {
            "key": "PL_SACU",
            "label": "Ś",
        }
        "ALGR(PL_L)": {
            "key": "PL_LSTR",
            "label": "Ł",
        }
        "ALGR(PL_Z)": {
            "key": "PL_ZDOT",
            "label": "Ż",
        }
        "ALGR(PL_X)": {
            "key": "PL_ZACU",
            "label": "Ź",
        }
        "ALGR(PL_C)": {
            "key": "PL_CACU",
            "label": "Ć",
        }
        "ALGR(PL_N)": {
            "key": "PL_NACU",
            "label": "Ń",
        }
    }
}
