{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ` │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ - │ = │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ D │ R │ W │ B │ J │ F │ U │ P │ ; │ [ │ ] │  \  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │ A │ S │ H │ T │ G │ Y │ N │ E │ O │ I │ ' │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │ Z │ X │ C │ V │ M │ K │ L │ , │ . │ / │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "WK_GRV",
            "label": "`",
        }
        "KC_1": {
            "key": "WK_1",
            "label": "1",
        }
        "KC_2": {
            "key": "WK_2",
            "label": "2",
        }
        "KC_3": {
            "key": "WK_3",
            "label": "3",
        }
        "KC_4": {
            "key": "WK_4",
            "label": "4",
        }
        "KC_5": {
            "key": "WK_5",
            "label": "5",
        }
        "KC_6": {
            "key": "WK_6",
            "label": "6",
        }
        "KC_7": {
            "key": "WK_7",
            "label": "7",
        }
        "KC_8": {
            "key": "WK_8",
            "label": "8",
        }
        "KC_9": {
            "key": "WK_9",
            "label": "9",
        }
        "KC_0": {
            "key": "WK_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "WK_MINS",
            "label": "-",
        }
        "KC_EQL": {
            "key": "WK_EQL",
            "label": "=",
        }
        "KC_Q": {
            "key": "WK_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "WK_D",
            "label": "D",
        }
        "KC_E": {
            "key": "WK_R",
            "label": "R",
        }
        "KC_R": {
            "key": "WK_W",
            "label": "W",
        }
        "KC_T": {
            "key": "WK_B",
            "label": "B",
        }
        "KC_Y": {
            "key": "WK_J",
            "label": "J",
        }
        "KC_U": {
            "key": "WK_F",
            "label": "F",
        }
        "KC_I": {
            "key": "WK_U",
            "label": "U",
        }
        "KC_O": {
            "key": "WK_P",
            "label": "P",
        }
        "KC_P": {
            "key": "WK_SCLN",
            "label": ";",
        }
        "KC_LBRC": {
            "key": "WK_LBRC",
            "label": "[",
        }
        "KC_RBRC": {
            "key": "WK_RBRC",
            "label": "]",
        }
        "KC_BSLS": {
            "key": "WK_BSLS",
            "label": "\\",
        }
        "KC_A": {
            "key": "WK_A",
            "label": "A",
        }
        "KC_S": {
            "key": "WK_S",
            "label": "S",
        }
        "KC_D": {
            "key": "WK_H",
            "label": "H",
        }
        "KC_F": {
            "key": "WK_T",
            "label": "T",
        }
        "KC_G": {
            "key": "WK_G",
            "label": "G",
        }
        "KC_H": {
            "key": "WK_Y",
            "label": "Y",
        }
        "KC_J": {
            "key": "WK_N",
            "label": "N",
        }
        "KC_K": {
            "key": "WK_E",
            "label": "E",
        }
        "KC_L": {
            "key": "WK_O",
            "label": "O",
        }
        "KC_SCLN": {
            "key": "WK_I",
            "label": "I",
        }
        "KC_QUOT": {
            "key": "WK_QUOT",
            "label": "'",
        }
        "KC_Z": {
            "key": "WK_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "WK_X",
            "label": "X",
        }
        "KC_C": {
            "key": "WK_C",
            "label": "C",
        }
        "KC_V": {
            "key": "WK_V",
            "label": "V",
        }
        "KC_B": {
            "key": "WK_M",
            "label": "M",
        }
        "KC_N": {
            "key": "WK_K",
            "label": "K",
        }
        "KC_M": {
            "key": "WK_L",
            "label": "L",
        }
        "KC_COMM": {
            "key": "WK_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "WK_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "WK_SLSH",
            "label": "/",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ~ │ ! │ @ │ # │ $ │ % │ ^ │ & │ * │ ( │ ) │ _ │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │ : │ { │ } │  |  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │   │   │   │   │   │   │   │   │   │   │ " │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │   │   │   │   │   │   │   │ < │ > │ ? │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(WK_GRV)": {
            "key": "WK_TILD",
            "label": "~",
        }
        "S(WK_1)": {
            "key": "WK_EXLM",
            "label": "!",
        }
        "S(WK_2)": {
            "key": "WK_AT",
            "label": "@",
        }
        "S(WK_3)": {
            "key": "WK_HASH",
            "label": "#",
        }
        "S(WK_4)": {
            "key": "WK_DLR",
            "label": "$",
        }
        "S(WK_5)": {
            "key": "WK_PERC",
            "label": "%",
        }
        "S(WK_6)": {
            "key": "WK_CIRC",
            "label": "^",
        }
        "S(WK_7)": {
            "key": "WK_AMPR",
            "label": "&",
        }
        "S(WK_8)": {
            "key": "WK_ASTR",
            "label": "*",
        }
        "S(WK_9)": {
            "key": "WK_LPRN",
            "label": "(",
        }
        "S(WK_0)": {
            "key": "WK_RPRN",
            "label": ")",
        }
        "S(WK_MINS)": {
            "key": "WK_UNDS",
            "label": "_",
        }
        "S(WK_EQL)": {
            "key": "WK_PLUS",
            "label": "+",
        }
        "S(WK_SCLN)": {
            "key": "WK_COLN",
            "label": ":",
        }
        "S(WK_LBRC)": {
            "key": "WK_LCBR",
            "label": "{",
        }
        "S(WK_RBRC)": {
            "key": "WK_RCBR",
            "label": "}",
        }
        "S(WK_BSLS)": {
            "key": "WK_PIPE",
            "label": "|",
        }
        "S(WK_QUOT)": {
            "key": "WK_DQUO",
            "label": "\"",
        }
        "S(WK_COMM)": {
            "key": "WK_LABK",
            "label": "<",
        }
        "S(WK_DOT)": {
            "key": "WK_RABK",
            "label": ">",
        }
        "S(WK_SLSH)": {
            "key": "WK_QUES",
            "label": "?",
        }
    }
}
