{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬─────┐
 * │ ^ │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ ß │ ´ │     │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬───┤
 * │     │ Q │ W │ E │ R │ T │ Z │ U │ I │ O │ P │ Ü │ + │   │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐  │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ Ö │ Ä │ # │  │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴──┤
 * │    │ < │ Y │ X │ C │ V │ B │ N │ M │ , │ . │ - │        │
 * ├────┴┬──┴─┬─┴───┼───┴───┴───┴───┴───┴───┼───┴─┬─┴──┬─────┤
 * │     │    │     │                       │     │    │     │
 * └─────┴────┴─────┴───────────────────────┴─────┴────┴─────┘
 */
        "KC_GRV": {
            "key": "DE_CIRC",
            "label": "^ (dead)",
        }
        "KC_1": {
            "key": "DE_1",
            "label": "1",
        }
        "KC_2": {
            "key": "DE_2",
            "label": "2",
        }
        "KC_3": {
            "key": "DE_3",
            "label": "3",
        }
        "KC_4": {
            "key": "DE_4",
            "label": "4",
        }
        "KC_5": {
            "key": "DE_5",
            "label": "5",
        }
        "KC_6": {
            "key": "DE_6",
            "label": "6",
        }
        "KC_7": {
            "key": "DE_7",
            "label": "7",
        }
        "KC_8": {
            "key": "DE_8",
            "label": "8",
        }
        "KC_9": {
            "key": "DE_9",
            "label": "9",
        }
        "KC_0": {
            "key": "DE_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "DE_SS",
            "label": "ß",
        }
        "KC_EQL": {
            "key": "DE_ACUT",
            "label": "´ (dead)",
        }
        "KC_Q": {
            "key": "DE_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "DE_W",
            "label": "W",
        }
        "KC_E": {
            "key": "DE_E",
            "label": "E",
        }
        "KC_R": {
            "key": "DE_R",
            "label": "R",
        }
        "KC_T": {
            "key": "DE_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "DE_Z",
            "label": "Z",
        }
        "KC_U": {
            "key": "DE_U",
            "label": "U",
        }
        "KC_I": {
            "key": "DE_I",
            "label": "I",
        }
        "KC_O": {
            "key": "DE_O",
            "label": "O",
        }
        "KC_P": {
            "key": "DE_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "DE_UDIA",
            "label": "Ü",
        }
        "KC_RBRC": {
            "key": "DE_PLUS",
            "label": "+",
        }
        "KC_A": {
            "key": "DE_A",
            "label": "A",
        }
        "KC_S": {
            "key": "DE_S",
            "label": "S",
        }
        "KC_D": {
            "key": "DE_D",
            "label": "D",
        }
        "KC_F": {
            "key": "DE_F",
            "label": "F",
        }
        "KC_G": {
            "key": "DE_G",
            "label": "G",
        }
        "KC_H": {
            "key": "DE_H",
            "label": "H",
        }
        "KC_J": {
            "key": "DE_J",
            "label": "J",
        }
        "KC_K": {
            "key": "DE_K",
            "label": "K",
        }
        "KC_L": {
            "key": "DE_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "DE_ODIA",
            "label": "Ö",
        }
        "KC_QUOT": {
            "key": "DE_ADIA",
            "label": "Ä",
        }
        "KC_NUHS": {
            "key": "DE_HASH",
            "label": "#",
        }
        "KC_NUBS": {
            "key": "DE_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "DE_Y",
            "label": "Y",
        }
        "KC_X": {
            "key": "DE_X",
            "label": "X",
        }
        "KC_C": {
            "key": "DE_C",
            "label": "C",
        }
        "KC_V": {
            "key": "DE_V",
            "label": "V",
        }
        "KC_B": {
            "key": "DE_B",
            "label": "B",
        }
        "KC_N": {
            "key": "DE_N",
            "label": "N",
        }
        "KC_M": {
            "key": "DE_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "DE_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "DE_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "DE_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬─────┐
 * │ ° │ ! │ " │ § │ $ │ % │ & │ / │ ( │ ) │ = │ ? │ ` │     │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬───┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │ * │   │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐  │
 * │      │   │   │   │   │   │   │   │   │   │   │   │ ' │  │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴──┤
 * │    │ > │   │   │   │   │   │   │   │ ; │ : │ _ │        │
 * ├────┴┬──┴─┬─┴───┼───┴───┴───┴───┴───┴───┼───┴─┬─┴──┬─────┤
 * │     │    │     │                       │     │    │     │
 * └─────┴────┴─────┴───────────────────────┴─────┴────┴─────┘
 */
        "S(DE_CIRC)": {
            "key": "DE_DEG",
            "label": "°",
        }
        "S(DE_1)": {
            "key": "DE_EXLM",
            "label": "!",
        }
        "S(DE_2)": {
            "key": "DE_DQUO",
            "label": "\"",
        }
        "S(DE_3)": {
            "key": "DE_SECT",
            "label": "§",
        }
        "S(DE_4)": {
            "key": "DE_DLR",
            "label": "$",
        }
        "S(DE_5)": {
            "key": "DE_PERC",
            "label": "%",
        }
        "S(DE_6)": {
            "key": "DE_AMPR",
            "label": "&",
        }
        "S(DE_7)": {
            "key": "DE_SLSH",
            "label": "/",
        }
        "S(DE_8)": {
            "key": "DE_LPRN",
            "label": "(",
        }
        "S(DE_9)": {
            "key": "DE_RPRN",
            "label": ")",
        }
        "S(DE_0)": {
            "key": "DE_EQL",
            "label": "=",
        }
        "S(DE_SS)": {
            "key": "DE_QUES",
            "label": "?",
        }
        "S(DE_ACUT)": {
            "key": "DE_GRV",
            "label": "` (dead)",
        }
        "S(DE_PLUS)": {
            "key": "DE_ASTR",
            "label": "*",
        }
        "S(DE_HASH)": {
            "key": "DE_QUOT",
            "label": "'",
        }
        "S(DE_LABK)": {
            "key": "DE_RABK",
            "label": ">",
        }
        "S(DE_COMM)": {
            "key": "DE_SCLN",
            "label": ";",
        }
        "S(DE_DOT)": {
            "key": "DE_COLN",
            "label": ":",
        }
        "S(DE_MINS)": {
            "key": "DE_UNDS",
            "label": "_",
        }
/* Alted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬─────┐
 * │ „ │ ¡ │ “ │ ¶ │ ¢ │ [ │ ] │ | │ { │ } │ ≠ │ ¿ │   │     │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬───┤
 * │     │ « │ ∑ │ € │ ® │ † │ Ω │ ¨ │ ⁄ │ Ø │ π │ • │ ± │   │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐  │
 * │      │ Å │ ‚ │ ∂ │ ƒ │ © │ ª │ º │ ∆ │ @ │ Œ │ Æ │ ‘ │  │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴──┤
 * │    │ ≤ │ ¥ │ ≈ │ Ç │ √ │ ∫ │ ~ │ µ │ ∞ │ … │ – │        │
 * ├────┴┬──┴─┬─┴───┼───┴───┴───┴───┴───┴───┼───┴─┬─┴──┬─────┤
 * │     │    │     │                       │     │    │     │
 * └─────┴────┴─────┴───────────────────────┴─────┴────┴─────┘
 */
        "A(DE_CIRC)": {
            "key": "DE_DLQU",
            "label": "„",
        }
        "A(DE_1)": {
            "key": "DE_IEXL",
            "label": "¡",
        }
        "A(DE_2)": {
            "key": "DE_LDQU",
            "label": "“",
        }
        "A(DE_3)": {
            "key": "DE_PILC",
            "label": "¶",
        }
        "A(DE_4)": {
            "key": "DE_CENT",
            "label": "¢",
        }
        "A(DE_5)": {
            "key": "DE_LBRC",
            "label": "[",
        }
        "A(DE_6)": {
            "key": "DE_RBRC",
            "label": "]",
        }
        "A(DE_7)": {
            "key": "DE_PIPE",
            "label": "|",
        }
        "A(DE_8)": {
            "key": "DE_LCBR",
            "label": "{",
        }
        "A(DE_9)": {
            "key": "DE_RCBR",
            "label": "}",
        }
        "A(DE_0)": {
            "key": "DE_NEQL",
            "label": "≠",
        }
        "A(DE_SS)": {
            "key": "DE_IQUE",
            "label": "¿",
        }
        "A(DE_Q)": {
            "key": "DE_LDAQ",
            "label": "«",
        }
        "A(DE_W)": {
            "key": "DE_NARS",
            "label": "∑",
        }
        "A(DE_E)": {
            "key": "DE_EURO",
            "label": "€",
        }
        "A(DE_R)": {
            "key": "DE_REGD",
            "label": "®",
        }
        "A(DE_T)": {
            "key": "DE_DAGG",
            "label": "†",
        }
        "A(DE_Z)": {
            "key": "DE_OMEG",
            "label": "Ω",
        }
        "A(DE_U)": {
            "key": "DE_DIAE",
            "label": "¨ (dead)",
        }
        "A(DE_I)": {
            "key": "DE_FRSL",
            "label": "⁄",
        }
        "A(DE_O)": {
            "key": "DE_OSTR",
            "label": "Ø",
        }
        "A(DE_P)": {
            "key": "DE_PI",
            "label": "π",
        }
        "A(DE_UDIA)": {
            "key": "DE_BULT",
            "label": "•",
        }
        "A(DE_PLUS)": {
            "key": "DE_PLMN",
            "label": "±",
        }
        "A(DE_A)": {
            "key": "DE_ARNG",
            "label": "Å",
        }
        "A(DE_S)": {
            "key": "DE_SLQU",
            "label": "‚",
        }
        "A(DE_D)": {
            "key": "DE_PDIF",
            "label": "∂",
        }
        "A(DE_F)": {
            "key": "DE_FHK",
            "label": "ƒ",
        }
        "A(DE_G)": {
            "key": "DE_COPY",
            "label": "©",
        }
        "A(DE_H)": {
            "key": "DE_FORD",
            "label": "ª",
        }
        "A(DE_J)": {
            "key": "DE_MORD",
            "label": "º",
        }
        "A(DE_K)": {
            "key": "DE_INCR",
            "label": "∆",
        }
        "A(DE_L)": {
            "key": "DE_AT",
            "label": "@",
        }
        "A(DE_ODIA)": {
            "key": "DE_OE",
            "label": "Œ",
        }
        "A(DE_ADIA)": {
            "key": "DE_AE",
            "label": "Æ",
        }
        "A(DE_HASH)": {
            "key": "DE_LSQU",
            "label": "‘",
        }
        "A(DE_LABK)": {
            "key": "DE_LTEQ",
            "label": "≤",
        }
        "A(DE_Y)": {
            "key": "DE_YEN",
            "label": "¥",
        }
        "A(DE_X)": {
            "key": "DE_AEQL",
            "label": "≈",
        }
        "A(DE_C)": {
            "key": "DE_CCCE",
            "label": "Ç",
        }
        "A(DE_V)": {
            "key": "DE_SQRT",
            "label": "√",
        }
        "A(DE_B)": {
            "key": "DE_INTG",
            "label": "∫",
        }
        "A(DE_N)": {
            "key": "DE_TILD",
            "label": "~ (dead)",
        }
        "A(DE_M)": {
            "key": "DE_MICR",
            "label": "µ",
        }
        "A(DE_COMM)": {
            "key": "DE_INFN",
            "label": "∞",
        }
        "A(DE_DOT)": {
            "key": "DE_ELLP",
            "label": "…",
        }
        "A(DE_MINS)": {
            "key": "DE_NDSH",
            "label": "–",
        }
/* Shift+Alted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬─────┐
 * │   │ ¬ │ ” │   │ £ │ ﬁ │   │ \ │ ˜ │ · │ ¯ │ ˙ │ ˚ │     │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬───┤
 * │     │ » │   │ ‰ │ ¸ │ ˝ │ ˇ │ Á │ Û │   │ ∏ │   │  │   │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐  │
 * │      │   │ Í │ ™ │ Ï │ Ì │ Ó │ ı │   │ ﬂ │   │   │   │  │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴──┤
 * │    │ ≥ │ ‡ │ Ù │   │ ◊ │ ‹ │ › │ ˘ │ ˛ │ ÷ │ — │        │
 * ├────┴┬──┴─┬─┴───┼───┴───┴───┴───┴───┴───┼───┴─┬─┴──┬─────┤
 * │     │    │     │                       │     │    │     │
 * └─────┴────┴─────┴───────────────────────┴─────┴────┴─────┘
 */
        "S(A(DE_1))": {
            "key": "DE_NOT",
            "label": "¬",
        }
        "S(A(DE_2))": {
            "key": "DE_RDQU",
            "label": "”",
        }
        "S(A(DE_4))": {
            "key": "DE_PND",
            "label": "£",
        }
        "S(A(DE_5))": {
            "key": "DE_FI",
            "label": "ﬁ",
        }
        "S(A(DE_7))": {
            "key": "DE_BSLS",
            "label": "\\",
        }
        "S(A(DE_8))": {
            "key": "DE_STIL",
            "label": "˜",
        }
        "S(A(DE_9))": {
            "key": "DE_MDDT",
            "label": "·",
        }
        "S(A(DE_0))": {
            "key": "DE_MACR",
            "label": "¯",
        }
        "S(A(DE_SS))": {
            "key": "DE_DOTA",
            "label": "˙",
        }
        "S(A(DE_ACUT))": {
            "key": "DE_RNGA",
            "label": "˚",
        }
        "S(A(DE_Q))": {
            "key": "DE_RDAQ",
            "label": "»",
        }
        "S(A(DE_E))": {
            "key": "DE_PERM",
            "label": "‰",
        }
        "S(A(DE_R))": {
            "key": "DE_CEDL",
            "label": "¸",
        }
        "S(A(DE_T))": {
            "key": "DE_DACU",
            "label": "˝",
        }
        "S(A(DE_Z))": {
            "key": "DE_CARN",
            "label": "ˇ",
        }
        "S(A(DE_U))": {
            "key": "DE_AACU",
            "label": "Á",
        }
        "S(A(DE_I))": {
            "key": "DE_UCIR",
            "label": "Û",
        }
        "S(A(DE_P))": {
            "key": "DE_NARP",
            "label": "∏",
        }
        "S(A(DE_PLUS))": {
            "key": "DE_APPL",
            "label": " (Apple logo)",
        }
        "S(A(DE_S))": {
            "key": "DE_IACU",
            "label": "Í",
        }
        "S(A(DE_D))": {
            "key": "DE_TM",
            "label": "™",
        }
        "S(A(DE_F))": {
            "key": "DE_IDIA",
            "label": "Ï",
        }
        "S(A(DE_G))": {
            "key": "DE_IGRV",
            "label": "Ì",
        }
        "S(A(DE_H))": {
            "key": "DE_OACU",
            "label": "Ó",
        }
        "S(A(DE_J))": {
            "key": "DE_DLSI",
            "label": "ı",
        }
        "S(A(DE_L))": {
            "key": "DE_FL",
            "label": "ﬂ",
        }
        "S(A(DE_LABK))": {
            "key": "DE_GTEQ",
            "label": "≥",
        }
        "S(A(DE_Y))": {
            "key": "DE_DDAG",
            "label": "‡",
        }
        "S(A(DE_X))": {
            "key": "DE_UGRV",
            "label": "Ù",
        }
        "S(A(DE_V))": {
            "key": "DE_LOZN",
            "label": "◊",
        }
        "S(A(DE_B))": {
            "key": "DE_LSAQ",
            "label": "‹",
        }
        "S(A(DE_N))": {
            "key": "DE_RSAQ",
            "label": "›",
        }
        "S(A(DE_M))": {
            "key": "DE_BREV",
            "label": "˘",
        }
        "S(A(DE_COMM))": {
            "key": "DE_OGON",
            "label": "˛",
        }
        "S(A(DE_DOT))": {
            "key": "DE_DIV",
            "label": "÷",
        }
        "S(A(DE_MINS))": {
            "key": "DE_MDSH",
            "label": "—",
        }
    }
}
