{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ` │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ - │ = │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ [ │ ] │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ ; │ ' │ # │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ \ │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ / │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "IE_GRV",
            "label": "`",
        }
        "KC_1": {
            "key": "IE_1",
            "label": "1",
        }
        "KC_2": {
            "key": "IE_2",
            "label": "2",
        }
        "KC_3": {
            "key": "IE_3",
            "label": "3",
        }
        "KC_4": {
            "key": "IE_4",
            "label": "4",
        }
        "KC_5": {
            "key": "IE_5",
            "label": "5",
        }
        "KC_6": {
            "key": "IE_6",
            "label": "6",
        }
        "KC_7": {
            "key": "IE_7",
            "label": "7",
        }
        "KC_8": {
            "key": "IE_8",
            "label": "8",
        }
        "KC_9": {
            "key": "IE_9",
            "label": "9",
        }
        "KC_0": {
            "key": "IE_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "IE_MINS",
            "label": "-",
        }
        "KC_EQL": {
            "key": "IE_EQL",
            "label": "=",
        }
        "KC_Q": {
            "key": "IE_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "IE_W",
            "label": "W",
        }
        "KC_E": {
            "key": "IE_E",
            "label": "E",
        }
        "KC_R": {
            "key": "IE_R",
            "label": "R",
        }
        "KC_T": {
            "key": "IE_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "IE_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "IE_U",
            "label": "U",
        }
        "KC_I": {
            "key": "IE_I",
            "label": "I",
        }
        "KC_O": {
            "key": "IE_O",
            "label": "O",
        }
        "KC_P": {
            "key": "IE_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "IE_LBRC",
            "label": "[",
        }
        "KC_RBRC": {
            "key": "IE_RBRC",
            "label": "]",
        }
        "KC_A": {
            "key": "IE_A",
            "label": "A",
        }
        "KC_S": {
            "key": "IE_S",
            "label": "S",
        }
        "KC_D": {
            "key": "IE_D",
            "label": "D",
        }
        "KC_F": {
            "key": "IE_F",
            "label": "F",
        }
        "KC_G": {
            "key": "IE_G",
            "label": "G",
        }
        "KC_H": {
            "key": "IE_H",
            "label": "H",
        }
        "KC_J": {
            "key": "IE_J",
            "label": "J",
        }
        "KC_K": {
            "key": "IE_K",
            "label": "K",
        }
        "KC_L": {
            "key": "IE_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "IE_SCLN",
            "label": ";",
        }
        "KC_QUOT": {
            "key": "IE_QUOT",
            "label": "'",
        }
        "KC_NUHS": {
            "key": "IE_HASH",
            "label": "#",
        }
        "KC_NUBS": {
            "key": "IE_BSLS",
            "label": "\\",
        }
        "KC_Z": {
            "key": "IE_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "IE_X",
            "label": "X",
        }
        "KC_C": {
            "key": "IE_C",
            "label": "C",
        }
        "KC_V": {
            "key": "IE_V",
            "label": "V",
        }
        "KC_B": {
            "key": "IE_B",
            "label": "B",
        }
        "KC_N": {
            "key": "IE_N",
            "label": "N",
        }
        "KC_M": {
            "key": "IE_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "IE_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "IE_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "IE_SLSH",
            "label": "/",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ¬ │ ! │ " │ £ │ $ │ % │ ^ │ & │ * │ ( │ ) │ _ │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ { │ } │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │ : │ @ │ ~ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ | │   │   │   │   │   │   │   │ < │ > │ ? │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(IE_GRV)": {
            "key": "IE_NOT",
            "label": "¬",
        }
        "S(IE_1)": {
            "key": "IE_EXLM",
            "label": "!",
        }
        "S(IE_2)": {
            "key": "IE_DQUO",
            "label": "\"",
        }
        "S(IE_3)": {
            "key": "IE_PND",
            "label": "£",
        }
        "S(IE_4)": {
            "key": "IE_DLR",
            "label": "$",
        }
        "S(IE_5)": {
            "key": "IE_PERC",
            "label": "%",
        }
        "S(IE_6)": {
            "key": "IE_CIRC",
            "label": "^",
        }
        "S(IE_7)": {
            "key": "IE_AMPR",
            "label": "&",
        }
        "S(IE_8)": {
            "key": "IE_ASTR",
            "label": "*",
        }
        "S(IE_9)": {
            "key": "IE_LPRN",
            "label": "(",
        }
        "S(IE_0)": {
            "key": "IE_RPRN",
            "label": ")",
        }
        "S(IE_MINS)": {
            "key": "IE_UNDS",
            "label": "_",
        }
        "S(IE_EQL)": {
            "key": "IE_PLUS",
            "label": "+",
        }
        "S(IE_LBRC)": {
            "key": "IE_LCBR",
            "label": "{",
        }
        "S(IE_RBRC)": {
            "key": "IE_RCBR",
            "label": "}",
        }
        "S(IE_SCLN)": {
            "key": "IE_COLN",
            "label": ":",
        }
        "S(IE_QUOT)": {
            "key": "IE_AT",
            "label": "@",
        }
        "S(IE_HASH)": {
            "key": "IE_TILD",
            "label": "~",
        }
        "S(IE_BSLS)": {
            "key": "IE_PIPE",
            "label": "|",
        }
        "S(IE_COMM)": {
            "key": "IE_LABK",
            "label": "<",
        }
        "S(IE_DOT)": {
            "key": "IE_RABK",
            "label": ">",
        }
        "S(IE_SLSH)": {
            "key": "IE_QUES",
            "label": "?",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ¦ │   │   │   │ € │   │   │   │   │   │   │   │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │ É │   │   │   │ Ú │ Í │ Ó │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ Á │   │   │   │   │   │   │   │   │   │ ´ │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │   │   │   │   │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(IE_GRV)": {
            "key": "IE_BRKP",
            "label": "¦",
        }
        "ALGR(IE_4)": {
            "key": "IE_EURO",
            "label": "€",
        }
        "ALGR(IE_E)": {
            "key": "IE_EACU",
            "label": "É",
        }
        "ALGR(IE_U)": {
            "key": "IE_UACU",
            "label": "Ú",
        }
        "ALGR(IE_I)": {
            "key": "IE_IACU",
            "label": "Í",
        }
        "ALGR(IE_O)": {
            "key": "IE_OACU",
            "label": "Ó",
        }
        "ALGR(IE_A)": {
            "key": "IE_AACU",
            "label": "Á",
        }
        "ALGR(IE_QUOT)": {
            "key": "IE_ACUT",
            "label": "´ (dead)",
        }
    }
}
