{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬─────┐
 * │ @ │ & │ é │ " │ ' │ ( │ § │ è │ ! │ ç │ à │ ) │ - │     │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬───┤
 * │     │ A │ Z │ E │ R │ T │ Y │ U │ I │ O │ P │ ^ │ $ │   │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐  │
 * │      │ Q │ S │ D │ F │ G │ H │ J │ K │ L │ M │ ù │ ` │  │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴──┤
 * │    │ < │ W │ X │ C │ V │ B │ N │ , │ ; │ : │ = │        │
 * ├────┴┬──┴─┬─┴───┼───┴───┴───┴───┴───┴───┼───┴─┬─┴──┬─────┤
 * │     │    │     │                       │     │    │     │
 * └─────┴────┴─────┴───────────────────────┴─────┴────┴─────┘
 */
        "KC_GRV": {
            "key": "FR_AT",
            "label": "@",
        }
        "KC_1": {
            "key": "FR_AMPR",
            "label": "&",
        }
        "KC_2": {
            "key": "FR_LEAC",
            "label": "é",
        }
        "KC_3": {
            "key": "FR_DQUO",
            "label": "\"",
        }
        "KC_4": {
            "key": "FR_QUOT",
            "label": "'",
        }
        "KC_5": {
            "key": "FR_LPRN",
            "label": "(",
        }
        "KC_6": {
            "key": "FR_SECT",
            "label": "§",
        }
        "KC_7": {
            "key": "FR_LEGR",
            "label": "è",
        }
        "KC_8": {
            "key": "FR_EXLM",
            "label": "!",
        }
        "KC_9": {
            "key": "FR_LCCE",
            "label": "ç",
        }
        "KC_0": {
            "key": "FR_LAGR",
            "label": "à",
        }
        "KC_MINS": {
            "key": "FR_RPRN",
            "label": ")",
        }
        "KC_EQL": {
            "key": "FR_MINS",
            "label": "-",
        }
        "KC_Q": {
            "key": "FR_A",
            "label": "A",
        }
        "KC_W": {
            "key": "FR_Z",
            "label": "Z",
        }
        "KC_E": {
            "key": "FR_E",
            "label": "E",
        }
        "KC_R": {
            "key": "FR_R",
            "label": "R",
        }
        "KC_T": {
            "key": "FR_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "FR_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "FR_U",
            "label": "U",
        }
        "KC_I": {
            "key": "FR_I",
            "label": "I",
        }
        "KC_O": {
            "key": "FR_O",
            "label": "O",
        }
        "KC_P": {
            "key": "FR_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "FR_CIRC",
            "label": "^",
        }
        "KC_RBRC": {
            "key": "FR_DLR",
            "label": "$",
        }
        "KC_A": {
            "key": "FR_Q",
            "label": "Q",
        }
        "KC_S": {
            "key": "FR_S",
            "label": "S",
        }
        "KC_D": {
            "key": "FR_D",
            "label": "D",
        }
        "KC_F": {
            "key": "FR_F",
            "label": "F",
        }
        "KC_G": {
            "key": "FR_G",
            "label": "G",
        }
        "KC_H": {
            "key": "FR_H",
            "label": "H",
        }
        "KC_J": {
            "key": "FR_J",
            "label": "J",
        }
        "KC_K": {
            "key": "FR_K",
            "label": "K",
        }
        "KC_L": {
            "key": "FR_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "FR_M",
            "label": "M",
        }
        "KC_QUOT": {
            "key": "FR_LUGR",
            "label": "ù",
        }
        "KC_NUHS": {
            "key": "FR_GRV",
            "label": "`",
        }
        "KC_NUBS": {
            "key": "FR_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "FR_W",
            "label": "W",
        }
        "KC_X": {
            "key": "FR_X",
            "label": "X",
        }
        "KC_C": {
            "key": "FR_C",
            "label": "C",
        }
        "KC_V": {
            "key": "FR_V",
            "label": "V",
        }
        "KC_B": {
            "key": "FR_B",
            "label": "B",
        }
        "KC_N": {
            "key": "FR_N",
            "label": "N",
        }
        "KC_M": {
            "key": "FR_COMM",
            "label": ",",
        }
        "KC_COMM": {
            "key": "FR_SCLN",
            "label": ";",
        }
        "KC_DOT": {
            "key": "FR_COLN",
            "label": ":",
        }
        "KC_SLSH": {
            "key": "FR_EQL",
            "label": "=",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬─────┐
 * │ # │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ ° │ _ │     │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬───┤
 * │     │   │   │   │   │   │   │   │   │   │   │ ¨ │ * │   │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐  │
 * │      │   │   │   │   │   │   │   │   │   │   │ % │ £ │  │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴──┤
 * │    │ > │   │   │   │   │   │   │ ? │ . │ / │ + │        │
 * ├────┴┬──┴─┬─┴───┼───┴───┴───┴───┴───┴───┼───┴─┬─┴──┬─────┤
 * │     │    │     │                       │     │    │     │
 * └─────┴────┴─────┴───────────────────────┴─────┴────┴─────┘
 */
        "S(FR_AT)": {
            "key": "FR_HASH",
            "label": "#",
        }
        "S(FR_AMPR)": {
            "key": "FR_1",
            "label": "1",
        }
        "S(FR_LEAC)": {
            "key": "FR_2",
            "label": "2",
        }
        "S(FR_DQUO)": {
            "key": "FR_3",
            "label": "3",
        }
        "S(FR_QUOT)": {
            "key": "FR_4",
            "label": "4",
        }
        "S(FR_LPRN)": {
            "key": "FR_5",
            "label": "5",
        }
        "S(FR_SECT)": {
            "key": "FR_6",
            "label": "6",
        }
        "S(FR_LEGR)": {
            "key": "FR_7",
            "label": "7",
        }
        "S(FR_EXLM)": {
            "key": "FR_8",
            "label": "8",
        }
        "S(FR_LCCE)": {
            "key": "FR_9",
            "label": "9",
        }
        "S(FR_LAGR)": {
            "key": "FR_0",
            "label": "0",
        }
        "S(FR_RPRN)": {
            "key": "FR_DEG",
            "label": "°",
        }
        "S(FR_MINS)": {
            "key": "FR_UNDS",
            "label": "_",
        }
        "S(FR_CIRC)": {
            "key": "FR_DIAE",
            "label": "¨ (dead)",
        }
        "S(FR_DLR)": {
            "key": "FR_ASTR",
            "label": "*",
        }
        "S(FR_LUGR)": {
            "key": "FR_PERC",
            "label": "%",
        }
        "S(FR_GRV)": {
            "key": "FR_PND",
            "label": "£",
        }
        "S(FR_LABK)": {
            "key": "FR_RABK",
            "label": ">",
        }
        "S(FR_COMM)": {
            "key": "FR_QUES",
            "label": "?",
        }
        "S(FR_SCLN)": {
            "key": "FR_DOT",
            "label": ".",
        }
        "S(FR_COLN)": {
            "key": "FR_SLSH",
            "label": "/",
        }
        "S(FR_EQL)": {
            "key": "FR_PLUS",
            "label": "+",
        }
/* Alted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬─────┐
 * │ • │  │ ë │ “ │ ‘ │ { │ ¶ │ « │ ¡ │ Ç │ Ø │ } │ — │     │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬───┤
 * │     │ Æ │ Â │ Ê │ ® │ † │ Ú │ º │ î │ Œ │ π │ Ô │ € │   │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐  │
 * │      │ ‡ │ Ò │ ∂ │ ƒ │ ﬁ │ Ì │ Ï │ È │ ¬ │ µ │ Ù │   │  │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴──┤
 * │    │ ≤ │ ‹ │ ≈ │ © │ ◊ │ ß │ ~ │ ∞ │ … │ ÷ │ ≠ │        │
 * ├────┴┬──┴─┬─┴───┼───┴───┴───┴───┴───┴───┼───┴─┬─┴──┬─────┤
 * │     │    │     │                       │     │    │     │
 * └─────┴────┴─────┴───────────────────────┴─────┴────┴─────┘
 */
        "A(FR_AT)": {
            "key": "FR_BULT",
            "label": "•",
        }
        "A(FR_AMPR)": {
            "key": "FR_APPL",
            "label": " (Apple logo)",
        }
        "A(FR_LEAC)": {
            "key": "FR_LEDI",
            "label": "ë",
        }
        "A(FR_DQUO)": {
            "key": "FR_LDQU",
            "label": "“",
        }
        "A(FR_QUOT)": {
            "key": "FR_LSQU",
            "label": "‘",
        }
        "A(FR_LPRN)": {
            "key": "FR_LCBR",
            "label": "{",
        }
        "A(FR_SECT)": {
            "key": "FR_PILC",
            "label": "¶",
        }
        "A(FR_LEGR)": {
            "key": "FR_LDAQ",
            "label": "«",
        }
        "A(FR_EXLM)": {
            "key": "FR_IEXL",
            "label": "¡",
        }
        "A(FR_LCCE)": {
            "key": "FR_CCCE",
            "label": "Ç",
        }
        "A(FR_LAGR)": {
            "key": "FR_OSTR",
            "label": "Ø",
        }
        "A(FR_RPRN)": {
            "key": "FR_RCBR",
            "label": "}",
        }
        "A(FR_MINS)": {
            "key": "FR_MDSH",
            "label": "—",
        }
        "A(FR_A)": {
            "key": "FR_AE",
            "label": "Æ",
        }
        "A(FR_Z)": {
            "key": "FR_CACI",
            "label": "Â",
        }
        "A(FR_E)": {
            "key": "FR_ECIR",
            "label": "Ê",
        }
        "A(FR_R)": {
            "key": "FR_REGD",
            "label": "®",
        }
        "A(FR_T)": {
            "key": "FR_DAGG",
            "label": "†",
        }
        "A(FR_Y)": {
            "key": "FR_CUAC",
            "label": "Ú",
        }
        "A(FR_U)": {
            "key": "FR_MORD",
            "label": "º",
        }
        "A(FR_I)": {
            "key": "FR_LICI",
            "label": "î",
        }
        "A(FR_O)": {
            "key": "FR_OE",
            "label": "Œ",
        }
        "A(FR_P)": {
            "key": "FR_PI",
            "label": "π",
        }
        "A(FR_CIRC)": {
            "key": "FR_OCIR",
            "label": "Ô",
        }
        "A(FR_DLR)": {
            "key": "FR_EURO",
            "label": "€",
        }
        "A(FR_Q)": {
            "key": "FR_DDAG",
            "label": "‡",
        }
        "A(FR_S)": {
            "key": "FR_COGR",
            "label": "Ò",
        }
        "A(FR_D)": {
            "key": "FR_PDIF",
            "label": "∂",
        }
        "A(FR_F)": {
            "key": "FR_FHK",
            "label": "ƒ",
        }
        "A(FR_G)": {
            "key": "FR_FI",
            "label": "ﬁ",
        }
        "A(FR_H)": {
            "key": "FR_CIGR",
            "label": "Ì",
        }
        "A(FR_J)": {
            "key": "FR_CIDI",
            "label": "Ï",
        }
        "A(FR_K)": {
            "key": "FR_CEGR",
            "label": "È",
        }
        "A(FR_L)": {
            "key": "FR_NOT",
            "label": "¬",
        }
        "A(FR_M)": {
            "key": "FR_MICR",
            "label": "µ",
        }
        "A(FR_LUGR)": {
            "key": "FR_CUGR",
            "label": "Ù",
        }
        "A(FR_LABK)": {
            "key": "FR_LTEQ",
            "label": "≤",
        }
        "A(FR_W)": {
            "key": "FR_LSAQ",
            "label": "‹",
        }
        "A(FR_X)": {
            "key": "FR_AEQL",
            "label": "≈",
        }
        "A(FR_C)": {
            "key": "FR_COPY",
            "label": "©",
        }
        "A(FR_V)": {
            "key": "FR_LOZN",
            "label": "◊",
        }
        "A(FR_B)": {
            "key": "FR_SS",
            "label": "ß",
        }
        "A(FR_N)": {
            "key": "FR_TILD",
            "label": "~ (dead)",
        }
        "A(FR_COMM)": {
            "key": "FR_INFN",
            "label": "∞",
        }
        "A(FR_SCLN)": {
            "key": "FR_ELLP",
            "label": "…",
        }
        "A(FR_COLN)": {
            "key": "FR_DIV",
            "label": "÷",
        }
        "A(FR_EQL)": {
            "key": "FR_NEQL",
            "label": "≠",
        }
/* Shift+Alted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬─────┐
 * │ Ÿ │ ´ │ „ │   │   │ [ │ å │ » │ Û │ Á │   │ ] │ – │     │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬───┤
 * │     │   │ Å │   │ ‚ │ ™ │   │ ª │ ï │   │ ∏ │   │ ¥ │   │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐  │
 * │      │ Ω │ ∑ │ ∆ │ · │ ﬂ │ Î │ Í │ Ë │ | │ Ó │ ‰ │   │  │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴──┤
 * │    │ ≥ │ › │ ⁄ │ ¢ │ √ │ ∫ │ ı │ ¿ │   │ \ │ ± │        │
 * ├────┴┬──┴─┬─┴───┼───┴───┴───┴───┴───┴───┼───┴─┬─┴──┬─────┤
 * │     │    │     │                       │     │    │     │
 * └─────┴────┴─────┴───────────────────────┴─────┴────┴─────┘
 */
        "S(A(FR_AT))": {
            "key": "FR_CYDI",
            "label": "Ÿ",
        }
        "S(A(FR_AMPR))": {
            "key": "FR_ACUT",
            "label": "´ (dead)",
        }
        "S(A(FR_LEAC))": {
            "key": "FR_DLQU",
            "label": "„",
        }
        "S(A(FR_LPRN))": {
            "key": "FR_LBRC",
            "label": "[",
        }
        "S(A(FR_SECT))": {
            "key": "FR_LARI",
            "label": "å",
        }
        "S(A(FR_LEGR))": {
            "key": "FR_RDAQ",
            "label": "»",
        }
        "S(A(FR_EXLM))": {
            "key": "FR_CUCI",
            "label": "Û",
        }
        "S(A(FR_LCCE))": {
            "key": "FR_CAAC",
            "label": "Á",
        }
        "S(A(FR_RPRN))": {
            "key": "FR_RBRC",
            "label": "]",
        }
        "S(A(FR_MINS))": {
            "key": "FR_NDSH",
            "label": "–",
        }
        "S(A(FR_Z))": {
            "key": "FR_CARI",
            "label": "Å",
        }
        "S(A(FR_R))": {
            "key": "FR_SLQU",
            "label": "‚",
        }
        "S(A(FR_T))": {
            "key": "FR_TM",
            "label": "™",
        }
        "S(A(FR_U))": {
            "key": "FR_FORD",
            "label": "ª",
        }
        "S(A(FR_I))": {
            "key": "FR_LIDI",
            "label": "ï",
        }
        "S(A(FR_P))": {
            "key": "FR_NARP",
            "label": "∏",
        }
        "S(A(FR_DLR))": {
            "key": "FR_YEN",
            "label": "¥",
        }
        "S(A(FR_Q))": {
            "key": "FR_OMEG",
            "label": "Ω",
        }
        "S(A(FR_S))": {
            "key": "FR_NARS",
            "label": "∑",
        }
        "S(A(FR_D))": {
            "key": "FR_INCR",
            "label": "∆",
        }
        "S(A(FR_F))": {
            "key": "FR_MDDT",
            "label": "·",
        }
        "S(A(FR_G))": {
            "key": "FR_FL",
            "label": "ﬂ",
        }
        "S(A(FR_H))": {
            "key": "FR_CICI",
            "label": "Î",
        }
        "S(A(FR_J))": {
            "key": "FR_CIAC",
            "label": "Í",
        }
        "S(A(FR_K))": {
            "key": "FR_CEDI",
            "label": "Ë",
        }
        "S(A(FR_L))": {
            "key": "FR_PIPE",
            "label": "|",
        }
        "S(A(FR_M))": {
            "key": "FR_COAC",
            "label": "Ó",
        }
        "S(A(FR_LUGR))": {
            "key": "FR_PERM",
            "label": "‰",
        }
        "S(A(FR_LABK))": {
            "key": "FR_GTEQ",
            "label": "≥",
        }
        "S(A(FR_W))": {
            "key": "FR_RSAQ",
            "label": "›",
        }
        "S(A(FR_X))": {
            "key": "FR_FRSL",
            "label": "⁄",
        }
        "S(A(FR_C))": {
            "key": "FR_CENT",
            "label": "¢",
        }
        "S(A(FR_V))": {
            "key": "FR_SQRT",
            "label": "√",
        }
        "S(A(FR_B))": {
            "key": "FR_INTG",
            "label": "∫",
        }
        "S(A(FR_N))": {
            "key": "FR_DLSI",
            "label": "ı",
        }
        "S(A(FR_COMM))": {
            "key": "FR_IQUE",
            "label": "¿",
        }
        "S(A(FR_COLN))": {
            "key": "FR_BSLS",
            "label": "\\",
        }
        "S(A(FR_EQL))": {
            "key": "FR_PLMN",
            "label": "±",
        }
    }
}
