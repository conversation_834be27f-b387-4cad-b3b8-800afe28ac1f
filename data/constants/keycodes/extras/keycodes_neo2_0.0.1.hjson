{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ^ │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ - │ ` │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ X │ V │ L │ C │ W │ K │ H │ G │ F │ Q │ ß │ ´ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │  L3  │ U │ I │ A │ E │ O │ S │ N │ R │ T │ D │ Y │ L3│    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │L4 │ Ü │ Ö │ Ä │ P │ Z │ B │ M │ , │ . │ J │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │ L4 │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "NE_CIRC",
            "label": "^ (dead)",
        }
        "KC_1": {
            "key": "NE_1",
            "label": "1",
        }
        "KC_2": {
            "key": "NE_2",
            "label": "2",
        }
        "KC_3": {
            "key": "NE_3",
            "label": "3",
        }
        "KC_4": {
            "key": "NE_4",
            "label": "4",
        }
        "KC_5": {
            "key": "NE_5",
            "label": "5",
        }
        "KC_6": {
            "key": "NE_6",
            "label": "6",
        }
        "KC_7": {
            "key": "NE_7",
            "label": "7",
        }
        "KC_8": {
            "key": "NE_8",
            "label": "8",
        }
        "KC_9": {
            "key": "NE_9",
            "label": "9",
        }
        "KC_0": {
            "key": "NE_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "NE_MINS",
            "label": "-",
        }
        "KC_EQL": {
            "key": "NE_GRV",
            "label": "` (dead)",
        }
        "KC_Q": {
            "key": "NE_X",
            "label": "X",
        }
        "KC_W": {
            "key": "NE_V",
            "label": "V",
        }
        "KC_E": {
            "key": "NE_L",
            "label": "L",
        }
        "KC_R": {
            "key": "NE_C",
            "label": "C",
        }
        "KC_T": {
            "key": "NE_W",
            "label": "W",
        }
        "KC_Y": {
            "key": "NE_K",
            "label": "K",
        }
        "KC_U": {
            "key": "NE_H",
            "label": "H",
        }
        "KC_I": {
            "key": "NE_G",
            "label": "G",
        }
        "KC_O": {
            "key": "NE_F",
            "label": "F",
        }
        "KC_P": {
            "key": "NE_Q",
            "label": "Q",
        }
        "KC_LBRC": {
            "key": "NE_SS",
            "label": "ß",
        }
        "KC_RBRC": {
            "key": "NE_ACUT",
            "label": "´ (dead)",
        }
        "KC_CAPS": {
            "key": "NE_L3L",
            "label": "(layer 3)",
        }
        "KC_A": {
            "key": "NE_U",
            "label": "U",
        }
        "KC_S": {
            "key": "NE_I",
            "label": "I",
        }
        "KC_D": {
            "key": "NE_A",
            "label": "A",
        }
        "KC_F": {
            "key": "NE_E",
            "label": "E",
        }
        "KC_G": {
            "key": "NE_O",
            "label": "O",
        }
        "KC_H": {
            "key": "NE_S",
            "label": "S",
        }
        "KC_J": {
            "key": "NE_N",
            "label": "N",
        }
        "KC_K": {
            "key": "NE_R",
            "label": "R",
        }
        "KC_L": {
            "key": "NE_T",
            "label": "T",
        }
        "KC_SCLN": {
            "key": "NE_D",
            "label": "D",
        }
        "KC_QUOT": {
            "key": "NE_Y",
            "label": "Y",
        }
        "KC_NUHS": {
            "key": "NE_L3R",
            "label": "(layer 3)",
        }
        "KC_NUBS": {
            "key": "NE_L4L",
            "label": "(layer 4)",
        }
        "KC_Z": {
            "key": "NE_UDIA",
            "label": "Ü",
        }
        "KC_X": {
            "key": "NE_ODIA",
            "label": "Ö",
        }
        "KC_C": {
            "key": "NE_ADIA",
            "label": "Ä",
        }
        "KC_V": {
            "key": "NE_P",
            "label": "P",
        }
        "KC_B": {
            "key": "NE_Z",
            "label": "Z",
        }
        "KC_N": {
            "key": "NE_B",
            "label": "B",
        }
        "KC_M": {
            "key": "NE_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "NE_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "NE_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "NE_J",
            "label": "J",
        }
        "KC_ALGR": {
            "key": "NE_L4R",
            "label": "(layer 4)",
        }
    }
}
