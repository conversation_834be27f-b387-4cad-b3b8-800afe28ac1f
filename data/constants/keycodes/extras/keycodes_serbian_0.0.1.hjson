{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ` │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ ' │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Љ │ Њ │ Е │ Р │ Т │ З │ У │ И │ О │ П │ Ш │ Ђ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ А │ С │ Д │ Ф │ Г │ Х │ Ј │ К │ Л │ Ч │ Ћ │ Ж │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ Ѕ │ Џ │ Ц │ В │ Б │ Н │ М │ , │ . │ - │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "RS_GRV",
            "label": "`",
        }
        "KC_1": {
            "key": "RS_1",
            "label": "1",
        }
        "KC_2": {
            "key": "RS_2",
            "label": "2",
        }
        "KC_3": {
            "key": "RS_3",
            "label": "3",
        }
        "KC_4": {
            "key": "RS_4",
            "label": "4",
        }
        "KC_5": {
            "key": "RS_5",
            "label": "5",
        }
        "KC_6": {
            "key": "RS_6",
            "label": "6",
        }
        "KC_7": {
            "key": "RS_7",
            "label": "7",
        }
        "KC_8": {
            "key": "RS_8",
            "label": "8",
        }
        "KC_9": {
            "key": "RS_9",
            "label": "9",
        }
        "KC_0": {
            "key": "RS_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "RS_QUOT",
            "label": "' (dead)",
        }
        "KC_EQL": {
            "key": "RS_PLUS",
            "label": "+",
        }
        "KC_Q": {
            "key": "RS_LJE",
            "label": "Љ",
        }
        "KC_W": {
            "key": "RS_NJE",
            "label": "Њ",
        }
        "KC_E": {
            "key": "RS_IE",
            "label": "Е",
        }
        "KC_R": {
            "key": "RS_ER",
            "label": "Р",
        }
        "KC_T": {
            "key": "RS_TE",
            "label": "Т",
        }
        "KC_Y": {
            "key": "RS_ZE",
            "label": "З",
        }
        "KC_U": {
            "key": "RS_U",
            "label": "У",
        }
        "KC_I": {
            "key": "RS_I",
            "label": "И",
        }
        "KC_O": {
            "key": "RS_O",
            "label": "О",
        }
        "KC_P": {
            "key": "RS_PE",
            "label": "П",
        }
        "KC_LBRC": {
            "key": "RS_SHA",
            "label": "Ш",
        }
        "KC_RBRC": {
            "key": "RS_DJE",
            "label": "Ђ",
        }
        "KC_A": {
            "key": "RS_A",
            "label": "А",
        }
        "KC_S": {
            "key": "RS_ES",
            "label": "С",
        }
        "KC_D": {
            "key": "RS_DE",
            "label": "Д",
        }
        "KC_F": {
            "key": "RS_EF",
            "label": "Ф",
        }
        "KC_G": {
            "key": "RS_GHE",
            "label": "Г",
        }
        "KC_H": {
            "key": "RS_HA",
            "label": "Х",
        }
        "KC_J": {
            "key": "RS_JE",
            "label": "Ј",
        }
        "KC_K": {
            "key": "RS_KA",
            "label": "К",
        }
        "KC_L": {
            "key": "RS_EL",
            "label": "Л",
        }
        "KC_SCLN": {
            "key": "RS_CHE",
            "label": "Ч",
        }
        "KC_QUOT": {
            "key": "RS_TSHE",
            "label": "Ћ",
        }
        "KC_NUHS": {
            "key": "RS_ZHE",
            "label": "Ж",
        }
        "KC_NUBS": {
            "key": "RS_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "RS_DZE",
            "label": "Ѕ",
        }
        "KC_X": {
            "key": "RS_DZHE",
            "label": "Џ",
        }
        "KC_C": {
            "key": "RS_TSE",
            "label": "Ц",
        }
        "KC_V": {
            "key": "RS_VE",
            "label": "В",
        }
        "KC_B": {
            "key": "RS_BE",
            "label": "Б",
        }
        "KC_N": {
            "key": "RS_EN",
            "label": "Н",
        }
        "KC_M": {
            "key": "RS_EM",
            "label": "М",
        }
        "KC_COMM": {
            "key": "RS_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "RS_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "RS_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ~ │ ! │ " │ # │ $ │ % │ & │ / │ ( │ ) │ = │ ? │ * │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │   │ ; │ : │ _ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(RS_GRV)": {
            "key": "RS_TILD",
            "label": "~",
        }
        "S(RS_1)": {
            "key": "RS_EXLM",
            "label": "!",
        }
        "S(RS_2)": {
            "key": "RS_DQUO",
            "label": "\"",
        }
        "S(RS_3)": {
            "key": "RS_HASH",
            "label": "#",
        }
        "S(RS_4)": {
            "key": "RS_DLR",
            "label": "$",
        }
        "S(RS_5)": {
            "key": "RS_PERC",
            "label": "%",
        }
        "S(RS_6)": {
            "key": "RS_AMPR",
            "label": "&",
        }
        "S(RS_7)": {
            "key": "RS_SLSH",
            "label": "/",
        }
        "S(RS_8)": {
            "key": "RS_LPRN",
            "label": "(",
        }
        "S(RS_9)": {
            "key": "RS_RPRN",
            "label": ")",
        }
        "S(RS_0)": {
            "key": "RS_EQL",
            "label": "=",
        }
        "S(RS_QUOT)": {
            "key": "RS_QUES",
            "label": "?",
        }
        "S(RS_PLUS)": {
            "key": "RS_ASTR",
            "label": "*",
        }
        "S(RS_LABK)": {
            "key": "RS_RABK",
            "label": ">",
        }
        "S(RS_COMM)": {
            "key": "RS_SCLN",
            "label": ";",
        }
        "S(RS_DOT)": {
            "key": "RS_COLN",
            "label": ":",
        }
        "S(RS_MINS)": {
            "key": "RS_UNDS",
            "label": "_",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │   │   │   │   │   │   │   │   │   │   │   │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │ € │   │   │   │   │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │   │   │   │   │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(RS_IE)": {
            "key": "RS_EURO",
            "label": "€",
        }
    }
}
