{"keycodes": {"0x7C00": {"group": "quantum", "key": "QK_BOOTLOADER", "aliases": ["QK_BOOT"]}, "0x7C01": {"group": "quantum", "key": "QK_REBOOT", "aliases": ["QK_RBT"]}, "0x7C02": {"group": "quantum", "key": "QK_DEBUG_TOGGLE", "aliases": ["DB_TOGG"]}, "0x7C03": {"group": "quantum", "key": "QK_CLEAR_EEPROM", "aliases": ["EE_CLR"]}, "0x7C04": {"group": "quantum", "key": "QK_MAKE"}, "0x7C10": {"group": "quantum", "key": "QK_AUTO_SHIFT_DOWN", "aliases": ["AS_DOWN"]}, "0x7C11": {"group": "quantum", "key": "QK_AUTO_SHIFT_UP", "aliases": ["AS_UP"]}, "0x7C12": {"group": "quantum", "key": "QK_AUTO_SHIFT_REPORT", "aliases": ["AS_RPT"]}, "0x7C13": {"group": "quantum", "key": "QK_AUTO_SHIFT_ON", "aliases": ["AS_ON"]}, "0x7C14": {"group": "quantum", "key": "QK_AUTO_SHIFT_OFF", "aliases": ["AS_OFF"]}, "0x7C15": {"group": "quantum", "key": "QK_AUTO_SHIFT_TOGGLE", "aliases": ["AS_TOGG"]}, "0x7C16": {"group": "quantum", "key": "QK_GRAVE_ESCAPE", "aliases": ["QK_GESC"]}, "0x7C17": {"group": "quantum", "key": "QK_VELOCIKEY_TOGGLE", "aliases": ["VK_TOGG"]}, "0x7C18": {"group": "quantum", "key": "QK_SPACE_CADET_LEFT_CTRL_PARENTHESIS_OPEN", "aliases": ["SC_LCPO"]}, "0x7C19": {"group": "quantum", "key": "QK_SPACE_CADET_RIGHT_CTRL_PARENTHESIS_CLOSE", "aliases": ["SC_RCPC"]}, "0x7C1A": {"group": "quantum", "key": "QK_SPACE_CADET_LEFT_SHIFT_PARENTHESIS_OPEN", "aliases": ["SC_LSPO"]}, "0x7C1B": {"group": "quantum", "key": "QK_SPACE_CADET_RIGHT_SHIFT_PARENTHESIS_CLOSE", "aliases": ["SC_RSPC"]}, "0x7C1C": {"group": "quantum", "key": "QK_SPACE_CADET_LEFT_ALT_PARENTHESIS_OPEN", "aliases": ["SC_LAPO"]}, "0x7C1D": {"group": "quantum", "key": "QK_SPACE_CADET_RIGHT_ALT_PARENTHESIS_CLOSE", "aliases": ["SC_RAPC"]}, "0x7C1E": {"group": "quantum", "key": "QK_SPACE_CADET_RIGHT_SHIFT_ENTER", "aliases": ["SC_SENT"]}, "0x7C20": {"group": "quantum", "key": "QK_OUTPUT_AUTO", "aliases": ["OU_AUTO"]}, "0x7C21": {"group": "quantum", "key": "QK_OUTPUT_USB", "aliases": ["OU_USB"]}, "0x7C22": {"group": "quantum", "key": "QK_OUTPUT_BLUETOOTH", "aliases": ["OU_BT"]}, "0x7C30": {"group": "quantum", "key": "QK_UNICODE_MODE_NEXT", "aliases": ["UC_NEXT"]}, "0x7C31": {"group": "quantum", "key": "QK_UNICODE_MODE_PREVIOUS", "aliases": ["UC_PREV"]}, "0x7C32": {"group": "quantum", "key": "QK_UNICODE_MODE_MACOS", "aliases": ["UC_MAC"]}, "0x7C33": {"group": "quantum", "key": "QK_UNICODE_MODE_LINUX", "aliases": ["UC_LINX"]}, "0x7C34": {"group": "quantum", "key": "QK_UNICODE_MODE_WINDOWS", "aliases": ["UC_WIN"]}, "0x7C35": {"group": "quantum", "key": "QK_UNICODE_MODE_BSD", "aliases": ["UC_BSD"]}, "0x7C36": {"group": "quantum", "key": "QK_UNICODE_MODE_WINCOMPOSE", "aliases": ["UC_WINC"]}, "0x7C37": {"group": "quantum", "key": "QK_UNICODE_MODE_EMACS", "aliases": ["UC_EMAC"]}, "0x7C40": {"group": "quantum", "key": "QK_HAPTIC_ON", "aliases": ["HF_ON"]}, "0x7C41": {"group": "quantum", "key": "QK_HAPTIC_OFF", "aliases": ["HF_OFF"]}, "0x7C42": {"group": "quantum", "key": "QK_HAPTIC_TOGGLE", "aliases": ["HF_TOGG"]}, "0x7C43": {"group": "quantum", "key": "QK_HAPTIC_RESET", "aliases": ["HF_RST"]}, "0x7C44": {"group": "quantum", "key": "QK_HAPTIC_FEEDBACK_TOGGLE", "aliases": ["HF_FDBK"]}, "0x7C45": {"group": "quantum", "key": "QK_HAPTIC_BUZZ_TOGGLE", "aliases": ["HF_BUZZ"]}, "0x7C46": {"group": "quantum", "key": "QK_HAPTIC_MODE_NEXT", "aliases": ["HF_NEXT"]}, "0x7C47": {"group": "quantum", "key": "QK_HAPTIC_MODE_PREVIOUS", "aliases": ["HF_PREV"]}, "0x7C48": {"group": "quantum", "key": "QK_HAPTIC_CONTINUOUS_TOGGLE", "aliases": ["HF_CONT"]}, "0x7C49": {"group": "quantum", "key": "QK_HAPTIC_CONTINUOUS_UP", "aliases": ["HF_CONU"]}, "0x7C4A": {"group": "quantum", "key": "QK_HAPTIC_CONTINUOUS_DOWN", "aliases": ["HF_COND"]}, "0x7C4B": {"group": "quantum", "key": "QK_HAPTIC_DWELL_UP", "aliases": ["HF_DWLU"]}, "0x7C4C": {"group": "quantum", "key": "QK_HAPTIC_DWELL_DOWN", "aliases": ["HF_DWLD"]}, "0x7C50": {"group": "quantum", "key": "QK_COMBO_ON", "aliases": ["CM_ON"]}, "0x7C51": {"group": "quantum", "key": "QK_COMBO_OFF", "aliases": ["CM_OFF"]}, "0x7C52": {"group": "quantum", "key": "QK_COMBO_TOGGLE", "aliases": ["CM_TOGG"]}, "0x7C53": {"group": "quantum", "key": "QK_DYNAMIC_MACRO_RECORD_START_1", "aliases": ["DM_REC1"]}, "0x7C54": {"group": "quantum", "key": "QK_DYNAMIC_MACRO_RECORD_START_2", "aliases": ["DM_REC2"]}, "0x7C55": {"group": "quantum", "key": "QK_DYNAMIC_MACRO_RECORD_STOP", "aliases": ["DM_RSTP"]}, "0x7C56": {"group": "quantum", "key": "QK_DYNAMIC_MACRO_PLAY_1", "aliases": ["DM_PLY1"]}, "0x7C57": {"group": "quantum", "key": "QK_DYNAMIC_MACRO_PLAY_2", "aliases": ["DM_PLY2"]}, "0x7C58": {"group": "quantum", "key": "QK_LEADER", "aliases": ["QK_LEAD"]}, "0x7C59": {"group": "quantum", "key": "QK_LOCK"}, "0x7C5A": {"group": "quantum", "key": "QK_ONE_SHOT_ON", "aliases": ["OS_ON"]}, "0x7C5B": {"group": "quantum", "key": "QK_ONE_SHOT_OFF", "aliases": ["OS_OFF"]}, "0x7C5C": {"group": "quantum", "key": "QK_ONE_SHOT_TOGGLE", "aliases": ["OS_TOGG"]}, "0x7C5D": {"group": "quantum", "key": "QK_KEY_OVERRIDE_TOGGLE", "aliases": ["KO_TOGG"]}, "0x7C5E": {"group": "quantum", "key": "QK_KEY_OVERRIDE_ON", "aliases": ["KO_ON"]}, "0x7C5F": {"group": "quantum", "key": "QK_KEY_OVERRIDE_OFF", "aliases": ["KO_OFF"]}, "0x7C60": {"group": "quantum", "key": "QK_SECURE_LOCK", "aliases": ["SE_LOCK"]}, "0x7C61": {"group": "quantum", "key": "QK_SECURE_UNLOCK", "aliases": ["SE_UNLK"]}, "0x7C62": {"group": "quantum", "key": "QK_SECURE_TOGGLE", "aliases": ["SE_TOGG"]}, "0x7C63": {"group": "quantum", "key": "QK_SECURE_REQUEST", "aliases": ["SE_REQ"]}, "0x7C70": {"group": "quantum", "key": "QK_DYNAMIC_TAPPING_TERM_PRINT", "aliases": ["DT_PRNT"]}, "0x7C71": {"group": "quantum", "key": "QK_DYNAMIC_TAPPING_TERM_UP", "aliases": ["DT_UP"]}, "0x7C72": {"group": "quantum", "key": "QK_DYNAMIC_TAPPING_TERM_DOWN", "aliases": ["DT_DOWN"]}, "0x7C73": {"group": "quantum", "key": "QK_CAPS_WORD_TOGGLE", "aliases": ["CW_TOGG"]}, "0x7C74": {"group": "quantum", "key": "QK_AUTOCORRECT_ON", "aliases": ["AC_ON"]}, "0x7C75": {"group": "quantum", "key": "QK_AUTOCORRECT_OFF", "aliases": ["AC_OFF"]}, "0x7C76": {"group": "quantum", "key": "QK_AUTOCORRECT_TOGGLE", "aliases": ["AC_TOGG"]}}}