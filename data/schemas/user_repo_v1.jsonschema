{"$schema": "https://json-schema.org/draft/2020-12/schema#", "$id": "qmk.user_repo.v1", "title": "User Repository Information", "type": "object", "definitions": {"build_target": {"oneOf": [{"$ref": "./definitions.jsonschema#/keyboard_keymap_tuple"}, {"$ref": "./definitions.jsonschema#/json_file_path"}]}}, "required": ["userspace_version", "build_targets"], "properties": {"userspace_version": {"type": "string", "enum": ["1.0"]}, "build_targets": {"type": "array", "items": {"$ref": "#/definitions/build_target"}}}}