{"$id": "qmk.api.keyboard.v1", "allOf": [{"$ref": "./keyboard.jsonschema#"}, {"properties": {"keymaps": {"type": "object", "properties": {"url": {"type": "string"}}}, "parse_errors": {"$ref": "./definitions.jsonschema#/string_array"}, "parse_warnings": {"$ref": "./definitions.jsonschema#/string_array"}, "processor_type": {"type": "string"}, "protocol": {"type": "string"}, "keyboard_folder": {"type": "string"}, "platform": {"type": "string"}}}]}