{"$schema": "https://json-schema.org/draft/2020-12/schema#", "$id": "qmk.keymap.v1", "title": "Keymap Information", "type": "object", "properties": {"author": {"type": "string"}, "converter": {"type": "string", "minLength": 1, "pattern": "^[a-z][0-9a-z_]*$"}, "host_language": {"$ref": "./definitions.jsonschema#/text_identifier"}, "keyboard": {"$ref": "./definitions.jsonschema#/text_identifier"}, "keymap": {"$ref": "./definitions.jsonschema#/text_identifier"}, "layout": {"$ref": "./definitions.jsonschema#/layout_macro"}, "layers": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}}, "encoders": {"type": "array", "items": {"type": "array", "items": {"type": "object", "required": ["ccw", "cw"], "properties": {"ccw": {"type": "string"}, "cw": {"type": "string"}}}}}, "macros": {"type": "array", "items": {"type": "array", "items": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": false, "properties": {"action": {"type": "string", "enum": ["beep", "delay", "down", "tap", "up"]}, "keycodes": {"type": "array", "items": {"$ref": "./definitions.jsonschema#/text_identifier"}}, "duration": {"$ref": "./definitions.jsonschema#/unsigned_int"}}}]}}}, "keycodes": {"$ref": "./definitions.jsonschema#/keycode_decl_array"}, "config": {"$ref": "./keyboard.jsonschema#"}, "notes": {"type": "string"}, "modules": {"type": "array", "items": {"type": "string"}}}}