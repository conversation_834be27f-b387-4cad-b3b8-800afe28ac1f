{
    // Format for each entry:
    // "<alias>": {
    //     "target": "<keyboard_folder>"
    // }
    //

    /* This list of aliases is for testing purposes -- ensures "linked list" recursive traversal works correctly. */
    "_test_a": { "target": "_test_b" },
    "_test_b": { "target": "_test_c" },
    "_test_c": { "target": "planck/rev6" },

    /* The main list of aliases for moved keyboards within QMK. */
    "2_milk": {
        "target": "spaceman/2_milk"
    },
    "absinthe": {
        "target": "keyhive/absinthe"
    },
    "aeboards/constellation": {
        "target": "aeboards/constellation/rev1"
    },
    "aeboards/ext65": {
        "target": "aeboards/ext65/rev1"
    },
    "ai03/equinox": {
        "target": "ai03/equinox/rev1"
    },
    "alice": {
        "target": "tgr/alice"
    },
    "amj40": {
        "target": "amjkeyboard/amj40"
    },
    "amj60": {
        "target": "amjkeyboard/amj60"
    },
    "amj96": {
        "target": "amjkeyboard/amj96"
    },
    "amjpad": {
        "target": "amjkeyboard/amjpad"
    },
    "angel64": {
        "target": "kakunpc/angel64/alpha"
    },
    "ashpil/modelm_usbc": {
        "target": "ibm/model_m/ashpil_usbc"
    },
    "at101_blackheart": {
        "target": "viktus/at101_bh"
    },
    "at101_bh": {
        "target": "viktus/at101_bh"
    },
    "atom47/rev2": {
        "target": "evyd13/atom47/rev2"
    },
    "atom47/rev3": {
        "target": "evyd13/atom47/rev3"
    },
    "bakeneko60": {
        "target": "kkatano/bakeneko60"
    },
    "bakeneko65": {
        "target": "kkatano/bakeneko65/rev2"
    },
    "bakeneko80": {
        "target": "kkatano/bakeneko80"
    },
    "bear_face": {
        "target": "bear_face/v1"
    },
    "bm16a": {
        "target": "kprepublic/bm16a/v1"
    },
    "bm16s": {
        "target": "kprepublic/bm16s"
    },
    "bm40hsrgb": {
        "target": "kprepublic/bm40hsrgb"
    },
    "bm43a": {
        "target": "kprepublic/bm43a"
    },
    "bm60poker": {
        "target": "kprepublic/bm60hsrgb_poker/rev1"
    },
    "bm60rgb": {
        "target": "kprepublic/bm60hsrgb/rev1"
    },
    "bm60rgb_iso": {
        "target": "kprepublic/bm60hsrgb_iso/rev1"
    },
    "bm68rgb": {
        "target": "kprepublic/bm68hsrgb/rev1"
    },
    "bpiphany/pegasushoof": {
        "target": "bpiphany/pegasushoof/2013"
    },
    "brick": {
        "target": "pauperboards/brick"
    },
    "chavdai40": {
        "target": "chavdai40/rev1"
    },
    "candybar/lefty": {
        "target": "tkc/candybar/lefty"
    },
    "candybar/righty": {
        "target": "tkc/candybar/righty"
    },
    "canoe": {
        "target": "percent/canoe"
    },
    "clawsome/gamebuddy": {
        "target": "clawsome/gamebuddy/v1_0"
    },
    "cmm_studio/saka68": {
        "target": "cmm_studio/saka68/solder"
    },
    "converter/modelm101": {
        "target": "ibm/model_m/teensypp"
    },
    "converter/modelm101_teensy2": {
        "target": "ibm/model_m/teensy2"
    },
    "converter/modelm_ssk": {
        "target": "ibm/model_m_ssk/teensypp_ssk"
    },
    "cospad": {
        "target": "kprepublic/cospad"
    },
    "crkbd/rev1/legacy": {
        "target": "crkbd/rev1"
    },
    "crkbd/rev1/common": {
        "target": "crkbd/rev1"
    },
    "custommk/genesis": {
        "target": "custommk/genesis/rev1"
    },
    "cxt_studio":{
        "target":"cxt_studio/12e4"
    },
    "daisy": {
        "target": "ktec/daisy"
    },
    "deemen17/de60": {
        "target": "deemen17/de60/r1"
    },
    "dp3000": {
        "target": "dp3000/rev1"
    },
    "drakon": {
        "target": "jagdpietr/drakon"
    },
    "durgod/k320": {
        "target": "durgod/k320/base"
    },
    "durgod/k3x0/k320": {
        "target": "durgod/k320/base"
    },
    "durgod/hades": {
        "target": "durgod/dgk6x/hades_ansi"
    },
    "durgod/hades_ansi": {
        "target": "durgod/dgk6x/hades_ansi"
    },
    "durgod/hades_iso": {
        "target": "durgod/dgk6x/hades_iso"
    },
    "dztech/dz60rgb": {
        "target": "dztech/dz60rgb/v1"
    },
    "dztech/dz60rgb_ansi": {
        "target": "dztech/dz60rgb_ansi/v1"
    },
    "dztech/dz60rgb_wkl": {
        "target": "dztech/dz60rgb_wkl/v1"
    },
    "dztech/dz65rgb": {
        "target": "dztech/dz65rgb/v1"
    },
    "dztech/volcano660": {
        "target": "ilumkb/volcano660"
    },
    "dztech/og60": {
        "target": "dztech/tofu60"
    },
    "eek": {
        "target": "eek/silk_down"
    },
    "epoch80": {
        "target": "kbdfans/epoch80"
    },
    "era/klein": {
        "target": "era/sirind/klein_sd"
	},
    "ergodone": {
        "target": "ktec/ergodone"
    },
    "ergodox_stm32": {
        "target": "handwired/ergodox_stm32"
    },
    "ergoinu": {
        "target": "dm9records/ergoinu"
    },
    "ergosaurus": {
        "target": "keyhive/ergosaurus"
    },
    "exclusive/e85": {
        "target": "exclusive/e85/hotswap"
    },
    "gh60": {
        "target": "gh60/revc"
    },
    "gmmk/pro": {
        "target": "gmmk/pro/rev1/ansi"
    },
    "gmmk/pro/ansi": {
        "target": "gmmk/pro/rev1/ansi"
    },
    "gmmk/pro/iso": {
        "target": "gmmk/pro/rev1/iso"
    },
    "handwired/dactyl_manuform/3x5_3": {
        "target": "handwired/dactyl_minidox"
    },
    "handwired/dactyl_manuform/6x6_kinesis": {
        "target": "handwired/dactyl_kinesis"
    },
    "handwired/dactyl_manuform/dmote/62key": {
        "target": "handwired/dmote"
    },
    "handwired/ferris": {
        "target": "ferris/0_1"
    },
    "handwired/ibm122m": {
        "target": "ibm/model_m_122/ibm122m"
    },
    "handwired/p1800fl": {
        "target": "team0110/p1800fl"
    },
    "handwired/jscotto/scotto9": {
        "target": "handwired/scottokeebs/scotto9"
    },
    "handwired/jscotto/scotto36": {
        "target": "handwired/scottokeebs/scotto36"
    },
    "handwired/jscotto/scotto40": {
        "target": "handwired/scottokeebs/scotto40"
    },
    "handwired/jscotto/scottocmd": {
        "target": "handwired/scottokeebs/scottocmd"
    },
    "handwired/jscotto/scottostarter": {
        "target": "handwired/scottokeebs/scottostarter"
    },
    "helix/pico/sc/back": {
        "target": "helix/pico/sc"
    },
    "helix/pico/sc/under": {
        "target": "helix/pico/sc"
    },
    "helix/rev2/back/oled": {
        "target": "helix/rev2/back"
    },
    "helix/rev2/oled": {
        "target": "helix/rev2"
    },
    "helix/rev2/oled/back": {
        "target": "helix/rev2/back"
    },
    "helix/rev2/oled/under": {
        "target": "helix/rev2/under"
    },
    "helix/rev2/sc/back": {
        "target": "helix/rev2/sc"
    },
    "helix/rev2/sc/oled": {
        "target": "helix/rev2/sc"
    },
    "helix/rev2/sc/oledback": {
        "target": "helix/rev2/sc"
    },
    "helix/rev2/sc/oledunder": {
        "target": "helix/rev2/sc"
    },
    "helix/rev2/sc/under": {
        "target": "helix/rev2/sc"
    },
    "helix/rev2/under": {
        "target": "helix/rev2/sc"
    },
    "helix/rev2/under/oled": {
        "target": "helix/rev2/under"
    },
    "honeycomb": {
        "target": "keyhive/honeycomb"
    },
    "hub16": {
        "target": "joshajohnson/hub16"
    },
    "hub20": {
        "target": "joshajohnson/hub20"
    },
    "idb_60": {
        "target": "idb/idb_60"
    },
    "idobo": {
        "target": "idobao/id75/v1"
    },
    "jacky_studio/piggy60": {
        "target": "jacky_studio/piggy60/rev1"
    },
    "jj40": {
        "target": "kprepublic/jj40/rev1"
    },
    "jj4x4": {
        "target": "kprepublic/jj4x4"
    },
    "jj50": {
        "target": "kprepublic/jj50/rev1"
    },
    "jm60": {
        "target": "kbdfans/jm60"
    },
    "jones": {
        "target": "jones/v03_1"
    },
    "kamigakushi": {
        "target": "jaykeeb/kamigakushi"
    },
    "katana60": {
        "target": "rominronin/katana60/rev1"
    },
    "kbdfans/kbd67mkiirgb": {
        "target": "kbdfans/kbd67/mkiirgb"
    },
    "kbdfans/kbd67/mkiirgb": {
        "target": "kbdfans/kbd67/mkiirgb/v1"
    },
    "keebio/chocopad": {
        "target": "keebio/chocopad/rev1"
    },
    "keebio/dsp40": {
        "target": "keebio/dsp40/rev1"
    },
    "keycapsss/plaid_pad": {
        "target": "keycapsss/plaid_pad/rev1"
    },
    "keyten/kt60hs_t": {
        "target": "keyten/kt60hs_t/v1"
    },
    "kira75": {
        "target": "kira/kira75"
    },
    "kira80": {
        "target": "kira/kira80"
    },
    "kudox": {
        "target": "kumaokobo/kudox/rev1"
    },
    "kudox/columner": {
        "target": "kumaokobo/kudox/columner"
    },
    "kudox/rev1": {
        "target": "kumaokobo/kudox/rev1"
    },
    "kudox/rev2": {
        "target": "kumaokobo/kudox/rev2"
    },
    "kudox/rev3": {
        "target": "kumaokobo/kudox/rev3"
    },
    "kudox_full": {
        "target": "kumaokobo/kudox_full/rev1"
    },
    "kudox_full/rev1": {
        "target": "kumaokobo/kudox_full/rev1"
    },
    "kudox_game": {
        "target": "kumaokobo/kudox_game/rev1"
    },
    "kudox_game/rev1": {
        "target": "kumaokobo/kudox_game/rev1"
    },
    "kudox_game/rev2": {
        "target": "kumaokobo/kudox_game/rev2"
    },
    "kyria": {
        "target": "splitkb/kyria"
    },
    "laser_ninja/pumpkin_pad": {
        "target": "laser_ninja/pumpkinpad"
    },
    "lattice60": {
        "target": "keyhive/lattice60"
    },
    "lazydesigners/the60": {
        "target": "lazydesigners/the60/rev1"
    },
    "lfkeyboards/lfk78": {
        "target": "lfkeyboards/lfk78/revj"
    },
    "lfkeyboards/smk65": {
        "target": "lfkeyboards/smk65/revb"
    },
    "m3v3van": {
        "target": "matthewdias/m3n3van"
    },
    "maartenwut/atom47/rev2": {
        "target": "evyd13/atom47/rev2"
    },
    "maartenwut/atom47/rev3": {
        "target": "evyd13/atom47/rev3"
    },
    "maartenwut/eon40": {
        "target": "evyd13/eon40"
    },
    "maartenwut/eon65": {
        "target": "evyd13/eon65"
    },
    "maartenwut/eon75": {
        "target": "evyd13/eon75"
    },
    "maartenwut/eon87": {
        "target": "evyd13/eon87"
    },
    "maartenwut/eon95": {
        "target": "evyd13/eon95"
    },
    "maartenwut/gh80_1800": {
        "target": "evyd13/gh80_1800"
    },
    "maartenwut/gh80_3700": {
        "target": "evyd13/gh80_3700"
    },
    "maartenwut/minitomic": {
        "target": "evyd13/minitomic"
    },
    "maartenwut/mx5160": {
        "target": "evyd13/mx5160"
    },
    "maartenwut/nt660": {
        "target": "evyd13/nt660"
    },
    "maartenwut/omrontkl": {
        "target": "evyd13/omrontkl"
    },
    "maartenwut/plain60": {
        "target": "evyd13/plain60"
    },
    "maartenwut/pockettype": {
        "target": "evyd13/pockettype"
    },
    "maartenwut/quackfire": {
        "target": "evyd13/quackfire"
    },
    "maartenwut/solheim68": {
        "target": "evyd13/solheim68"
    },
    "maartenwut/ta65": {
        "target": "evyd13/ta65"
    },
    "maartenwut/wasdat": {
        "target": "evyd13/wasdat"
    },
    "maartenwut/wasdat_code": {
        "target": "evyd13/wasdat_code"
    },
    "maartenwut/wonderland": {
        "target": "evyd13/wonderland"
    },
    "matchstickworks/southpad": {
        "target": "matchstickworks/southpad/rev2/"
    },
    "matrix/m12og": {
        "target": "matrix/m12og/rev1"
    },
    "mechlovin/hannah910": {
        "target": "mechlovin/hannah910/rev1"
    },
    "mechlovin/adelais/rgb_led": {
        "target": "mechlovin/adelais/rgb_led/rev1"
    },
    "mechlovin/adelais/standard_led": {
        "target": "mechlovin/adelais/standard_led/arm/rev2"
    },
    "mechlovin/delphine": {
        "target": "mechlovin/delphine/mono_led"
    },
    "mechlovin/hannah60rgb": {
        "target": "mechlovin/hannah60rgb/rev1"
    },
    "mechlovin/hannah65/mechlovin9": {
        "target": "mechlovin/mechlovin9/rev1"
    },
    "mechlovin/hex4b": {
        "target": "mechlovin/hex4b/rev1"
    },
    "melgeek/z70ultra": {
        "target": "melgeek/z70ultra/rev1"
    },
    "mechlovin/hannah65": {
        "target": "mechlovin/hannah65/rev1"
    },
    "minim": {
        "target": "matthewdias/minim"
    },
    "mnk1800s": {
        "target": "monokei/mnk1800s"
    },
    "mnk50": {
        "target": "monokei/mnk50"
    },
    "mnk75": {
        "target": "monokei/mnk75"
    },
    "model01": {
        "target": "keyboardio/model01"
    },
    "model_v": {
        "target": "matthewdias/model_v"
    },
    "m0lly": {
        "target": "tkc/m0lly"
    },
    "montsinger/rebound": {
        "target": "montsinger/rebound/rev1"
    },
    "moonlander": {
        "target": "zsa/moonlander"
    },
    "mschwingen/modelm": {
        "target": "ibm/model_m/mschwingen"
    },
    "oddball": {
        "target": "oddball/v1"
    },
    "omnikey_blackheart": {
        "target": "viktus/omnikey_bh"
    },
    "omnikey_bh": {
        "target": "viktus/omnikey_bh"
    },
    "opus": {
        "target": "keyhive/opus"
    },
    "pabile/p20": {
        "target": "pabile/p20/ver1"
    },
    "pancake/feather": {
        "target": "spaceman/pancake/rev1/feather"
    },
    "pancake/promicro": {
        "target": "spaceman/pancake/rev1/promicro"
    },
    "peiorisboards/ixora": {
        "target": "coarse/ixora"
    },
    "pico": {
        "target": "kumaokobo/pico/65keys"
    },
    "pico/65keys": {
        "target": "kumaokobo/pico/65keys"
    },
    "pico/70keys": {
        "target": "kumaokobo/pico/70keys"
    },
    "plaid": {
        "target": "dm9records/plaid"
    },
    "plain60": {
        "target": "evyd13/plain60"
    },
    "planck/ez": {
        "target": "zsa/planck_ez/base"
    },
    "planck/ez/base": {
        "target": "zsa/planck_ez/base"
    },
    "planck/ez/glow": {
        "target": "zsa/planck_ez/glow"
    },
    "ploopyco/trackball": {
        "target": "ploopyco/trackball/rev1_005"
    },
    "plywrks/ply8x": {
        "target": "plywrks/ply8x/solder"
    },
    "polilla": {
        "target": "polilla/rev1"
    },
    "primekb/prime_l": {
        "target": "primekb/prime_l/v1"
    },
    "primekb/prime_l_v2": {
        "target": "primekb/prime_l/v2"
    },
    "projectkb/alice": {
        "target": "projectkb/alice/rev1"
    },
    "rama/koyu": {
        "target": "wilba_tech/rama_works_koyu"
    },
    "rama/m6_a": {
        "target": "wilba_tech/rama_works_m6_a"
    },
    "rama/m6_b": {
        "target": "wilba_tech/rama_works_m6_b"
    },
    "rama/m10_b": {
        "target": "wilba_tech/rama_works_m10_b"
    },
    "rama/m60_a": {
        "target": "wilba_tech/rama_works_m60_a"
    },
    "rama/u80_a": {
        "target": "wilba_tech/rama_works_u80_a"
    },
    "ramonimbao/herringbone": {
        "target": "rmi_kb/herringbone/v1"
    },
    "ramonimbao/mona": {
        "target": "rmi_kb/mona/v1"
    },
    "redox_w": {
        "target": "redox/wireless"
    },
    "rgbkb/pan": {
        "target": "rgbkb/pan/rev1/32a"
    },
    "rgbkb/pan/rev1": {
        "target": "rgbkb/pan/rev1/32a"
    },
    "romac": {
        "target": "kingly_keys/romac"
    },
    "ropro": {
        "target": "kingly_keys/ropro"
    },
    "satan": {
        "target": "gh60/satan"
    },
    "skog": {
        "target": "percent/skog"
    },
    "smallice": {
        "target": "keyhive/smallice"
    },
    "southpole": {
        "target": "keyhive/southpole"
    },
    "speedo": {
        "target": "cozykeys/speedo/v2"
    },
    "staryu": {
        "target": "ktec/staryu"
    },
    "stoutgat": {
        "target": "tkw/stoutgat/v1"
    },
    "suihankey": {
        "target": "kakunpc/suihankey/split/alpha"
    },
    "ta65": {
        "target": "evyd13/ta65"
    },
    "tartan": {
        "target": "dm9records/tartan"
    },
    "tkc1800": {
        "target": "tkc/tkc1800"
    },
    "tkw/stoutgat/v2": {
        "target": "tkw/stoutgat/v2/f411"
    },
    "tokyo60": {
        "target": "tokyokeyboard/tokyo60"
    },
    "txuu": {
        "target": "matthewdias/txuu"
    },
    "underscore33": {
        "target": "tominabox1/underscore33/rev1"
    },
    "vinta": {
        "target": "coarse/vinta"
    },
    "wasdat": {
        "target": "evyd13/wasdat"
    },
    "westfoxtrot/cypher": {
        "target": "westfoxtrot/cypher/rev1"
    },
    "whale/sk": {
        "target": "whale/sk/v3"
    },
    "xd002": {
        "target": "xiudi/xd002"
    },
    "xd004": {
        "target": "xiudi/xd004/v1"
    },
    "xd60": {
        "target": "xiudi/xd60/rev2"
    },
    "xd68": {
        "target": "xiudi/xd68"
    },
    "xd75": {
        "target": "xiudi/xd75"
    },
    "xd84": {
        "target": "xiudi/xd84"
    },
    "xd84pro": {
        "target": "xiudi/xd84pro"
    },
    "xd87": {
        "target": "xiudi/xd87"
    },
    "xd96": {
        "target": "xiudi/xd96"
    },
    "xelus/dawn60": {
        "target": "xelus/dawn60/rev1"
    },
    "xelus/valor": {
        "target": "xelus/valor/rev1"
    },
    "z150_blackheart": {
        "target": "viktus/z150_bh"
    },
    "z150_bh":{
        "target": "viktus/z150_bh"
    },
    "zeal60": {
        "target": "wilba_tech/zeal60"
    },
    "zeal65": {
        "target": "wilba_tech/zeal65"
    },
    // Moved during 2022 Q1 cycle
    "6ball": {
        "target": "maple_computing/6ball"
    },
    "7skb": {
        "target": "salicylic_acid3/7skb"
    },
    "7splus": {
        "target": "salicylic_acid3/7splus"
    },
    "acr60": {
        "target": "mechkeys/acr60"
    },
    "adalyn": {
        "target": "tominabox1/adalyn"
    },
    "ajisai74": {
        "target": "salicylic_acid3/ajisai74"
    },
    "aleth42": {
        "target": "25keys/aleth42"
    },
    "alicia_cook": {
        "target": "ibnuda/alicia_cook"
    },
    "allison": {
        "target": "prototypist/allison"
    },
    "allison_numpad": {
        "target": "prototypist/allison_numpad"
    },
    "alu84": {
        "target": "mechkeys/alu84"
    },
    "angel17": {
        "target": "kakunpc/angel17"
    },
    "angel64/alpha": {
        "target": "kakunpc/angel64/alpha"
    },
    "angel64/rev1": {
        "target": "kakunpc/angel64/rev1"
    },
    "arch_36": {
        "target": "obosob/arch_36"
    },
    "bakeneko65/rev2": {
        "target": "kkatano/bakeneko65/rev2"
    },
    "bakeneko65/rev3": {
        "target": "kkatano/bakeneko65/rev3"
    },
    "barleycorn": {
        "target": "yiancardesigns/barleycorn"
    },
    "bat43/rev1": {
        "target": "dailycraft/bat43/rev1"
    },
    "bat43/rev2": {
        "target": "dailycraft/bat43/rev2"
    },
    "bigseries/1key": {
        "target": "woodkeys/bigseries/1key"
    },
    "bigseries/2key": {
        "target": "woodkeys/bigseries/2key"
    },
    "bigseries/3key": {
        "target": "woodkeys/bigseries/3key"
    },
    "bigseries/4key": {
        "target": "woodkeys/bigseries/4key"
    },
    "bkf": {
        "target": "drhigsby/bkf"
    },
    "business_card/alpha": {
        "target": "kakunpc/business_card/alpha"
    },
    "business_card/beta": {
        "target": "kakunpc/business_card/beta"
    },
    "butterstick": {
        "target": "gboards/butterstick"
    },
    "c39": {
        "target": "maple_computing/c39"
    },
    "cassette42": {
        "target": "25keys/cassette42"
    },
    "chidori": {
        "target": "kagizaraya/chidori"
    },
    "chili": {
        "target": "ydkb/chili"
    },
    "chimera_ergo": {
        "target": "glenpickle/chimera_ergo"
    },
    "chimera_ls": {
        "target": "glenpickle/chimera_ls"
    },
    "chimera_ortho": {
        "target": "glenpickle/chimera_ortho"
    },
    "chimera_ortho_plus": {
        "target": "glenpickle/chimera_ortho_plus"
    },
    "choc_taro": {
        "target": "kakunpc/choc_taro"
    },
    "choco60": {
        "target": "recompile_keys/choco60"
    },
    "christmas_tree": {
        "target": "maple_computing/christmas_tree"
    },
    "claw44/rev1": {
        "target": "dailycraft/claw44/rev1"
    },
    "cocoa40": {
        "target": "recompile_keys/cocoa40"
    },
    "comet46": {
        "target": "satt/comet46"
    },
    "cu24": {
        "target": "capsunlocked/cu24"
    },
    "cu75": {
        "target": "capsunlocked/cu75"
    },
    "cu80": {
        "target": "capsunlocked/cu80/v1"
    },
    "delilah": {
        "target": "rainkeebs/delilah"
    },
    "diverge3": {
        "target": "unikeyboard/diverge3"
    },
    "divergetm2": {
        "target": "unikeyboard/divergetm2"
    },
    "dozen0": {
        "target": "yynmt/dozen0"
    },
    "dubba175": {
        "target": "drhigsby/dubba175"
    },
    "eggman": {
        "target": "qpockets/eggman"
    },
    "enter67": {
        "target": "kezewa/enter67"
    },
    "enter80": {
        "target": "kezewa/enter80"
    },
    "ergo42": {
        "target": "biacco42/ergo42"
    },
    "ergoarrows": {
        "target": "salicylic_acid3/ergoarrows"
    },
    "ergodash/mini": {
        "target": "omkbd/ergodash/mini"
    },
    "ergodash/rev1": {
        "target": "omkbd/ergodash/rev1"
    },
    "ergodox_infinity": {
        "target": "input_club/ergodox_infinity"
    },
    "ergotaco": {
        "target": "gboards/ergotaco"
    },
    "espectro": {
        "target": "mechkeys/espectro"
    },
    "eu_isolation": {
        "target": "p3d/eu_isolation"
    },
    "felix": {
        "target": "unikeyboard/felix"
    },
    "flygone60/rev3": {
        "target": "shandoncodes/flygone60/rev3"
    },
    "four_banger": {
        "target": "bpiphany/four_banger"
    },
    "freyr": {
        "target": "hnahkb/freyr"
    },
    "geminate60": {
        "target": "weirdo/geminate60"
    },
    "gentleman65": {
        "target": "jkeys_design/gentleman65"
    },
    "georgi": {
        "target": "gboards/georgi"
    },
    "gergo": {
        "target": "gboards/gergo"
    },
    "getta25": {
        "target": "salicylic_acid3/getta25"
    },
    "gingham": {
        "target": "yiancardesigns/gingham"
    },
    "gurindam": {
        "target": "ibnuda/gurindam"
    },
    "halberd": {
        "target": "kagizaraya/halberd"
    },
    "handwired/hillside/0_1": {
        "target": "hillside/48/0_1"
    },
    "hecomi/alpha": {
        "target": "takashiski/hecomi/alpha"
    },
    "hfdkb/keyboard_sw/k83":{
        "target": "inland/kb83"
    },
    "hid_liber": {
        "target": "bpiphany/hid_liber"
    },
    "id67/default_rgb": {
        "target": "idobao/id67"
    },
    "id67/rgb": {
        "target": "idobao/id67"
    },
    "id80": {
        "target": "idobao/id80/v2/ansi"
    },
    "idobao/id80/v1/ansi": {
        "target": "idobao/id80/v2/ansi"
    },
    "idobao/id80/v1/iso": {
        "target": "idobao/id80/v2/iso"
    },
    "id87": {
        "target": "idobao/id87/v1"
    },
    "infinity60": {
        "target": "input_club/infinity60"
    },
    "ivy/rev1": {
        "target": "maple_computing/ivy/rev1"
    },
    "jisplit89": {
        "target": "salicylic_acid3/jisplit89"
    },
    "jnao": {
        "target": "maple_computing/jnao"
    },
    "just60": {
        "target": "ydkb/just60"
    },
    "k_type": {
        "target": "input_club/k_type"
    },
    "kagamidget": {
        "target": "yynmt/kagamidget"
    },
    "kelowna/rgb64": {
        "target": "weirdo/kelowna/rgb64"
    },
    "keychron/q0": {
        "target": "keychron/q0/base"
    },
    "keychron/q1": {
        "target": "keychron/q1v1/ansi"
    }
    "keychron/q4": {
        "target": "keychron/q4/ansi/v1"
    }
    "kmac": {
        "target": "kbdmania/kmac"
    }
    "kmac_pad": {
        "target": "kbdmania/kmac_pad"
    }
    "kprepublic/bm40hsrgb": {
        "target": "kprepublic/bm40hsrgb/rev1"
    },
    "kprepublic/bm65hsrgb_iso": {
        "target": "kprepublic/bm65hsrgb_iso/rev1"
    },
    "kprepublic/bm68hsrgb": {
        "target": "kprepublic/bm68hsrgb/rev1"
    },
    "late9/rev1": {
        "target": "ivndbt/late9/rev1"
    },
    "latin17rgb": {
        "target": "latincompass/latin17rgb"
    },
    "latin47ble": {
        "target": "latincompass/latin47ble"
    },
    "latin60rgb": {
        "target": "latincompass/latin60rgb"
    },
    "latin64ble": {
        "target": "latincompass/latin64ble"
    },
    "latin6rgb": {
        "target": "latincompass/latin6rgb"
    },
    "latinpad": {
        "target": "latincompass/latinpad"
    },
    "latinpadble": {
        "target": "latincompass/latinpadble"
    },
    "launchpad/rev1": {
        "target": "maple_computing/launchpad/rev1"
    },
    "lefty": {
        "target": "smoll/lefty/rev2"
    },
    "lefty/rev1": {
        "target": "smoll/lefty/rev1"
    },
    "lck75": {
        "target": "lyso1/lck75"
    },
    "le_chiffre": {
        "target": "tominabox1/le_chiffre"
    },
    "lefishe": {
        "target": "lyso1/lefishe"
    },
    "lets_split_eh/eh": {
        "target": "maple_computing/lets_split_eh"
    },
    "ls_60": {
        "target": "weirdo/ls_60"
    },
    "lpad": {
        "target": "laneware/lpad"
    },
    "lw67": {
        "target": "laneware/lw67"
    },
    "lw75": {
        "target": "laneware/lw75"
    },
    "m3n3van": {
        "target": "matthewdias/m3n3van"
    },
    "macro1": {
        "target": "laneware/macro1"
    },
    "maple_computing/lets_split_eh/eh": {
        "target": "maple_computing/lets_split_eh"
    },
    "massdrop/thekey": {
        "target": "drop/thekey/v1"
    },
    "massdrop/thekey_v2": {
        "target": "drop/thekey/v2"
    },
    "mechmini/v1": {
        "target": "mechkeys/mechmini/v1"
    },
    "mechmini/v2": {
        "target": "mechkeys/mechmini/v2"
    },
    "meira": {
        "target": "woodkeys/meira"
    },
    "meishi": {
        "target": "biacco42/meishi"
    },
    "meishi2": {
        "target": "biacco42/meishi2"
    },
    "melody96": {
        "target": "ymdk/melody96"
    },
    "miniaxe": {
        "target": "kagizaraya/miniaxe"
    },
    "minidox/rev1": {
        "target": "maple_computing/minidox/rev1"
    },
    "mino/hotswap": {
        "target": "shandoncodes/mino/hotswap"
    },
    "mino_plus/hotswap": {
        "target": "shandoncodes/mino_plus/hotswap"
    },
    "mino_plus/soldered": {
        "target": "shandoncodes/mino_plus/soldered"
    },
    "mio": {
        "target": "recompile_keys/mio"
    },
    "montex": {
        "target": "idobao/montex/v1"
    },
    "mt40": {
        "target": "mt/mt40"
    },
    "mt64rgb": {
        "target": "mt/mt64rgb"
    },
    "mt84": {
        "target": "mt/mt84"
    },
    "mt980": {
        "target": "mt/mt980"
    },
    "mt/ncr80/hotswap": {
        "target": "mt/ncr80/r2/hotswap"
    },
    "mt/ncr80/solder": {
        "target": "mt/ncr80/r2/solder"
    },
    "nafuda": {
        "target": "salicylic_acid3/nafuda"
    },
    "naiping/np64": {
        "target": "weirdo/naiping/np64"
    },
    "naiping/nphhkb": {
        "target": "weirdo/naiping/nphhkb"
    },
    "naiping/npminila": {
        "target": "weirdo/naiping/npminila"
    },
    "naked48": {
        "target": "salicylic_acid3/naked48"
    },
    "naked60": {
        "target": "salicylic_acid3/naked60"
    },
    "naked64": {
        "target": "salicylic_acid3/naked64"
    },
    "namecard2x4": {
        "target": "takashiski/namecard2x4"
    },
    "navi10": {
        "target": "keyhive/navi10"
    },
    "nebula12": {
        "target": "spaceholdings/nebula12"
    },
    "nebula68": {
        "target": "spaceholdings/nebula68"
    },
    "nebula68b": {
        "target": "spaceholdings/nebula68b"
    },
    "neopad/rev1": {
        "target": "ivndbt/neopad/rev1"
    },
    "niu_mini": {
        "target": "kbdfans/niu_mini"
    },
    "nk1": {
        "target": "novelkeys/nk1"
    },
    "nk65": {
        "target": "novelkeys/nk65"
    },
    "nk87": {
        "target": "novelkeys/nk87"
    },
    "nknl7en": {
        "target": "salicylic_acid3/nknl7en"
    },
    "nknl7jp": {
        "target": "salicylic_acid3/nknl7jp"
    },
    "nomu30": {
        "target": "recompile_keys/nomu30"
    },
    "novelpad": {
        "target": "novelkeys/novelpad"
    },
    "ogurec": {
        "target": "drhigsby/ogurec"
    },
    "otaku_split/rev0": {
        "target": "takashiski/otaku_split/rev0"
    },
    "otaku_split/rev1": {
        "target": "takashiski/otaku_split/rev1"
    },
    "owl8": {
        "target": "dailycraft/owl8"
    },
    "packrat": {
        "target": "drhigsby/packrat"
    },
    "pistachio": {
        "target": "rate/pistachio"
    },
    "pistachio_mp": {
        "target": "rate/pistachio_mp"
    },
    "pistachio_pro": {
        "target": "rate/pistachio_pro"
    },
    "plexus75": {
        "target": "checkerboards/plexus75"
    },
    "pursuit40": {
        "target": "checkerboards/pursuit40"
    },
    "pw88": {
        "target": "smoll/pw88"
    },
    "q4z": {
        "target": "p3d/q4z"
    },
    "qaz": {
        "target": "tominabox1/qaz"
    },
    "quark": {
        "target": "checkerboards/quark"
    },
    "rabbit_capture_plan": {
        "target": "kakunpc/rabbit_capture_plan"
    },
    "raindrop": {
        "target": "laneware/raindrop"
    },
    "ramonimbao/aelith": {
        "target": "rmi_kb/aelith"
    },
    "ramonimbao/chevron": {
        "target": "rmi_kb/chevron"
    },
    "ramonimbao/herringbone/pro": {
        "target": "rmi_kb/herringbone/pro"
    },
    "ramonimbao/herringbone/v1": {
        "target": "rmi_kb/herringbone/v1"
    },
    "ramonimbao/mona/v1": {
        "target": "rmi_kb/mona/v1"
    },
    "ramonimbao/mona/v1_1": {
        "target": "rmi_kb/mona/v1_1"
    },
    "ramonimbao/mona/v32a": {
        "target": "rmi_kb/mona/v32a"
    },
    "ramonimbao/squishy65": {
        "target": "rmi_kb/squishy65"
    },
    "ramonimbao/squishyfrl": {
        "target": "rmi_kb/squishyfrl"
    },
    "ramonimbao/squishytkl": {
        "target": "rmi_kb/squishytkl"
    },
    "ramonimbao/tkl_ff/v1": {
        "target": "rmi_kb/tkl_ff/v1"
    },
    "ramonimbao/tkl_ff/v2": {
        "target": "rmi_kb/tkl_ff/v2"
    },
    "ramonimbao/wete/v1": {
        "target": "rmi_kb/wete/v1"
    },
    "ramonimbao/wete/v2": {
        "target": "rmi_kb/wete/v2"
    },
    "rainkeeb": {
        "target": "rainkeebs/rainkeeb"
    },
    "reviung33": {
        "target": "reviung/reviung33"
    },
    "reviung34": {
        "target": "reviung/reviung34"
    },
    "reviung39": {
        "target": "reviung/reviung39"
    },
    "reviung41": {
        "target": "reviung/reviung41"
    },
    "reviung5": {
        "target": "reviung/reviung5"
    },
    "reviung53": {
        "target": "reviung/reviung53"
    },
    "reviung61": {
        "target": "reviung/reviung61"
    },
    "riot_pad": {
        "target": "shandoncodes/riot_pad"
    },
    "runner3680/3x6": {
        "target": "omkbd/runner3680/3x6"
    },
    "runner3680/3x7": {
        "target": "omkbd/runner3680/3x7"
    },
    "runner3680/3x8": {
        "target": "omkbd/runner3680/3x8"
    },
    "runner3680/4x6": {
        "target": "omkbd/runner3680/4x6"
    },
    "runner3680/4x7": {
        "target": "omkbd/runner3680/4x7"
    },
    "runner3680/4x8": {
        "target": "omkbd/runner3680/4x8"
    },
    "runner3680/5x6": {
        "target": "omkbd/runner3680/5x6"
    },
    "runner3680/5x6_5x8": {
        "target": "omkbd/runner3680/5x6_5x8"
    },
    "runner3680/5x7": {
        "target": "omkbd/runner3680/5x7"
    },
    "runner3680/5x8": {
        "target": "omkbd/runner3680/5x8"
    },
    "saevus/cor": {
        "target": "concreteflowers/cor"
    },
    "saevus/cor_tkl": {
        "target": "concreteflowers/cor_tkl"
    },
    "scarletbandana": {
        "target": "woodkeys/scarletbandana"
    },
    "scythe": {
        "target": "kagizaraya/scythe"
    },
    "seigaiha": {
        "target": "yiancardesigns/seigaiha"
    },
    "setta21": {
        "target": "salicylic_acid3/setta21"
    },
    "soda/mango": {
        "target": "magic_force/mf17"
    },
    "soda/pocket": {
        "target": "magic_force/mf34"
    },
    "space_space/rev1": {
        "target": "qpockets/space_space/rev1"
    },
    "space_space/rev2": {
        "target": "qpockets/space_space/rev2"
    },
    "spacey": {
        "target": "p3d/spacey"
    },
    "spiderisland/winry25tc": {
        "target": "winry/winry25tc"
    },
    "splitreus62": {
        "target": "nacly/splitreus62"
    },
    "squiggle/rev1": {
        "target": "ibnuda/squiggle/rev1"
    },
    "standaside": {
        "target": "edi/standaside"
    },
    "steal_this_keyboard": {
        "target": "obosob/steal_this_keyboard"
    },
    "stella": {
        "target": "hnahkb/stella"
    },
    "studiokestra/line_tkl": {
        "target": "studiokestra/line_friends_tkl"
    },
    "suihankey/alpha": {
        "target": "kakunpc/suihankey/alpha"
    },
    "suihankey/rev1": {
        "target": "kakunpc/suihankey/rev1"
    },
    "suihankey/split": {
        "target": "kakunpc/suihankey/split"
    },
    "synapse": {
        "target": "p3d/synapse"
    },
    "the_ruler": {
        "target": "maple_computing/the_ruler"
    },
    "thedogkeyboard": {
        "target": "kakunpc/thedogkeyboard"
    },
    "tiger910": {
        "target": "weirdo/tiger910"
    },
    "treadstone32": {
        "target": "marksard/treadstone32"
    },
    "treadstone48/rev1": {
        "target": "marksard/treadstone48/rev1"
    },
    "treadstone48/rev2": {
        "target": "marksard/treadstone48/rev2"
    },
    "tronguylabs/m122_3270": {
        "target": "ibm/model_m_122/m122_3270/teensy"
    },
    "tw40": {
        "target": "p3d/tw40"
    },
    "ua62": {
        "target": "nacly/ua62"
    },
    "underscore33/rev1": {
        "target": "tominabox1/underscore33/rev1"
    },
    "underscore33/rev2": {
        "target": "tominabox1/underscore33/rev2"
    },
    "uno": {
        "target": "keyhive/uno"
    },
    "ut472": {
        "target": "keyhive/ut472"
    },
    "vn66": {
        "target": "hnahkb/vn66"
    },
    "w1_at": {
        "target": "geonworks/w1_at"
    },
    "wallaby": {
        "target": "kkatano/wallaby"
    },
    "wanten": {
        "target": "qpockets/wanten"
    },
    "wheatfield/blocked65": {
        "target": "mt/blocked65"
    },
    "wheatfield/split75": {
        "target": "mt/split75"
    },
    "whitefox": {
        "target": "input_club/whitefox"
    },
    "wings42/rev1": {
        "target": "dailycraft/wings42/rev1"
    },
    "wings42/rev1_extkeys": {
        "target": "dailycraft/wings42/rev1_extkeys"
    },
    "wings42/rev2": {
        "target": "dailycraft/wings42/rev2"
    },
    "yasui": {
        "target": "rainkeebs/yasui"
    },
    "yd60mq": {
        "target": "ymdk/yd60mq"
    },
    "yd68": {
        "target": "ydkb/yd68"
    },
    "ymd75": {
        "target": "ymdk/ymd75/rev1"
    },
    "ymd96": {
        "target": "ymdk/ymd96"
    },
    "ymdk/id75": {
        "target": "ymdk/id75/f103"
    },
    "ymdk_np21": {
        "target": "ymdk/np21"
    },
    "yugo_m/model_m_101": {
        "target": "ibm/model_m/yugo_m"
    },
    "yurei": {
        "target": "kkatano/yurei"
    },
    "z12": {
        "target": "zigotica/z12"
    },
    "z34": {
        "target": "zigotica/z34"
    },
    "zinc": {
        "target": "25keys/zinc"
    },
    "zinc/rev1": {
        "target": "25keys/zinc/rev1"
    },
    "zinc/reva": {
        "target": "25keys/zinc/reva"
    },
    // Moved during 2023 Q4 cycle
    "ymdk/melody96": {
        "target": "ymdk/melody96/soldered"
    },
    // Moved during 2024 Q2 cycle
    "kprepublic/jj40": {
        "target": "kprepublic/jj40/rev1"
    },
    "kprepublic/jj50": {
        "target": "kprepublic/jj50/rev1"
    },
    "dnworks/9973": {
        "target": "dnworks/tkl87"
    },
    // DEFAULT_FOLDER removed during 2025 Q1 cycle
    "0_sixty": {
        "target": "0_sixty/base"
    },
    "0xcb/splaytoraid": {
        "target": "0xcb/splaytoraid/rp2040_ce"
    },
    "1upkeyboards/pi40": {
        "target": "1upkeyboards/pi40/mit_v1_0"
    },
    "1upkeyboards/pi50": {
        "target": "1upkeyboards/pi50/grid"
    },
    "1upkeyboards/sweet16": {
        "target": "1upkeyboards/sweet16/v1"
    },
    "25keys/aleth42": {
        "target": "25keys/aleth42/rev1"
    },
    "25keys/zinc": {
        "target": "25keys/zinc/rev1"
    },
    "40percentclub/i75": {
        "target": "40percentclub/i75/promicro"
    },
    "40percentclub/polyandry": {
        "target": "40percentclub/polyandry/promicro"
    },
    "8pack": {
        "target": "8pack/rev12"
    },
    "adkb96": {
        "target": "adkb96/rev1"
    },
    "adm42": {
        "target": "adm42/rev4"
    },
    "aeboards/satellite": {
        "target": "aeboards/satellite/rev1"
    },
    "atreus": {
        "target": "atreus/astar"
    },
    "atreyu": {
        "target": "atreyu/rev1"
    },
    "biacco42/ergo42": {
        "target": "biacco42/ergo42/rev1"
    },
    "binepad/bn009": {
        "target": "binepad/bn009/r2"
    },
    "binepad/bnr1": {
        "target": "binepad/bnr1/v2"
    },
    "boston_meetup": {
        "target": "boston_meetup/2019"
    },
    "bpiphany/frosty_flake": {
        "target": "bpiphany/frosty_flake/20140521"
    },
    "buzzard": {
        "target": "buzzard/rev1"
    },
    "cannonkeys/db60": {
        "target": "cannonkeys/db60/rev2"
    },
    "clickety_split/leeloo": {
        "target": "clickety_split/leeloo/rev3"
    },
    "converter/palm_usb": {
        "target": "converter/palm_usb/stowaway"
    },
    "crkbd": {
        "target": "crkbd/rev1"
    },
    "dailycraft/bat43": {
        "target": "dailycraft/bat43/rev2"
    },
    "dailycraft/claw44": {
        "target": "dailycraft/claw44/rev1"
    },
    "dailycraft/sandbox": {
        "target": "dailycraft/sandbox/rev2"
    },
    "dailycraft/wings42": {
        "target": "dailycraft/wings42/rev2"
    },
    "delikeeb/vanana": {
        "target": "delikeeb/vanana/rev2"
    },
    "delikeeb/waaffle/rev3": {
        "target": "delikeeb/waaffle/rev3/pro_micro"
    },
    "deltasplit75": {
        "target": "deltasplit75/v2"
    },
    "drhigsby/ogurec": {
        "target": "drhigsby/ogurec/left_pm"
    },
    "duck/octagon": {
        "target": "duck/octagon/v2"
    },
    "duck/orion": {
        "target": "duck/orion/v3"
    },
    "ducky/one2mini": {
        "target": "ducky/one2mini/1861st"
    },
    "ducky/one2sf": {
        "target": "ducky/one2sf/1967st"
    },
    "dumbpad": {
        "target": "dumbpad/v0x"
    },
    "durgod/galaxy": {
        "target": "durgod/dgk6x/galaxy"
    },
    "durgod/venus": {
        "target": "durgod/dgk6x/venus"
    },
    "dztech/tofu/ii": {
        "target": "dztech/tofu/ii/v1"
    },
    "dztech/tofu/jr": {
        "target": "dztech/tofu/jr/v1"
    },
    "eco": {
        "target": "eco/rev2"
    },
    "ergoslab": {
        "target": "ergoslab/rev1"
    },
    "ergotravel": {
        "target": "ergotravel/rev1"
    },
    "evyd13/atom47": {
        "target": "evyd13/atom47/rev4"
    },
    "fortitude60": {
        "target": "fortitude60/rev1"
    },
    "fruitykeeb/fruitbar/r1": {
        "target": "fruitykeeb/fruitbar/r1/promicro"
    },
    "ghs/jem": {
        "target": "ghs/jem/soldered"
    },
    "hadron": {
        "target": "hadron/ver2"
    },
    "handwired/bento": {
        "target": "handwired/bento/rev1"
    },
    "handwired/dactyl_manuform/6x6": {
        "target": "handwired/dactyl_manuform/6x6/promicro"
    },
    "handwired/meck_tkl": {
        "target": "handwired/meck_tkl/blackpill_f401"
    },
    "handwired/ms_sculpt_mobile": {
        "target": "handwired/ms_sculpt_mobile/teensy2pp"
    },
    "handwired/onekey": {
        "target": "handwired/onekey/promicro"
    },
    "handwired/pill60": {
        "target": "handwired/pill60/bluepill"
    },
    "handwired/postageboard": {
        "target": "handwired/postageboard/mini"
    },
    "handwired/qc60": {
        "target": "handwired/qc60/proto"
    },
    "handwired/sono1": {
        "target": "handwired/sono1/t2pp"
    },
    "handwired/splittest": {
        "target": "handwired/splittest/promicro"
    },
    "handwired/stef9998/split_5x7": {
        "target": "handwired/stef9998/split_5x7/rev1"
    },
    "handwired/symmetric70_proto/promicro": {
        "target": "handwired/symmetric70_proto/promicro/base"
    },
    "handwired/symmetric70_proto/proton_c": {
        "target": "handwired/symmetric70_proto/proton_c/base"
    },
    "handwired/unk": {
        "target": "handwired/unk/rev1"
    },
    "handwired/xealous": {
        "target": "handwired/xealous/rev1"
    },
    "hillside/46": {
        "target": "hillside/46/0_1"
    },
    "hhkb/ansi": {
        "target": "hhkb/ansi/32u4"
    },
    "hillside/48": {
        "target": "hillside/48/0_1"
    },
    "hillside/52": {
        "target": "hillside/52/0_1"
    },
    "ibnuda/squiggle": {
        "target": "ibnuda/squiggle/rev1"
    },
    "idobao/id80/v1": {
        "target": "idobao/id80/v2/ansi"
    },
    "idobao/id80/v3": {
        "target": "idobao/id80/v3/ansi"
    },
    "inett_studio/sq80": {
        "target": "inett_studio/sq80/hotswap_layout_i"
    },
    "input_club/infinity60": {
        "target": "input_club/infinity60/led"
    },
    "jacky_studio/bear_65": {
        "target": "jacky_studio/bear_65/rev1"
    },
    "jacky_studio/piggy60/rev1": {
        "target": "jacky_studio/piggy60/rev1/solder"
    },
    "jadookb/jkb65": {
        "target": "jadookb/jkb65/r1"
    },
    "jian": {
        "target": "jian/rev2"
    },
    "jiran": {
        "target": "jiran/rev1"
    },
    "jorne": {
        "target": "jorne/rev1"
    },
    "junco": {
        "target": "junco/rev1"
    },
    "kakunpc/angel17": {
        "target": "kakunpc/angel17/rev1"
    },
    "kakunpc/angel64": {
        "target": "kakunpc/angel64/rev1"
    },
    "kakunpc/business_card": {
        "target": "kakunpc/business_card/beta"
    },
    "kakunpc/suihankey": {
        "target": "kakunpc/suihankey/rev1"
    },
    "kakunpc/suihankey/split": {
        "target": "kakunpc/suihankey/split/rev1"
    },
    "kapcave/paladinpad": {
        "target": "kapcave/paladinpad/rev2"
    },
    "kapl": {
        "target": "kapl/rev1"
    },
    "kbdfans/d45": {
        "target": "kbdfans/d45/v2"
    },
    "kbdfans/kbd75": {
        "target": "kbdfans/kbd75/rev1"
    },
    "keebio/bdn9": {
        "target": "keebio/bdn9/rev1"
    },
    "keebio/convolution": {
        "target": "keebio/convolution/rev1"
    },
    "keebio/foldkb": {
        "target": "keebio/foldkb/rev1"
    },
    "keebio/kbo5000": {
        "target": "keebio/kbo5000/rev1"
    },
    "keebio/levinson": {
        "target": "keebio/levinson/rev2"
    },
    "keebio/quefrency": {
        "target": "keebio/quefrency/rev1"
    },
    "keebio/rorschach": {
        "target": "keebio/rorschach/rev1"
    },
    "keebio/sinc": {
        "target": "keebio/sinc/rev1"
    },
    "keebio/viterbi": {
        "target": "keebio/viterbi/rev2"
    },
    "keycapsss/kimiko": {
        "target": "keycapsss/kimiko/rev2"
    },
    "keyhive/navi10": {
        "target": "keyhive/navi10/rev3"
    },
    "keyhive/uno": {
        "target": "keyhive/uno/rev1"
    },
    "kin80": {
        "target": "kin80/blackpill401"
    },
    "kumaokobo/kudox_full": {
        "target": "kumaokobo/kudox_full/rev1"
    },
    "kumaokobo/kudox_game": {
        "target": "kumaokobo/kudox_game/rev2"
    },
    "kumaokobo/kudox": {
        "target": "kumaokobo/kudox/rev3"
    },
    "kumaokobo/pico": {
        "target": "kumaokobo/pico/65keys"
    },
    "lazydesigners/dimple": {
        "target": "lazydesigners/dimple/staggered/rev1"
    },
    "lets_split": {
        "target": "lets_split/rev2"
    },
    "lfkeyboards/lfk87": {
        "target": "lfkeyboards/lfk78/revc"
    },
    "lily58": {
        "target": "lily58/rev1"
    },
    "lime": {
        "target": "lime/rev1"
    },
    "maple_computing/christmas_tree": {
        "target": "maple_computing/christmas_tree/v2017"
    },
    "maple_computing/ivy": {
        "target": "maple_computing/ivy/rev1"
    },
    "maple_computing/launchpad": {
        "target": "maple_computing/launchpad/rev1"
    },
    "maple_computing/minidox": {
        "target": "maple_computing/minidox/rev1"
    },
    "mariorion_v25": {
        "target": "mariorion_v25/prod"
    },
    "marksard/rhymestone": {
        "target": "marksard/rhymestone/rev1"
    },
    "marksard/treadstone32": {
        "target": "marksard/treadstone32/rev1"
    },
    "marksard/treadstone48": {
        "target": "marksard/treadstone48/rev1"
    },
    "maxipad": {
        "target": "maxipad/promicro"
    },
    "mechkeys/mechmini": {
        "target": "mechkeys/mechmini/v2"
    },
    "mechllama/g35": {
        "target": "mechllama/g35/v2"
    },
    "mechlovin/adelais": {
        "target": "mechlovin/adelais/standard_led/arm/rev2"
    },
    "mechlovin/adelais/standard_led/arm/rev4": {
        "target": "mechlovin/adelais/standard_led/arm/rev4/stm32f303"
    },
    "mechlovin/adelais/standard_led/arm": {
        "target": "mechlovin/adelais/standard_led/arm/rev2"
    },
    "mechlovin/adelais/standard_led/avr": {
        "target": "mechlovin/adelais/standard_led/avr/rev1"
    },
    "mechlovin/hannah65/rev1": {
        "target": "mechlovin/hannah65/rev1/haus"
    },
    "mechlovin/infinity87/rev1": {
        "target": "mechlovin/infinity87/rev1/standard"
    },
    "mechlovin/infinity87": {
        "target": "mechlovin/infinity87/rgb_rev1"
    },
    "mechlovin/mechlovin9": {
        "target": "mechlovin/mechlovin9/rev1"
    },
    "mechlovin/olly/jf": {
        "target": "mechlovin/olly/jf/rev1"
    },
    "mechlovin/zed1800": {
        "target": "mechlovin/zed1800/saber"
    },
    "mechlovin/zed65/no_backlight": {
        "target": "mechlovin/zed65/no_backlight/wearhaus66"
    },
    "mechlovin/zed65": {
        "target": "mechlovin/zed65/no_backlight/wearhaus66"
    },
    "mechwild/bde": {
        "target": "mechwild/bde/rev2"
    },
    "mechwild/mokulua": {
        "target": "mechwild/mokulua/standard"
    },
    "mechwild/obe/f401": {
        "target": "mechwild/obe/f401/base"
    },
    "mechwild/obe/f411": {
        "target": "mechwild/obe/f411/base"
    },
    "mechwild/obe": {
        "target": "mechwild/obe/f401/base"
    },
    "mechwild/waka60/f401": {
        "target": "mechwild/waka60/f401/base"
    },
    "mechwild/waka60/f411": {
        "target": "mechwild/waka60/f411/base"
    },
    "mechwild/waka60": {
        "target": "mechwild/waka60/f401/base"
    },
    "murcielago": {
        "target": "murcielago/rev1"
    },
    "nullbitsco/scramble": {
        "target": "nullbitsco/scramble/v2"
    },
    "omkbd/ergodash": {
        "target": "omkbd/ergodash/rev1"
    },
    "omkbd/runner3680": {
        "target": "omkbd/runner3680/5x8"
    },
    "orthodox": {
        "target": "orthodox/rev3"
    },
    "peej/rosaline": {
        "target": "peej/rosaline/staggered"
    },
    "peej/tripel": {
        "target": "peej/tripel/left"
    },
    "peranekofactory/tone": {
        "target": "peranekofactory/tone/rev2"
    },
    "phase_studio/titan65": {
        "target": "phase_studio/titan65/hotswap"
    },
    "pica40": {
        "target": "pica40/rev2"
    },
    "pinky": {
        "target": "pinky/3"
    },
    "ploopyco/madromys": {
        "target": "ploopyco/madromys/rev1_001"
    },
    "ploopyco/trackball_mini": {
        "target": "ploopyco/trackball_mini/rev1_001"
    },
    "ploopyco/trackball_nano": {
        "target": "ploopyco/trackball_nano/rev1_001"
    },
    "ploopyco/trackball_thumb": {
        "target": "ploopyco/trackball_thumb/rev1_001"
    },
    "primekb/meridian": {
        "target": "primekb/meridian/ktr1010"
    },
    "primekb/prime_e": {
        "target": "primekb/prime_e/std"
    },
    "program_yoink": {
        "target": "program_yoink/staggered"
    },
    "projectcain/vault35": {
        "target": "projectcain/vault35/atmega32u4"
    },
    "qpockets/space_space": {
        "target": "qpockets/space_space/rev2"
    },
    "qwertyydox": {
        "target": "qwertyydox/rev1"
    },
    "rate/pistachio": {
        "target": "rate/pistachio/rev2"
    },
    "recompile_keys/choco60": {
        "target": "recompile_keys/choco60/rev1"
    },
    "recompile_keys/nomu30": {
        "target": "recompile_keys/nomu30/rev1"
    },
    "redox/rev1": {
        "target": "redox/rev1/base"
    },
    "rgbkb/mun": {
        "target": "rgbkb/mun/rev1"
    },
    "rgbkb/sol3": {
        "target": "rgbkb/sol3/rev1"
    },
    "rgbkb/sol": {
        "target": "rgbkb/sol/rev2"
    },
    "rgbkb/zen": {
        "target": "rgbkb/zen/rev2"
    },
    "rgbkb/zygomorph": {
        "target": "rgbkb/zygomorph/rev1"
    },
    "rmi_kb/herringbone": {
        "target": "rmi_kb/herringbone/v1"
    },
    "rmi_kb/mona": {
        "target": "rmi_kb/mona/v1_1"
    },
    "rmi_kb/tkl_ff": {
        "target": "rmi_kb/tkl_ff/v1"
    },
    "rmi_kb/wete": {
        "target": "rmi_kb/wete/v2"
    },
    "rookiebwoy/late9": {
        "target": "ivndbt/late9/rev1"
    },
    "rookiebwoy/neopad": {
        "target": "ivndbt/neopad/rev1"
    },
    "ivndbt/late9": {
        "target": "ivndbt/late9/rev1"
    },
    "ivndbt/neopad": {
        "target": "ivndbt/neopad/rev1"
    },
    "rura66": {
        "target": "rura66/rev1"
    },
    "salicylic_acid3/7skb": {
        "target": "salicylic_acid3/7skb/rev1"
    },
    "salicylic_acid3/getta25": {
        "target": "salicylic_acid3/getta25/rev1"
    },
    "salicylic_acid3/jisplit89": {
        "target": "salicylic_acid3/jisplit89/rev1"
    },
    "salicylic_acid3/naked48": {
        "target": "salicylic_acid3/naked48/rev1"
    },
    "salicylic_acid3/naked60": {
        "target": "salicylic_acid3/naked60/rev1"
    },
    "salicylic_acid3/naked64": {
        "target": "salicylic_acid3/naked64/rev1"
    },
    "salicylic_acid3/setta21": {
        "target": "salicylic_acid3/setta21/rev1"
    },
    "sawnsprojects/okayu": {
        "target": "sawnsprojects/okayu/stm32f072"
    },
    "smoll/lefty": {
        "target": "smoll/lefty/rev2"
    },
    "sofle": {
        "target": "sofle/rev1"
    },
    "spaceholdings/nebula68b": {
        "target": "spaceholdings/nebula68b/solder"
    },
    "spacetime": {
        "target": "spacetime/rev1"
    },
    "splitkb/aurora/corne": {
        "target": "splitkb/aurora/corne/rev1"
    },
    "splitkb/aurora/helix": {
        "target": "splitkb/aurora/helix/rev1"
    },
    "splitkb/aurora/lily58": {
        "target": "splitkb/aurora/lily58/rev1"
    },
    "splitkb/aurora/sofle_v2": {
        "target": "splitkb/aurora/sofle_v2/rev1"
    },
    "splitkb/aurora/sweep": {
        "target": "splitkb/aurora/sweep/rev1"
    },
    "splitkb/kyria": {
        "target": "splitkb/kyria/rev3"
    },
    "splitkb/kyria/rev1": {
        "target": "splitkb/kyria/rev1/base"
    },
    "splitkb/kyria/rev2": {
        "target": "splitkb/kyria/rev2/base"
    },
    "splitty": {
        "target": "splitty/rev1"
    },
    "studiokestra/galatea": {
        "target": "studiokestra/galatea/rev1"
    },
    "takashiski/hecomi": {
        "target": "takashiski/hecomi/alpha"
    },
    "takashiski/namecard2x4": {
        "target": "takashiski/namecard2x4/rev2"
    },
    "teleport/native": {
        "target": "teleport/native/iso"
    },
    "themadnoodle/noodlepad": {
        "target": "themadnoodle/noodlepad/v1"
    },
    "tkw/grandiceps": {
        "target": "tkw/grandiceps/rev1"
    },
    "tominabox1/le_chiffre": {
        "target": "tominabox1/le_chiffre/rev1"
    },
    "tominabox1/littlefoot_lx": {
        "target": "tominabox1/littlefoot_lx/rev1"
    },
    "tominabox1/underscore33": {
        "target": "tominabox1/underscore33/rev1"
    },
    "trnthsn/e8ghty": {
        "target": "trnthsn/e8ghty/stm32f103"
    },
    "trnthsn/s6xty5neor2": {
        "target": "trnthsn/s6xty5neor2/stm32f103"
    },
    "tweetydabird/lotus58": {
        "target": "tweetydabird/lotus58/promicro"
    },
    "unison": {
        "target": "unison/v04"
    },
    "uzu42": {
        "target": "uzu42/rev1"
    },
    "vitamins_included": {
        "target": "vitamins_included/rev2"
    },
    "westm/westm68": {
        "target": "westm/westm68/rev2"
    },
    "westm/westm9": {
        "target": "westm/westm9/rev2"
    },
    "woodkeys/meira": {
        "target": "woodkeys/meira/promicro"
    },
    "work_louder/loop": {
        "target": "work_louder/loop/rev3"
    },
    "work_louder/work_board": {
        "target": "work_louder/work_board/rev3"
    },
    "yanghu/unicorne": {
        "target": "yanghu/unicorne/f411"
    },
    "yosino58": {
        "target": "yosino58/rev1"
    },
    "ymdk/yd60mq": {
        "target": "ymdk/yd60mq/12led"
    },
    "yushakobo/navpad/10": {
        "target": "yushakobo/navpad/10/rev1"
    },
    "yynmt/acperience12": {
        "target": "yynmt/acperience12/rev1"
    },
    "zsa/planck_ez": {
        "target": "zsa/planck_ez/base"
    }
}
