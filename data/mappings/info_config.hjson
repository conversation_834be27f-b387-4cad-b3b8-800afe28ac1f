// This file maps keys between `config.h` and `info.json`. It is used by QMK
// to correctly and consistently map back and forth between the two systems.
{
    // Format:
    // <config.h key>: {"info_key": <info.json key>, ["value_type": <value_type>], ["to_json": <true/false>], ["to_c": <true/false>]}
    // value_type: one of "array", "array.int", "bool, "flag", "int", "hex", "list", "mapping", "str", "raw"
    // to_json: Default `true`. Set to `false` to exclude this mapping from info.json
    // to_c: Default `true`. Set to `false` to exclude this mapping from config.h
    // warn_duplicate: Default `true`. Set to `false` to turn off warning when a value exists in both places
    // deprecated: Default `false`. Set to `true` to turn on warning when a value exists
    // invalid: Default `false`. Set to `true` to generate errors when a value exists
    // replace_with: use with a key marked deprecated or invalid to designate a replacement

    // APA102
    "APA102_CI_PIN": {"info_key": "apa102.clock_pin"},
    "APA102_DEFAULT_BRIGHTNESS": {"info_key": "apa102.default_brightness", "value_type": "int"},
    "APA102_DI_PIN": {"info_key": "apa102.data_pin"},

    // Audio
    "AUDIO_DEFAULT_ON": {"info_key": "audio.default.on", "value_type": "bool"},
    "AUDIO_DEFAULT_CLICKY_ON": {"info_key": "audio.default.clicky", "value_type": "bool"},
    "AUDIO_POWER_CONTROL_PIN": {"info_key": "audio.power_control.pin"},
    "AUDIO_POWER_CONTROL_PIN_ON_STATE": {"info_key": "audio.power_control.on_state", "value_type": "int" },
    "AUDIO_VOICES": {"info_key": "audio.voices", "value_type": "flag"},
    "SENDSTRING_BELL": {"info_key": "audio.macro_beep", "value_type": "flag"},

    // Backlight
    "BACKLIGHT_BREATHING": {"info_key": "backlight.breathing", "value_type": "flag"},
    "BACKLIGHT_CAPS_LOCK": {"info_key": "backlight.as_caps_lock", "value_type": "flag"},
    "BACKLIGHT_LEVELS": {"info_key": "backlight.levels", "value_type": "int"},
    "BACKLIGHT_LIMIT_VAL": {"info_key": "backlight.max_brightness", "value_type": "int"},
    "BACKLIGHT_ON_STATE": {"info_key": "backlight.on_state", "value_type": "int"},
    "BACKLIGHT_PIN": {"info_key": "backlight.pin"},
    "BACKLIGHT_PINS": {"info_key": "backlight.pins", "value_type": "array"},
    "BREATHING_PERIOD": {"info_key": "backlight.breathing_period", "value_type": "int"},
    "BACKLIGHT_DEFAULT_ON": {"info_key": "backlight.default.on", "value_type": "bool"},
    "BACKLIGHT_DEFAULT_BREATHING": {"info_key": "backlight.default.breathing", "value_type": "bool"},
    "BACKLIGHT_DEFAULT_LEVEL": {"info_key": "backlight.default.brightness", "value_type": "int"},

    // Bootmagic
    "BOOTMAGIC_COLUMN": {"info_key": "bootmagic.matrix.1", "value_type": "int"},
    "BOOTMAGIC_COLUMN_RIGHT": {"info_key": "split.bootmagic.matrix.1", "value_type": "int"},
    "BOOTMAGIC_ROW": {"info_key": "bootmagic.matrix.0", "value_type": "int"},
    "BOOTMAGIC_ROW_RIGHT": {"info_key": "split.bootmagic.matrix.0", "value_type": "int"},

    // Caps Word
    "BOTH_SHIFTS_TURNS_ON_CAPS_WORD": {"info_key": "caps_word.both_shifts_turns_on", "value_type": "flag"},
    "CAPS_WORD_IDLE_TIMEOUT": {"info_key": "caps_word.idle_timeout", "value_type": "int"},
    "CAPS_WORD_INVERT_ON_SHIFT": {"info_key": "caps_word.invert_on_shift", "value_type": "flag"},
    "DOUBLE_TAP_SHIFT_TURNS_ON_CAPS_WORD": {"info_key": "caps_word.double_tap_shift_turns_on", "value_type": "flag"},

    // Combos
    "COMBO_TERM": {"info_key": "combo.term", "value_type": "int"},

    "DIP_SWITCH_MATRIX_GRID": {"info_key": "dip_switch.matrix_grid", "value_type": "array.array.int", "to_json": false},
    "DIP_SWITCH_PINS": {"info_key": "dip_switch.pins", "value_type": "array"},
    "DIP_SWITCH_PINS_RIGHT": {"info_key": "split.dip_switch.right.pins", "value_type": "array"},

    // Dynamic Keymap
    "DYNAMIC_KEYMAP_EEPROM_MAX_ADDR": {"info_key": "dynamic_keymap.eeprom_max_addr", "value_type": "int"},
    "DYNAMIC_KEYMAP_LAYER_COUNT": {"info_key": "dynamic_keymap.layer_count", "value_type": "int"},

    // EEPROM
    "WEAR_LEVELING_BACKING_SIZE": {"info_key": "eeprom.wear_leveling.backing_size", "value_type": "int", "to_json": false},
    "WEAR_LEVELING_LOGICAL_SIZE": {"info_key": "eeprom.wear_leveling.logical_size", "value_type": "int", "to_json": false},

    // host
    "NKRO_DEFAULT_ON": {"info_key": "host.default.nkro", "value_type": "bool"},

    // Layer locking
    "LAYER_LOCK_IDLE_TIMEOUT": {"info_key": "layer_lock.timeout", "value_type": "int"},

    // Indicators
    "LED_CAPS_LOCK_PIN": {"info_key": "indicators.caps_lock"},
    "LED_NUM_LOCK_PIN": {"info_key": "indicators.num_lock"},
    "LED_SCROLL_LOCK_PIN": {"info_key": "indicators.scroll_lock"},
    "LED_COMPOSE_PIN": {"info_key": "indicators.compose"},
    "LED_KANA_PIN": {"info_key": "indicators.kana"},
    "LED_PIN_ON_STATE": {"info_key": "indicators.on_state", "value_type": "int"},

    // Joystick
    "JOYSTICK_AXIS_COUNT": {"info_key": "joystick.axis_count", "value_type": "int"},
    "JOYSTICK_AXIS_RESOLUTION": {"info_key": "joystick.axis_resolution", "value_type": "int"},
    "JOYSTICK_BUTTON_COUNT": {"info_key": "joystick.button_count", "value_type": "int"},

    // Leader Key
    "LEADER_PER_KEY_TIMING": {"info_key": "leader_key.timing", "value_type": "flag"},
    "LEADER_KEY_STRICT_KEY_PROCESSING": {"info_key": "leader_key.strict_processing", "value_type": "flag"},
    "LEADER_TIMEOUT": {"info_key": "leader_key.timeout", "value_type": "int"},

    // LED Matrix
    "LED_MATRIX_CENTER": {"info_key": "led_matrix.center_point", "value_type": "array.int"},
    "LED_MATRIX_KEYRELEASES": {"info_key": "led_matrix.react_on_keyup", "value_type": "flag"},
    "LED_MATRIX_LED_FLUSH_LIMIT": {"info_key": "led_matrix.led_flush_limit", "value_type": "int"},
    "LED_MATRIX_LED_PROCESS_LIMIT": {"info_key": "led_matrix.led_process_limit", "value_type": "int", "to_json": false},
    "LED_MATRIX_MAXIMUM_BRIGHTNESS": {"info_key": "led_matrix.max_brightness", "value_type": "int"},
    "LED_MATRIX_SLEEP": {"info_key": "led_matrix.sleep", "value_type": "flag"},
    "LED_MATRIX_SPD_STEP": {"info_key": "led_matrix.speed_steps", "value_type": "int"},
    "LED_MATRIX_SPLIT": {"info_key": "led_matrix.split_count", "value_type": "array.int"},
    "LED_MATRIX_TIMEOUT": {"info_key": "led_matrix.timeout", "value_type": "int"},
    "LED_MATRIX_VAL_STEP": {"info_key": "led_matrix.val_steps", "value_type": "int"},
    "LED_MATRIX_LED_COUNT": {"info_key": "led_matrix.led_count", "value_type": "int", "to_json": false},
    "LED_MATRIX_DEFAULT_ON": {"info_key": "led_matrix.default.on", "value_type": "bool"},
    "LED_MATRIX_DEFAULT_VAL": {"info_key": "led_matrix.default.val", "value_type": "int"},
    "LED_MATRIX_DEFAULT_SPD": {"info_key": "led_matrix.default.speed", "value_type": "int"},

    // Locking Switch
    "LOCKING_SUPPORT_ENABLE": {"info_key": "qmk.locking.enabled", "value_type": "flag"},
    "LOCKING_RESYNC_ENABLE": {"info_key": "qmk.locking.resync", "value_type": "flag"},

    // LUFA Bootloader
    "QMK_ESC_INPUT": {"info_key": "qmk_lufa_bootloader.esc_input"},
    "QMK_ESC_OUTPUT": {"info_key": "qmk_lufa_bootloader.esc_output"},
    "QMK_LED": {"info_key": "qmk_lufa_bootloader.led"},
    "QMK_SPEAKER": {"info_key": "qmk_lufa_bootloader.speaker"},

    // Matrix
    "DEBOUNCE": {"info_key": "debounce", "value_type": "int"},
    "DIODE_DIRECTION": {"info_key": "diode_direction"},
    "MATRIX_HAS_GHOST": {"info_key": "matrix_pins.ghost", "value_type": "flag"},
    "MATRIX_INPUT_PRESSED_STATE": {"info_key": "matrix_pins.input_pressed_state", "value_type": "int"},
    "MATRIX_IO_DELAY": {"info_key": "matrix_pins.io_delay", "value_type": "int"},

    // Mouse Keys
    "MOUSEKEY_DELAY": {"info_key": "mousekey.delay", "value_type": "int"},
    "MOUSEKEY_INTERVAL": {"info_key": "mousekey.interval", "value_type": "int"},
    "MOUSEKEY_MAX_SPEED": {"info_key": "mousekey.max_speed", "value_type": "int"},
    "MOUSEKEY_TIME_TO_MAX": {"info_key": "mousekey.time_to_max", "value_type": "int"},
    "MOUSEKEY_WHEEL_DELAY": {"info_key": "mousekey.wheel_delay", "value_type": "int"},

    // One Shot
    "ONESHOT_TIMEOUT": {"info_key": "oneshot.timeout", "value_type": "int"},
    "ONESHOT_TAP_TOGGLE": {"info_key": "oneshot.tap_toggle", "value_type": "int"},

    // PS/2
    "PS2_CLOCK_PIN": {"info_key": "ps2.clock_pin"},
    "PS2_DATA_PIN": {"info_key": "ps2.data_pin"},

    // RGB Matrix
    "RGB_MATRIX_CENTER": {"info_key": "rgb_matrix.center_point", "value_type": "array.int"},
    "RGB_MATRIX_HUE_STEP": {"info_key": "rgb_matrix.hue_steps", "value_type": "int"},
    "RGB_MATRIX_KEYRELEASES": {"info_key": "rgb_matrix.react_on_keyup", "value_type": "flag"},
    "RGB_MATRIX_LED_FLUSH_LIMIT": {"info_key": "rgb_matrix.led_flush_limit", "value_type": "int"},
    "RGB_MATRIX_LED_PROCESS_LIMIT": {"info_key": "rgb_matrix.led_process_limit", "value_type": "int", "to_json": false},
    "RGB_MATRIX_MAXIMUM_BRIGHTNESS": {"info_key": "rgb_matrix.max_brightness", "value_type": "int"},
    "RGB_MATRIX_SAT_STEP": {"info_key": "rgb_matrix.sat_steps", "value_type": "int"},
    "RGB_MATRIX_SLEEP": {"info_key": "rgb_matrix.sleep", "value_type": "flag"},
    "RGB_MATRIX_SPD_STEP": {"info_key": "rgb_matrix.speed_steps", "value_type": "int"},
    "RGB_MATRIX_SPLIT": {"info_key": "rgb_matrix.split_count", "value_type": "array.int"},
    "RGB_MATRIX_TIMEOUT": {"info_key": "rgb_matrix.timeout", "value_type": "int"},
    "RGB_MATRIX_VAL_STEP": {"info_key": "rgb_matrix.val_steps", "value_type": "int"},
    "RGB_MATRIX_LED_COUNT": {"info_key": "rgb_matrix.led_count", "value_type": "int", "to_json": false},
    "RGB_MATRIX_DEFAULT_ON": {"info_key": "rgb_matrix.default.on", "value_type": "bool"},
    "RGB_MATRIX_DEFAULT_HUE": {"info_key": "rgb_matrix.default.hue", "value_type": "int"},
    "RGB_MATRIX_DEFAULT_SAT": {"info_key": "rgb_matrix.default.sat", "value_type": "int"},
    "RGB_MATRIX_DEFAULT_VAL": {"info_key": "rgb_matrix.default.val", "value_type": "int"},
    "RGB_MATRIX_DEFAULT_SPD": {"info_key": "rgb_matrix.default.speed", "value_type": "int"},

    // RGBLight
    "RGBLED_SPLIT": {"info_key": "rgblight.split_count", "value_type": "array.int"},
    "RGBLIGHT_HUE_STEP": {"info_key": "rgblight.hue_steps", "value_type": "int"},
    "RGBLIGHT_LAYER_BLINK": {"info_key": "rgblight.layers.blink", "value_type": "flag"},
    "RGBLIGHT_LAYERS": {"info_key": "rgblight.layers.enabled", "value_type": "flag"},
    "RGBLIGHT_LAYERS_OVERRIDE_RGB_OFF": {"info_key": "rgblight.layers.override_rgb", "value_type": "flag"},
    "RGBLIGHT_LED_COUNT": {"info_key": "rgblight.led_count", "value_type": "int"},
    "RGBLIGHT_LED_MAP": {"info_key": "rgblight.led_map", "value_type": "array.int"},
    "RGBLIGHT_LIMIT_VAL": {"info_key": "rgblight.max_brightness", "value_type": "int"},
    "RGBLIGHT_MAX_LAYERS": {"info_key": "rgblight.layers.max", "value_type": "int"},
    "RGBLIGHT_SAT_STEP": {"info_key": "rgblight.saturation_steps", "value_type": "int"},
    "RGBLIGHT_SLEEP": {"info_key": "rgblight.sleep", "value_type": "flag"},
    "RGBLIGHT_SPLIT": {"info_key": "rgblight.split", "value_type": "flag"},
    "RGBLIGHT_VAL_STEP": {"info_key": "rgblight.brightness_steps", "value_type": "int"},
    "RGBLIGHT_DEFAULT_ON": {"info_key": "rgblight.default.on", "value_type": "bool"},
    "RGBLIGHT_DEFAULT_HUE": {"info_key": "rgblight.default.hue", "value_type": "int"},
    "RGBLIGHT_DEFAULT_SAT": {"info_key": "rgblight.default.sat", "value_type": "int"},
    "RGBLIGHT_DEFAULT_VAL": {"info_key": "rgblight.default.val", "value_type": "int"},
    "RGBLIGHT_DEFAULT_SPD": {"info_key": "rgblight.default.speed", "value_type": "int"},

    // Secure
    "SECURE_IDLE_TIMEOUT": {"info_key": "secure.idle_timeout", "value_type": "int"},
    "SECURE_UNLOCK_SEQUENCE": {"info_key": "secure.unlock_sequence", "value_type": "array.array.int", "to_json": false},
    "SECURE_UNLOCK_TIMEOUT": {"info_key": "secure.unlock_timeout", "value_type": "int"},

    // Split Keyboard
    "SOFT_SERIAL_PIN": {"info_key": "split.serial.pin"},
    "SOFT_SERIAL_SPEED": {"info_key": "split.soft_serial_speed"},
    "SPLIT_HAND_MATRIX_GRID": {"info_key": "split.handedness.matrix_grid", "value_type": "array", "to_c": false},
    "SPLIT_HAND_PIN": {"info_key": "split.handedness.pin"},
    "SPLIT_USB_DETECT": {"info_key": "split.usb_detect.enabled", "value_type": "flag"},
    "SPLIT_USB_TIMEOUT": {"info_key": "split.usb_detect.timeout", "value_type": "int"},
    "SPLIT_USB_TIMEOUT_POLL": {"info_key": "split.usb_detect.polling_interval", "value_type": "int"},
    "SPLIT_WATCHDOG_ENABLE": {"info_key": "split.transport.watchdog", "value_type": "flag"},
    "SPLIT_WATCHDOG_TIMEOUT": {"info_key": "split.transport.watchdog_timeout", "value_type": "int"},
    "SPLIT_ACTIVITY_ENABLE": {"info_key": "split.transport.sync.activity", "value_type": "flag"},
    "SPLIT_DETECTED_OS_ENABLE": {"info_key": "split.transport.sync.detected_os", "value_type": "flag"},
    "SPLIT_HAPTIC_ENABLE": {"info_key": "split.transport.sync.haptic", "value_type": "flag"},
    "SPLIT_LAYER_STATE_ENABLE": {"info_key": "split.transport.sync.layer_state", "value_type": "flag"},
    "SPLIT_LED_STATE_ENABLE": {"info_key": "split.transport.sync.indicators", "value_type": "flag"},
    "SPLIT_TRANSPORT_MIRROR": {"info_key": "split.transport.sync.matrix_state", "value_type": "flag"},
    "SPLIT_MODS_ENABLE": {"info_key": "split.transport.sync.modifiers", "value_type": "flag"},
    "SPLIT_OLED_ENABLE": {"info_key": "split.transport.sync.oled", "value_type": "flag"},
    "SPLIT_ST7565_ENABLE": {"info_key": "split.transport.sync.st7565", "value_type": "flag"},
    "SPLIT_WPM_ENABLE": {"info_key": "split.transport.sync.wpm", "value_type": "flag"},

    // Tapping
    "CHORDAL_HOLD": {"info_key": "tapping.chordal_hold", "value_type": "flag"},
    "FLOW_TAP_TERM": {"info_key": "tapping.flow_tap_term", "value_type": "int"},
    "HOLD_ON_OTHER_KEY_PRESS": {"info_key": "tapping.hold_on_other_key_press", "value_type": "flag"},
    "HOLD_ON_OTHER_KEY_PRESS_PER_KEY": {"info_key": "tapping.hold_on_other_key_press_per_key", "value_type": "flag"},
    "PERMISSIVE_HOLD": {"info_key": "tapping.permissive_hold", "value_type": "flag"},
    "PERMISSIVE_HOLD_PER_KEY": {"info_key": "tapping.permissive_hold_per_key", "value_type": "flag"},
    "RETRO_TAPPING": {"info_key": "tapping.retro", "value_type": "flag"},
    "RETRO_TAPPING_PER_KEY": {"info_key": "tapping.retro_per_key", "value_type": "flag"},
    "TAP_CODE_DELAY": {"info_key": "qmk.tap_keycode_delay", "value_type": "int"},
    "TAP_HOLD_CAPS_DELAY": {"info_key": "qmk.tap_capslock_delay", "value_type": "int"},
    "TAPPING_TERM": {"info_key": "tapping.term", "value_type": "int"},
    "TAPPING_TERM_PER_KEY": {"info_key": "tapping.term_per_key", "value_type": "flag"},
    "TAPPING_TOGGLE": {"info_key": "tapping.toggle", "value_type": "int"},

    // USB
    "USB_MAX_POWER_CONSUMPTION": {"info_key": "usb.max_power", "value_type": "int"},
    "USB_POLLING_INTERVAL_MS": {"info_key": "usb.polling_interval", "value_type": "int"},
    "USB_SUSPEND_WAKEUP_DELAY": {"info_key": "usb.suspend_wakeup_delay", "value_type": "int"},

    // WS2812
    "WS2812_DI_PIN": {"info_key": "ws2812.pin"},
    "WS2812_I2C_ADDRESS": {"info_key": "ws2812.i2c_address", "value_type": "hex"},
    "WS2812_I2C_TIMEOUT": {"info_key": "ws2812.i2c_timeout", "value_type": "int"},
    "WS2812_RGBW": {"info_key": "ws2812.rgbw", "value_type": "flag"},

    "LAYOUTS": {"info_key": "layout_aliases", "value_type": "mapping"},

    // Items we want flagged in lint
    "DEBOUNCING_DELAY": {"info_key": "_invalid.debouncing_delay", "invalid": true, "replace_with": "DEBOUNCE"},
    "DESCRIPTION": {"info_key": "_invalid.usb_description", "invalid": true},
    "IGNORE_MOD_TAP_INTERRUPT": {"info_key": "_invalid.ignore_mod_tap_interrupt", "value_type": "flag", "invalid": true},
    "IGNORE_MOD_TAP_INTERRUPT_PER_KEY": {"info_key": "_invalid.ignore_mod_tap_interrupt_per_key", "invalid": true},
    "LED_DISABLE_WHEN_USB_SUSPENDED": {"info_key": "_invalid.led_matrix_sleep", "invalid": true, "replace_with": "LED_MATRIX_SLEEP"},
    "NO_ACTION_FUNCTION": {"info_key": "_invalid.no_action_function", "invalid": true},
    "NO_ACTION_MACRO": {"info_key": "_invalid.no_action_macro", "invalid": true},
    "PREVENT_STUCK_MODIFIERS": {"info_key": "_invalid.prevent_stuck_mods", "invalid": true},
    "QMK_KEYS_PER_SCAN": {"info_key": "qmk.keys_per_scan", "value_type": "int", "deprecated": true},
    "RGB_DI_PIN": {"info_key": "rgblight.pin", "invalid": true, "replace_with": "WS2812_DI_PIN or APA102_DI_PIN"},
    "RGBW": {"info_key": "rgblight.rgbw", "invalid": true, "replace_with": "WS2812_RGBW"},
    "RGB_DISABLE_WHEN_USB_SUSPENDED": {"info_key": "_invalid.rgb_matrix_sleep", "invalid": true, "replace_with": "RGB_MATRIX_SLEEP"},
    "RGBLIGHT_ANIMATIONS": {"info_key": "_invalid.rgblight.animations.all", "value_type": "flag", "invalid": true},
    "TAPPING_FORCE_HOLD": {"info_key": "tapping.force_hold", "value_type": "flag", "deprecated": true},
    "TAPPING_FORCE_HOLD_PER_KEY": {"info_key": "tapping.force_hold_per_key", "value_type": "flag", "deprecated": true},
    "UNUSED_PINS": {"info_key": "_invalid.unused_pins", "deprecated": true},
    "COMBO_COUNT": {"info_key": "_invalid.combo.count", "invalid": true},

    // USB params, need to mark as failure when specified in config.h, rather than deprecated
    "DEVICE_VER": {"info_key": "usb.device_version", "value_type": "bcd_version", "deprecated": true, "replace_with": "`usb.device_version` in info.json"},
    "MANUFACTURER": {"info_key": "manufacturer", "value_type": "str", "deprecated": true, "replace_with": "`manufacturer` in info.json"},
    "PRODUCT": {"info_key": "keyboard_name", "warn_duplicate": false, "value_type": "str", "deprecated": true, "replace_with": "`keyboard_name` in info.json"},
    "PRODUCT_ID": {"info_key": "usb.pid", "value_type": "hex", "deprecated": true, "replace_with": "`usb.pid` in info.json"},
    "VENDOR_ID": {"info_key": "usb.vid", "value_type": "hex", "deprecated": true, "replace_with": "`usb.vid` in info.json"},
    "FORCE_NKRO": {"info_key": "usb.force_nkro", "value_type": "flag", "deprecated": true, "replace_with": "`host.default.nkro` in info.json"},

    // Items we want flagged in lint
    "VIAL_KEYBOARD_UID": {"info_key": "_invalid.vial_uid", "invalid": true},
    "VIAL_UNLOCK_COMBO_COLS": {"info_key": "_invalid.vial_unlock_cols", "invalid": true},
    "VIAL_UNLOCK_COMBO_ROWS": {"info_key": "_invalid.vial_unlock_rows", "invalid": true}
}
