{"manufacturer": "a-jazz", "keyboard_name": "akc084", "maintainer": "<PERSON>", "bootloader": "stm32duino", "diode_direction": "ROW2COL", "encoder": {"rotary": [{"pin_a": "A6", "pin_b": "A7"}]}, "features": {"bootmagic": true, "console": true, "encoder": true, "extrakey": true, "mousekey": true, "nkro": true}, "indicators": {"caps_lock": "A9", "scroll_lock": "A10"}, "matrix_pins": {"cols": ["B0", "B1", "B2", "B3", "B4", "B5", "B6", "B7", "B8", "B9", "B10", "B11", "B12", "B13"], "rows": ["A0", "A1", "A2", "A3", "A4", "A5", "A8"]}, "processor": "STM32F103", "usb": {"device_version": "1.0.1", "no_startup_check": true, "pid": "0x0084", "vid": "0x414A", "wait_for_enumeration": false}, "layouts": {"LAYOUT": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [0, 12], "x": 12, "y": 0}, {"matrix": [0, 13], "x": 13, "y": 0}, {"matrix": [3, 13], "x": 14, "y": 0}, {"matrix": [4, 12], "x": 15, "y": 0}, {"matrix": [6, 12], "x": 16, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [1, 4], "x": 4, "y": 1}, {"matrix": [1, 5], "x": 5, "y": 1}, {"matrix": [1, 6], "x": 6, "y": 1}, {"matrix": [1, 7], "x": 7, "y": 1}, {"matrix": [1, 8], "x": 8, "y": 1}, {"matrix": [1, 9], "x": 9, "y": 1}, {"matrix": [1, 10], "x": 10, "y": 1}, {"matrix": [1, 11], "x": 11, "y": 1}, {"matrix": [1, 12], "x": 12, "y": 1}, {"matrix": [1, 13], "x": 13, "y": 1, "w": 2}, {"matrix": [4, 13], "x": 15, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2, "w": 1.5}, {"matrix": [2, 1], "x": 1.5, "y": 2}, {"matrix": [2, 2], "x": 2.5, "y": 2}, {"matrix": [2, 3], "x": 3.5, "y": 2}, {"matrix": [2, 4], "x": 4.5, "y": 2}, {"matrix": [2, 5], "x": 5.5, "y": 2}, {"matrix": [2, 6], "x": 6.5, "y": 2}, {"matrix": [2, 7], "x": 7.5, "y": 2}, {"matrix": [2, 8], "x": 8.5, "y": 2}, {"matrix": [2, 9], "x": 9.5, "y": 2}, {"matrix": [2, 10], "x": 10.5, "y": 2}, {"matrix": [2, 11], "x": 11.5, "y": 2}, {"matrix": [2, 12], "x": 12.5, "y": 2}, {"matrix": [2, 13], "x": 13.5, "y": 2, "w": 1.5}, {"matrix": [5, 11], "x": 15, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3, "w": 1.75}, {"matrix": [3, 1], "x": 1.75, "y": 3}, {"matrix": [3, 2], "x": 2.75, "y": 3}, {"matrix": [3, 3], "x": 3.75, "y": 3}, {"matrix": [3, 4], "x": 4.75, "y": 3}, {"matrix": [3, 5], "x": 5.75, "y": 3}, {"matrix": [3, 6], "x": 6.75, "y": 3}, {"matrix": [3, 7], "x": 7.75, "y": 3}, {"matrix": [3, 8], "x": 8.75, "y": 3}, {"matrix": [3, 9], "x": 9.75, "y": 3}, {"matrix": [3, 10], "x": 10.75, "y": 3}, {"matrix": [3, 11], "x": 11.75, "y": 3}, {"matrix": [3, 12], "x": 12.75, "y": 3, "w": 2.25}, {"matrix": [5, 12], "x": 15, "y": 3}, {"matrix": [4, 0], "x": 0, "y": 4, "w": 2.25}, {"matrix": [4, 1], "x": 2.25, "y": 4}, {"matrix": [4, 2], "x": 3.25, "y": 4}, {"matrix": [4, 3], "x": 4.25, "y": 4}, {"matrix": [4, 4], "x": 5.25, "y": 4}, {"matrix": [4, 5], "x": 6.25, "y": 4}, {"matrix": [4, 6], "x": 7.25, "y": 4}, {"matrix": [4, 7], "x": 8.25, "y": 4}, {"matrix": [4, 8], "x": 9.25, "y": 4}, {"matrix": [4, 9], "x": 10.25, "y": 4}, {"matrix": [4, 10], "x": 11.25, "y": 4}, {"matrix": [4, 11], "x": 12.25, "y": 4, "w": 1.75}, {"matrix": [5, 7], "x": 14, "y": 4}, {"matrix": [5, 13], "x": 15, "y": 4}, {"matrix": [5, 0], "x": 0, "y": 5, "w": 1.25}, {"matrix": [5, 1], "x": 1.25, "y": 5, "w": 1.25}, {"matrix": [5, 2], "x": 2.5, "y": 5, "w": 1.25}, {"matrix": [5, 3], "x": 3.75, "y": 5, "w": 6.25}, {"matrix": [5, 4], "x": 10, "y": 5}, {"matrix": [5, 5], "x": 11, "y": 5}, {"matrix": [5, 6], "x": 12, "y": 5}, {"matrix": [5, 8], "x": 13, "y": 5}, {"matrix": [5, 9], "x": 14, "y": 5}, {"matrix": [5, 10], "x": 15, "y": 5}]}}}