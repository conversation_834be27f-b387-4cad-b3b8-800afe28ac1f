{"keyboard_name": "Titan60", "manufacturer": "Acekeyboard", "url": "https://acekeyboard.co/", "maintainer": "keebne<PERSON>b", "usb": {"vid": "0xACE1", "pid": "0x5449", "device_version": "0.0.1"}, "features": {"backlight": true, "bootmagic": true, "extrakey": true, "mousekey": true, "nkro": false, "rgblight": true}, "qmk": {"locking": {"enabled": true, "resync": true}}, "matrix_pins": {"cols": ["F4", "F7", "F5", "F6", "C7", "C6", "B6", "B5", "B4", "D7", "D6", "D4", "D5", "D3"], "rows": ["B1", "B2", "B3", "F0", "F1"]}, "diode_direction": "COL2ROW", "backlight": {"pin": "B7", "breathing": true}, "rgblight": {"saturation_steps": 8, "brightness_steps": 8, "led_count": 6, "sleep": true, "animations": {"breathing": true, "rainbow_mood": true, "rainbow_swirl": true, "snake": true, "knight": true, "christmas": true, "static_gradient": true, "rgb_test": true, "alternating": true, "twinkle": true}}, "ws2812": {"pin": "D0"}, "processor": "atmega32u4", "bootloader": "atmel-dfu", "layouts": {"LAYOUT_60_ansi": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [0, 12], "x": 12, "y": 0}, {"matrix": [0, 13], "x": 13, "y": 0, "w": 2}, {"matrix": [1, 0], "x": 0, "y": 1, "w": 1.5}, {"matrix": [1, 1], "x": 1.5, "y": 1}, {"matrix": [1, 2], "x": 2.5, "y": 1}, {"matrix": [1, 3], "x": 3.5, "y": 1}, {"matrix": [1, 4], "x": 4.5, "y": 1}, {"matrix": [1, 5], "x": 5.5, "y": 1}, {"matrix": [1, 6], "x": 6.5, "y": 1}, {"matrix": [1, 7], "x": 7.5, "y": 1}, {"matrix": [1, 8], "x": 8.5, "y": 1}, {"matrix": [1, 9], "x": 9.5, "y": 1}, {"matrix": [1, 10], "x": 10.5, "y": 1}, {"matrix": [1, 11], "x": 11.5, "y": 1}, {"matrix": [1, 12], "x": 12.5, "y": 1}, {"matrix": [1, 13], "x": 13.5, "y": 1, "w": 1.5}, {"matrix": [2, 0], "x": 0, "y": 2, "w": 1.75}, {"matrix": [2, 1], "x": 1.75, "y": 2}, {"matrix": [2, 2], "x": 2.75, "y": 2}, {"matrix": [2, 3], "x": 3.75, "y": 2}, {"matrix": [2, 4], "x": 4.75, "y": 2}, {"matrix": [2, 5], "x": 5.75, "y": 2}, {"matrix": [2, 6], "x": 6.75, "y": 2}, {"matrix": [2, 7], "x": 7.75, "y": 2}, {"matrix": [2, 8], "x": 8.75, "y": 2}, {"matrix": [2, 9], "x": 9.75, "y": 2}, {"matrix": [2, 10], "x": 10.75, "y": 2}, {"matrix": [2, 11], "x": 11.75, "y": 2}, {"matrix": [2, 13], "x": 12.75, "y": 2, "w": 2.25}, {"matrix": [3, 0], "x": 0, "y": 3, "w": 2.25}, {"matrix": [3, 2], "x": 2.25, "y": 3}, {"matrix": [3, 3], "x": 3.25, "y": 3}, {"matrix": [3, 4], "x": 4.25, "y": 3}, {"matrix": [3, 5], "x": 5.25, "y": 3}, {"matrix": [3, 6], "x": 6.25, "y": 3}, {"matrix": [3, 7], "x": 7.25, "y": 3}, {"matrix": [3, 8], "x": 8.25, "y": 3}, {"matrix": [3, 9], "x": 9.25, "y": 3}, {"matrix": [3, 10], "x": 10.25, "y": 3}, {"matrix": [3, 11], "x": 11.25, "y": 3}, {"matrix": [3, 12], "x": 12.25, "y": 3, "w": 2.75}, {"matrix": [4, 0], "x": 0, "y": 4, "w": 1.25}, {"matrix": [4, 1], "x": 1.25, "y": 4, "w": 1.25}, {"matrix": [4, 2], "x": 2.5, "y": 4, "w": 1.25}, {"matrix": [4, 6], "x": 3.75, "y": 4, "w": 6.25}, {"matrix": [4, 10], "x": 10, "y": 4, "w": 1.25}, {"matrix": [4, 11], "x": 11.25, "y": 4, "w": 1.25}, {"matrix": [4, 12], "x": 12.5, "y": 4, "w": 1.25}, {"matrix": [4, 13], "x": 13.75, "y": 4, "w": 1.25}]}, "LAYOUT_60_ansi_split": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [0, 12], "x": 12, "y": 0}, {"matrix": [0, 13], "x": 13, "y": 0}, {"matrix": [2, 12], "x": 14, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1, "w": 1.5}, {"matrix": [1, 1], "x": 1.5, "y": 1}, {"matrix": [1, 2], "x": 2.5, "y": 1}, {"matrix": [1, 3], "x": 3.5, "y": 1}, {"matrix": [1, 4], "x": 4.5, "y": 1}, {"matrix": [1, 5], "x": 5.5, "y": 1}, {"matrix": [1, 6], "x": 6.5, "y": 1}, {"matrix": [1, 7], "x": 7.5, "y": 1}, {"matrix": [1, 8], "x": 8.5, "y": 1}, {"matrix": [1, 9], "x": 9.5, "y": 1}, {"matrix": [1, 10], "x": 10.5, "y": 1}, {"matrix": [1, 11], "x": 11.5, "y": 1}, {"matrix": [1, 12], "x": 12.5, "y": 1}, {"matrix": [1, 13], "x": 13.5, "y": 1, "w": 1.5}, {"matrix": [2, 0], "x": 0, "y": 2, "w": 1.75}, {"matrix": [2, 1], "x": 1.75, "y": 2}, {"matrix": [2, 2], "x": 2.75, "y": 2}, {"matrix": [2, 3], "x": 3.75, "y": 2}, {"matrix": [2, 4], "x": 4.75, "y": 2}, {"matrix": [2, 5], "x": 5.75, "y": 2}, {"matrix": [2, 6], "x": 6.75, "y": 2}, {"matrix": [2, 7], "x": 7.75, "y": 2}, {"matrix": [2, 8], "x": 8.75, "y": 2}, {"matrix": [2, 9], "x": 9.75, "y": 2}, {"matrix": [2, 10], "x": 10.75, "y": 2}, {"matrix": [2, 11], "x": 11.75, "y": 2}, {"matrix": [2, 13], "x": 12.75, "y": 2, "w": 2.25}, {"matrix": [3, 0], "x": 0, "y": 3, "w": 1.25}, {"matrix": [3, 1], "x": 1.25, "y": 3}, {"matrix": [3, 2], "x": 2.25, "y": 3}, {"matrix": [3, 3], "x": 3.25, "y": 3}, {"matrix": [3, 4], "x": 4.25, "y": 3}, {"matrix": [3, 5], "x": 5.25, "y": 3}, {"matrix": [3, 6], "x": 6.25, "y": 3}, {"matrix": [3, 7], "x": 7.25, "y": 3}, {"matrix": [3, 8], "x": 8.25, "y": 3}, {"matrix": [3, 9], "x": 9.25, "y": 3}, {"matrix": [3, 10], "x": 10.25, "y": 3}, {"matrix": [3, 11], "x": 11.25, "y": 3}, {"matrix": [3, 12], "x": 12.25, "y": 3, "w": 1.75}, {"matrix": [4, 3], "x": 14, "y": 3}, {"matrix": [4, 0], "x": 0, "y": 4, "w": 1.25}, {"matrix": [4, 1], "x": 1.25, "y": 4, "w": 1.25}, {"matrix": [4, 2], "x": 2.5, "y": 4, "w": 1.25}, {"matrix": [4, 6], "x": 3.75, "y": 4, "w": 6.25}, {"matrix": [4, 10], "x": 10, "y": 4, "w": 1.25}, {"matrix": [4, 11], "x": 11.25, "y": 4, "w": 1.25}, {"matrix": [4, 12], "x": 12.5, "y": 4, "w": 1.25}, {"matrix": [4, 13], "x": 13.75, "y": 4, "w": 1.25}]}, "LAYOUT_60_iso": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [0, 12], "x": 12, "y": 0}, {"matrix": [0, 13], "x": 13, "y": 0, "w": 2}, {"matrix": [1, 0], "x": 0, "y": 1, "w": 1.5}, {"matrix": [1, 1], "x": 1.5, "y": 1}, {"matrix": [1, 2], "x": 2.5, "y": 1}, {"matrix": [1, 3], "x": 3.5, "y": 1}, {"matrix": [1, 4], "x": 4.5, "y": 1}, {"matrix": [1, 5], "x": 5.5, "y": 1}, {"matrix": [1, 6], "x": 6.5, "y": 1}, {"matrix": [1, 7], "x": 7.5, "y": 1}, {"matrix": [1, 8], "x": 8.5, "y": 1}, {"matrix": [1, 9], "x": 9.5, "y": 1}, {"matrix": [1, 10], "x": 10.5, "y": 1}, {"matrix": [1, 11], "x": 11.5, "y": 1}, {"matrix": [1, 12], "x": 12.5, "y": 1}, {"matrix": [2, 13], "x": 13.75, "y": 1, "w": 1.25, "h": 2}, {"matrix": [2, 0], "x": 0, "y": 2, "w": 1.75}, {"matrix": [2, 1], "x": 1.75, "y": 2}, {"matrix": [2, 2], "x": 2.75, "y": 2}, {"matrix": [2, 3], "x": 3.75, "y": 2}, {"matrix": [2, 4], "x": 4.75, "y": 2}, {"matrix": [2, 5], "x": 5.75, "y": 2}, {"matrix": [2, 6], "x": 6.75, "y": 2}, {"matrix": [2, 7], "x": 7.75, "y": 2}, {"matrix": [2, 8], "x": 8.75, "y": 2}, {"matrix": [2, 9], "x": 9.75, "y": 2}, {"matrix": [2, 10], "x": 10.75, "y": 2}, {"matrix": [2, 11], "x": 11.75, "y": 2}, {"matrix": [1, 13], "x": 12.75, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3, "w": 1.25}, {"matrix": [3, 1], "x": 1.25, "y": 3}, {"matrix": [3, 2], "x": 2.25, "y": 3}, {"matrix": [3, 3], "x": 3.25, "y": 3}, {"matrix": [3, 4], "x": 4.25, "y": 3}, {"matrix": [3, 5], "x": 5.25, "y": 3}, {"matrix": [3, 6], "x": 6.25, "y": 3}, {"matrix": [3, 7], "x": 7.25, "y": 3}, {"matrix": [3, 8], "x": 8.25, "y": 3}, {"matrix": [3, 9], "x": 9.25, "y": 3}, {"matrix": [3, 10], "x": 10.25, "y": 3}, {"matrix": [3, 11], "x": 11.25, "y": 3}, {"matrix": [3, 12], "x": 12.25, "y": 3, "w": 2.75}, {"matrix": [4, 0], "x": 0, "y": 4, "w": 1.25}, {"matrix": [4, 1], "x": 1.25, "y": 4, "w": 1.25}, {"matrix": [4, 2], "x": 2.5, "y": 4, "w": 1.25}, {"matrix": [4, 6], "x": 3.75, "y": 4, "w": 6.25}, {"matrix": [4, 10], "x": 10, "y": 4, "w": 1.25}, {"matrix": [4, 11], "x": 11.25, "y": 4, "w": 1.25}, {"matrix": [4, 12], "x": 12.5, "y": 4, "w": 1.25}, {"matrix": [4, 13], "x": 13.75, "y": 4, "w": 1.25}]}, "LAYOUT_60_iso_split": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [0, 12], "x": 12, "y": 0}, {"matrix": [0, 13], "x": 13, "y": 0}, {"matrix": [2, 12], "x": 14, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1, "w": 1.5}, {"matrix": [1, 1], "x": 1.5, "y": 1}, {"matrix": [1, 2], "x": 2.5, "y": 1}, {"matrix": [1, 3], "x": 3.5, "y": 1}, {"matrix": [1, 4], "x": 4.5, "y": 1}, {"matrix": [1, 5], "x": 5.5, "y": 1}, {"matrix": [1, 6], "x": 6.5, "y": 1}, {"matrix": [1, 7], "x": 7.5, "y": 1}, {"matrix": [1, 8], "x": 8.5, "y": 1}, {"matrix": [1, 9], "x": 9.5, "y": 1}, {"matrix": [1, 10], "x": 10.5, "y": 1}, {"matrix": [1, 11], "x": 11.5, "y": 1}, {"matrix": [1, 12], "x": 12.5, "y": 1}, {"matrix": [2, 13], "x": 13.75, "y": 1, "w": 1.25, "h": 2}, {"matrix": [2, 0], "x": 0, "y": 2, "w": 1.75}, {"matrix": [2, 1], "x": 1.75, "y": 2}, {"matrix": [2, 2], "x": 2.75, "y": 2}, {"matrix": [2, 3], "x": 3.75, "y": 2}, {"matrix": [2, 4], "x": 4.75, "y": 2}, {"matrix": [2, 5], "x": 5.75, "y": 2}, {"matrix": [2, 6], "x": 6.75, "y": 2}, {"matrix": [2, 7], "x": 7.75, "y": 2}, {"matrix": [2, 8], "x": 8.75, "y": 2}, {"matrix": [2, 9], "x": 9.75, "y": 2}, {"matrix": [2, 10], "x": 10.75, "y": 2}, {"matrix": [2, 11], "x": 11.75, "y": 2}, {"matrix": [1, 13], "x": 12.75, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3, "w": 1.25}, {"matrix": [3, 1], "x": 1.25, "y": 3}, {"matrix": [3, 2], "x": 2.25, "y": 3}, {"matrix": [3, 3], "x": 3.25, "y": 3}, {"matrix": [3, 4], "x": 4.25, "y": 3}, {"matrix": [3, 5], "x": 5.25, "y": 3}, {"matrix": [3, 6], "x": 6.25, "y": 3}, {"matrix": [3, 7], "x": 7.25, "y": 3}, {"matrix": [3, 8], "x": 8.25, "y": 3}, {"matrix": [3, 9], "x": 9.25, "y": 3}, {"matrix": [3, 10], "x": 10.25, "y": 3}, {"matrix": [3, 11], "x": 11.25, "y": 3}, {"matrix": [3, 12], "x": 12.25, "y": 3, "w": 1.75}, {"matrix": [4, 3], "x": 14, "y": 3}, {"matrix": [4, 0], "x": 0, "y": 4, "w": 1.25}, {"matrix": [4, 1], "x": 1.25, "y": 4, "w": 1.25}, {"matrix": [4, 2], "x": 2.5, "y": 4, "w": 1.25}, {"matrix": [4, 6], "x": 3.75, "y": 4, "w": 6.25}, {"matrix": [4, 10], "x": 10, "y": 4, "w": 1.25}, {"matrix": [4, 11], "x": 11.25, "y": 4, "w": 1.25}, {"matrix": [4, 12], "x": 12.5, "y": 4, "w": 1.25}, {"matrix": [4, 13], "x": 13.75, "y": 4, "w": 1.25}]}, "LAYOUT_60_tsangan": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [0, 12], "x": 12, "y": 0}, {"matrix": [0, 13], "x": 13, "y": 0}, {"matrix": [2, 12], "x": 14, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1, "w": 1.5}, {"matrix": [1, 1], "x": 1.5, "y": 1}, {"matrix": [1, 2], "x": 2.5, "y": 1}, {"matrix": [1, 3], "x": 3.5, "y": 1}, {"matrix": [1, 4], "x": 4.5, "y": 1}, {"matrix": [1, 5], "x": 5.5, "y": 1}, {"matrix": [1, 6], "x": 6.5, "y": 1}, {"matrix": [1, 7], "x": 7.5, "y": 1}, {"matrix": [1, 8], "x": 8.5, "y": 1}, {"matrix": [1, 9], "x": 9.5, "y": 1}, {"matrix": [1, 10], "x": 10.5, "y": 1}, {"matrix": [1, 11], "x": 11.5, "y": 1}, {"matrix": [1, 12], "x": 12.5, "y": 1}, {"matrix": [1, 13], "x": 13.5, "y": 1, "w": 1.5}, {"matrix": [2, 0], "x": 0, "y": 2, "w": 1.75}, {"matrix": [2, 1], "x": 1.75, "y": 2}, {"matrix": [2, 2], "x": 2.75, "y": 2}, {"matrix": [2, 3], "x": 3.75, "y": 2}, {"matrix": [2, 4], "x": 4.75, "y": 2}, {"matrix": [2, 5], "x": 5.75, "y": 2}, {"matrix": [2, 6], "x": 6.75, "y": 2}, {"matrix": [2, 7], "x": 7.75, "y": 2}, {"matrix": [2, 8], "x": 8.75, "y": 2}, {"matrix": [2, 9], "x": 9.75, "y": 2}, {"matrix": [2, 10], "x": 10.75, "y": 2}, {"matrix": [2, 11], "x": 11.75, "y": 2}, {"matrix": [2, 13], "x": 12.75, "y": 2, "w": 2.25}, {"matrix": [3, 0], "x": 0, "y": 3, "w": 2.25}, {"matrix": [3, 2], "x": 2.25, "y": 3}, {"matrix": [3, 3], "x": 3.25, "y": 3}, {"matrix": [3, 4], "x": 4.25, "y": 3}, {"matrix": [3, 5], "x": 5.25, "y": 3}, {"matrix": [3, 6], "x": 6.25, "y": 3}, {"matrix": [3, 7], "x": 7.25, "y": 3}, {"matrix": [3, 8], "x": 8.25, "y": 3}, {"matrix": [3, 9], "x": 9.25, "y": 3}, {"matrix": [3, 10], "x": 10.25, "y": 3}, {"matrix": [3, 11], "x": 11.25, "y": 3}, {"matrix": [3, 12], "x": 12.25, "y": 3, "w": 1.75}, {"matrix": [4, 3], "x": 14, "y": 3}, {"matrix": [4, 0], "x": 0, "y": 4, "w": 1.5}, {"matrix": [4, 1], "x": 1.5, "y": 4}, {"matrix": [4, 2], "x": 2.5, "y": 4, "w": 1.5}, {"matrix": [4, 6], "x": 4, "y": 4, "w": 7}, {"matrix": [4, 11], "x": 11, "y": 4, "w": 1.5}, {"matrix": [4, 12], "x": 12.5, "y": 4}, {"matrix": [4, 13], "x": 13.5, "y": 4, "w": 1.5}]}, "LAYOUT_60_tsangan_split": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [0, 12], "x": 12, "y": 0}, {"matrix": [0, 13], "x": 13, "y": 0}, {"matrix": [2, 12], "x": 14, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1, "w": 1.5}, {"matrix": [1, 1], "x": 1.5, "y": 1}, {"matrix": [1, 2], "x": 2.5, "y": 1}, {"matrix": [1, 3], "x": 3.5, "y": 1}, {"matrix": [1, 4], "x": 4.5, "y": 1}, {"matrix": [1, 5], "x": 5.5, "y": 1}, {"matrix": [1, 6], "x": 6.5, "y": 1}, {"matrix": [1, 7], "x": 7.5, "y": 1}, {"matrix": [1, 8], "x": 8.5, "y": 1}, {"matrix": [1, 9], "x": 9.5, "y": 1}, {"matrix": [1, 10], "x": 10.5, "y": 1}, {"matrix": [1, 11], "x": 11.5, "y": 1}, {"matrix": [1, 12], "x": 12.5, "y": 1}, {"matrix": [1, 13], "x": 13.5, "y": 1, "w": 1.5}, {"matrix": [2, 0], "x": 0, "y": 2, "w": 1.75}, {"matrix": [2, 1], "x": 1.75, "y": 2}, {"matrix": [2, 2], "x": 2.75, "y": 2}, {"matrix": [2, 3], "x": 3.75, "y": 2}, {"matrix": [2, 4], "x": 4.75, "y": 2}, {"matrix": [2, 5], "x": 5.75, "y": 2}, {"matrix": [2, 6], "x": 6.75, "y": 2}, {"matrix": [2, 7], "x": 7.75, "y": 2}, {"matrix": [2, 8], "x": 8.75, "y": 2}, {"matrix": [2, 9], "x": 9.75, "y": 2}, {"matrix": [2, 10], "x": 10.75, "y": 2}, {"matrix": [2, 11], "x": 11.75, "y": 2}, {"matrix": [2, 13], "x": 12.75, "y": 2, "w": 2.25}, {"matrix": [3, 0], "x": 0, "y": 3, "w": 1.25}, {"matrix": [3, 1], "x": 1.25, "y": 3}, {"matrix": [3, 2], "x": 2.25, "y": 3}, {"matrix": [3, 3], "x": 3.25, "y": 3}, {"matrix": [3, 4], "x": 4.25, "y": 3}, {"matrix": [3, 5], "x": 5.25, "y": 3}, {"matrix": [3, 6], "x": 6.25, "y": 3}, {"matrix": [3, 7], "x": 7.25, "y": 3}, {"matrix": [3, 8], "x": 8.25, "y": 3}, {"matrix": [3, 9], "x": 9.25, "y": 3}, {"matrix": [3, 10], "x": 10.25, "y": 3}, {"matrix": [3, 11], "x": 11.25, "y": 3}, {"matrix": [3, 12], "x": 12.25, "y": 3, "w": 1.75}, {"matrix": [4, 3], "x": 14, "y": 3}, {"matrix": [4, 0], "x": 0, "y": 4, "w": 1.5}, {"matrix": [4, 1], "x": 1.5, "y": 4}, {"matrix": [4, 2], "x": 2.5, "y": 4, "w": 1.5}, {"matrix": [4, 6], "x": 4, "y": 4, "w": 7}, {"matrix": [4, 11], "x": 11, "y": 4, "w": 1.5}, {"matrix": [4, 12], "x": 12.5, "y": 4}, {"matrix": [4, 13], "x": 13.5, "y": 4, "w": 1.5}]}, "LAYOUT_60_utilitarian": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [0, 12], "x": 12, "y": 0}, {"matrix": [0, 13], "x": 13, "y": 0, "w": 2}, {"matrix": [1, 0], "x": 0, "y": 1, "w": 1.5}, {"matrix": [1, 1], "x": 1.5, "y": 1}, {"matrix": [1, 2], "x": 2.5, "y": 1}, {"matrix": [1, 3], "x": 3.5, "y": 1}, {"matrix": [1, 4], "x": 4.5, "y": 1}, {"matrix": [1, 5], "x": 5.5, "y": 1}, {"matrix": [1, 6], "x": 6.5, "y": 1}, {"matrix": [1, 7], "x": 7.5, "y": 1}, {"matrix": [1, 8], "x": 8.5, "y": 1}, {"matrix": [1, 9], "x": 9.5, "y": 1}, {"matrix": [1, 10], "x": 10.5, "y": 1}, {"matrix": [1, 11], "x": 11.5, "y": 1}, {"matrix": [1, 12], "x": 12.5, "y": 1}, {"matrix": [1, 13], "x": 13.5, "y": 1, "w": 1.5}, {"matrix": [2, 0], "x": 0, "y": 2, "w": 1.75}, {"matrix": [2, 1], "x": 1.75, "y": 2}, {"matrix": [2, 2], "x": 2.75, "y": 2}, {"matrix": [2, 3], "x": 3.75, "y": 2}, {"matrix": [2, 4], "x": 4.75, "y": 2}, {"matrix": [2, 5], "x": 5.75, "y": 2}, {"matrix": [2, 6], "x": 6.75, "y": 2}, {"matrix": [2, 7], "x": 7.75, "y": 2}, {"matrix": [2, 8], "x": 8.75, "y": 2}, {"matrix": [2, 9], "x": 9.75, "y": 2}, {"matrix": [2, 10], "x": 10.75, "y": 2}, {"matrix": [2, 11], "x": 11.75, "y": 2}, {"matrix": [2, 13], "x": 12.75, "y": 2, "w": 2.25}, {"matrix": [3, 0], "x": 0, "y": 3, "w": 2}, {"matrix": [3, 2], "x": 2, "y": 3}, {"matrix": [3, 3], "x": 3, "y": 3}, {"matrix": [3, 4], "x": 4, "y": 3}, {"matrix": [3, 5], "x": 5, "y": 3}, {"matrix": [3, 6], "x": 6, "y": 3}, {"matrix": [3, 7], "x": 7, "y": 3}, {"matrix": [3, 8], "x": 8, "y": 3}, {"matrix": [3, 9], "x": 9, "y": 3}, {"matrix": [3, 10], "x": 10, "y": 3}, {"matrix": [3, 11], "x": 11, "y": 3}, {"matrix": [3, 13], "x": 12, "y": 3}, {"matrix": [3, 12], "x": 13, "y": 3}, {"matrix": [4, 3], "x": 14, "y": 3}, {"matrix": [4, 0], "x": 0, "y": 4, "w": 1.25}, {"matrix": [4, 1], "x": 1.25, "y": 4, "w": 1.25}, {"matrix": [4, 2], "x": 2.5, "y": 4, "w": 1.25}, {"matrix": [4, 6], "x": 3.75, "y": 4, "w": 6.25}, {"matrix": [4, 10], "x": 10, "y": 4}, {"matrix": [4, 11], "x": 11, "y": 4}, {"matrix": [4, 9], "x": 12, "y": 4}, {"matrix": [4, 12], "x": 13, "y": 4}, {"matrix": [4, 13], "x": 14, "y": 4}]}, "LAYOUT_60_utilitarian_split": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [0, 12], "x": 12, "y": 0}, {"matrix": [0, 13], "x": 13, "y": 0}, {"matrix": [2, 12], "x": 14, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1, "w": 1.5}, {"matrix": [1, 1], "x": 1.5, "y": 1}, {"matrix": [1, 2], "x": 2.5, "y": 1}, {"matrix": [1, 3], "x": 3.5, "y": 1}, {"matrix": [1, 4], "x": 4.5, "y": 1}, {"matrix": [1, 5], "x": 5.5, "y": 1}, {"matrix": [1, 6], "x": 6.5, "y": 1}, {"matrix": [1, 7], "x": 7.5, "y": 1}, {"matrix": [1, 8], "x": 8.5, "y": 1}, {"matrix": [1, 9], "x": 9.5, "y": 1}, {"matrix": [1, 10], "x": 10.5, "y": 1}, {"matrix": [1, 11], "x": 11.5, "y": 1}, {"matrix": [1, 12], "x": 12.5, "y": 1}, {"matrix": [1, 13], "x": 13.5, "y": 1, "w": 1.5}, {"matrix": [2, 0], "x": 0, "y": 2, "w": 1.75}, {"matrix": [2, 1], "x": 1.75, "y": 2}, {"matrix": [2, 2], "x": 2.75, "y": 2}, {"matrix": [2, 3], "x": 3.75, "y": 2}, {"matrix": [2, 4], "x": 4.75, "y": 2}, {"matrix": [2, 5], "x": 5.75, "y": 2}, {"matrix": [2, 6], "x": 6.75, "y": 2}, {"matrix": [2, 7], "x": 7.75, "y": 2}, {"matrix": [2, 8], "x": 8.75, "y": 2}, {"matrix": [2, 9], "x": 9.75, "y": 2}, {"matrix": [2, 10], "x": 10.75, "y": 2}, {"matrix": [2, 11], "x": 11.75, "y": 2}, {"matrix": [2, 13], "x": 12.75, "y": 2, "w": 2.25}, {"matrix": [3, 0], "x": 0, "y": 3, "w": 2}, {"matrix": [3, 2], "x": 2, "y": 3}, {"matrix": [3, 3], "x": 3, "y": 3}, {"matrix": [3, 4], "x": 4, "y": 3}, {"matrix": [3, 5], "x": 5, "y": 3}, {"matrix": [3, 6], "x": 6, "y": 3}, {"matrix": [3, 7], "x": 7, "y": 3}, {"matrix": [3, 8], "x": 8, "y": 3}, {"matrix": [3, 9], "x": 9, "y": 3}, {"matrix": [3, 10], "x": 10, "y": 3}, {"matrix": [3, 11], "x": 11, "y": 3}, {"matrix": [3, 13], "x": 12, "y": 3}, {"matrix": [3, 12], "x": 13, "y": 3}, {"matrix": [4, 3], "x": 14, "y": 3}, {"matrix": [4, 0], "x": 0, "y": 4, "w": 1.25}, {"matrix": [4, 1], "x": 1.25, "y": 4, "w": 1.25}, {"matrix": [4, 2], "x": 2.5, "y": 4, "w": 1.25}, {"matrix": [4, 4], "x": 3.75, "y": 4, "w": 2.75}, {"matrix": [4, 6], "x": 6.5, "y": 4, "w": 1.25}, {"matrix": [4, 8], "x": 7.75, "y": 4, "w": 2.25}, {"matrix": [4, 10], "x": 10, "y": 4}, {"matrix": [4, 11], "x": 11, "y": 4}, {"matrix": [4, 9], "x": 12, "y": 4}, {"matrix": [4, 12], "x": 13, "y": 4}, {"matrix": [4, 13], "x": 14, "y": 4}]}, "LAYOUT_60_utilitarian_iso_split": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [0, 12], "x": 12, "y": 0}, {"matrix": [0, 13], "x": 13, "y": 0}, {"matrix": [2, 12], "x": 14, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1, "w": 1.5}, {"matrix": [1, 1], "x": 1.5, "y": 1}, {"matrix": [1, 2], "x": 2.5, "y": 1}, {"matrix": [1, 3], "x": 3.5, "y": 1}, {"matrix": [1, 4], "x": 4.5, "y": 1}, {"matrix": [1, 5], "x": 5.5, "y": 1}, {"matrix": [1, 6], "x": 6.5, "y": 1}, {"matrix": [1, 7], "x": 7.5, "y": 1}, {"matrix": [1, 8], "x": 8.5, "y": 1}, {"matrix": [1, 9], "x": 9.5, "y": 1}, {"matrix": [1, 10], "x": 10.5, "y": 1}, {"matrix": [1, 11], "x": 11.5, "y": 1}, {"matrix": [1, 12], "x": 12.5, "y": 1}, {"matrix": [2, 13], "x": 13.75, "y": 1, "w": 1.25, "h": 2}, {"matrix": [2, 0], "x": 0, "y": 2, "w": 1.75}, {"matrix": [2, 1], "x": 1.75, "y": 2}, {"matrix": [2, 2], "x": 2.75, "y": 2}, {"matrix": [2, 3], "x": 3.75, "y": 2}, {"matrix": [2, 4], "x": 4.75, "y": 2}, {"matrix": [2, 5], "x": 5.75, "y": 2}, {"matrix": [2, 6], "x": 6.75, "y": 2}, {"matrix": [2, 7], "x": 7.75, "y": 2}, {"matrix": [2, 8], "x": 8.75, "y": 2}, {"matrix": [2, 9], "x": 9.75, "y": 2}, {"matrix": [2, 10], "x": 10.75, "y": 2}, {"matrix": [2, 11], "x": 11.75, "y": 2}, {"matrix": [1, 13], "x": 12.75, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3, "w": 2}, {"matrix": [3, 2], "x": 2, "y": 3}, {"matrix": [3, 3], "x": 3, "y": 3}, {"matrix": [3, 4], "x": 4, "y": 3}, {"matrix": [3, 5], "x": 5, "y": 3}, {"matrix": [3, 6], "x": 6, "y": 3}, {"matrix": [3, 7], "x": 7, "y": 3}, {"matrix": [3, 8], "x": 8, "y": 3}, {"matrix": [3, 9], "x": 9, "y": 3}, {"matrix": [3, 10], "x": 10, "y": 3}, {"matrix": [3, 11], "x": 11, "y": 3}, {"matrix": [3, 13], "x": 12, "y": 3}, {"matrix": [3, 12], "x": 13, "y": 3}, {"matrix": [4, 3], "x": 14, "y": 3}, {"matrix": [4, 0], "x": 0, "y": 4, "w": 1.25}, {"matrix": [4, 1], "x": 1.25, "y": 4, "w": 1.25}, {"matrix": [4, 2], "x": 2.5, "y": 4, "w": 1.25}, {"matrix": [4, 4], "x": 3.75, "y": 4, "w": 2.75}, {"matrix": [4, 6], "x": 6.5, "y": 4, "w": 1.25}, {"matrix": [4, 8], "x": 7.75, "y": 4, "w": 2.25}, {"matrix": [4, 10], "x": 10, "y": 4}, {"matrix": [4, 11], "x": 11, "y": 4}, {"matrix": [4, 9], "x": 12, "y": 4}, {"matrix": [4, 12], "x": 13, "y": 4}, {"matrix": [4, 13], "x": 14, "y": 4}]}}}