# titan60

![Front of the Titan60 PCB](https://i.imgur.com/LaPzdRl.png)
![Back of the Titan60 PCB](https://i.imgur.com/nbineBj.png)

60% custom keyboard originally meant for the Saturn 60

* Keyboard Maintainer: [keebnewb](https://github.com/thompson-ele)
* Hardware Supported: Titan 60 PCB
* Hardware Availability: [Acekeyboard](https://acekeyboard.co/)

Make example for this keyboard (after setting up your build environment):

    make acekeyboard/titan60:default

Flashing example for this keyboard:

    make acekeyboard/titan60:default:flash

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
