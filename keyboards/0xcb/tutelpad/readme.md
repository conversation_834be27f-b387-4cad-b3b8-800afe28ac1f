# TutelPad macropad

* Keyboard Maintainer: [ItsFiremanSam](https://github.com/ItsFiremanSam)
* Hardware Supported: [TutelPad GitHub](https://github.com/0xCB-dev/0xCB-TutelPad)
* Hardware Availability: [KeebSupply](https://keeb.supply/)

## Bootloader

You can enter the bootloader by pressing the reset switch on the side while the keyboard is plugged in. You can also short the GND and RST pads on the controller.

Make example for this keyboard (after setting up your build environment):

    make 0xcb/tutelpad:default

Flashing example for this keyboard:

    make 0xcb/tutelpad:default:flash

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
