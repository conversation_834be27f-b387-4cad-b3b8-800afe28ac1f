{"keyboard_name": "Static", "manufacturer": "0xCB", "url": "https://0xCB.dev", "maintainer": "<PERSON><PERSON><PERSON>", "usb": {"vid": "0xCB00", "pid": "0xA455", "device_version": "0.0.1"}, "build": {"lto": true}, "features": {"bootmagic": true, "encoder": true, "extrakey": true, "mousekey": true, "nkro": false, "oled": true}, "matrix_pins": {"cols": ["B5", "D4", "C0", "C1", "C2", "C3"], "rows": ["D5", "D6", "D7", "B0", "B1", "B2", "B3", "B4"]}, "diode_direction": "COL2ROW", "encoder": {"rotary": [{"pin_a": "D0", "pin_b": "D1"}]}, "qmk": {"locking": {"enabled": true, "resync": true}, "tap_keycode_delay": 10}, "processor": "atmega328p", "bootloader": "usbasploader", "layout_aliases": {"LAYOUT": "LAYOUT_all"}, "layouts": {"LAYOUT_all": {"layout": [{"matrix": [1, 5], "x": 11, "y": 0}, {"matrix": [0, 0], "x": 0, "y": 1}, {"matrix": [1, 0], "x": 1, "y": 1}, {"matrix": [0, 1], "x": 2, "y": 1}, {"matrix": [1, 1], "x": 3, "y": 1}, {"matrix": [0, 2], "x": 4, "y": 1}, {"matrix": [1, 2], "x": 5, "y": 1}, {"matrix": [0, 3], "x": 6, "y": 1}, {"matrix": [1, 3], "x": 7, "y": 1}, {"matrix": [0, 4], "x": 8, "y": 1}, {"matrix": [1, 4], "x": 9, "y": 1}, {"matrix": [0, 5], "x": 10, "y": 1}, {"matrix": [3, 5], "x": 11, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2, "w": 1.25}, {"matrix": [3, 0], "x": 1.25, "y": 2}, {"matrix": [2, 1], "x": 2.25, "y": 2}, {"matrix": [3, 1], "x": 3.25, "y": 2}, {"matrix": [2, 2], "x": 4.25, "y": 2}, {"matrix": [3, 2], "x": 5.25, "y": 2}, {"matrix": [2, 3], "x": 6.25, "y": 2}, {"matrix": [3, 3], "x": 7.25, "y": 2}, {"matrix": [2, 4], "x": 8.25, "y": 2}, {"matrix": [3, 4], "x": 9.25, "y": 2}, {"matrix": [2, 5], "x": 10.25, "y": 2, "w": 1.75}, {"matrix": [4, 0], "x": 0, "y": 3, "w": 1.75}, {"matrix": [4, 1], "x": 1.75, "y": 3}, {"matrix": [5, 1], "x": 2.75, "y": 3}, {"matrix": [4, 2], "x": 3.75, "y": 3}, {"matrix": [5, 2], "x": 4.75, "y": 3}, {"matrix": [4, 3], "x": 5.75, "y": 3}, {"matrix": [5, 3], "x": 6.75, "y": 3}, {"matrix": [4, 4], "x": 7.75, "y": 3}, {"matrix": [5, 4], "x": 8.75, "y": 3}, {"matrix": [4, 5], "x": 9.75, "y": 3}, {"matrix": [5, 5], "x": 10.75, "y": 3, "w": 1.25}, {"matrix": [6, 0], "x": 0, "y": 4}, {"matrix": [7, 0], "x": 1, "y": 4}, {"matrix": [6, 1], "x": 2, "y": 4}, {"matrix": [7, 1], "x": 3, "y": 4, "w": 2.75}, {"matrix": [7, 2], "x": 5.75, "y": 4}, {"matrix": [6, 4], "x": 6.75, "y": 4, "w": 2.25}, {"matrix": [7, 4], "x": 9, "y": 4}, {"matrix": [6, 5], "x": 10, "y": 4}, {"matrix": [7, 5], "x": 11, "y": 4}]}, "LAYOUT_bigbar": {"layout": [{"matrix": [1, 5], "x": 11, "y": 0}, {"matrix": [0, 0], "x": 0, "y": 1}, {"matrix": [1, 0], "x": 1, "y": 1}, {"matrix": [0, 1], "x": 2, "y": 1}, {"matrix": [1, 1], "x": 3, "y": 1}, {"matrix": [0, 2], "x": 4, "y": 1}, {"matrix": [1, 2], "x": 5, "y": 1}, {"matrix": [0, 3], "x": 6, "y": 1}, {"matrix": [1, 3], "x": 7, "y": 1}, {"matrix": [0, 4], "x": 8, "y": 1}, {"matrix": [1, 4], "x": 9, "y": 1}, {"matrix": [0, 5], "x": 10, "y": 1}, {"matrix": [3, 5], "x": 11, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2, "w": 1.25}, {"matrix": [3, 0], "x": 1.25, "y": 2}, {"matrix": [2, 1], "x": 2.25, "y": 2}, {"matrix": [3, 1], "x": 3.25, "y": 2}, {"matrix": [2, 2], "x": 4.25, "y": 2}, {"matrix": [3, 2], "x": 5.25, "y": 2}, {"matrix": [2, 3], "x": 6.25, "y": 2}, {"matrix": [3, 3], "x": 7.25, "y": 2}, {"matrix": [2, 4], "x": 8.25, "y": 2}, {"matrix": [3, 4], "x": 9.25, "y": 2}, {"matrix": [2, 5], "x": 10.25, "y": 2, "w": 1.75}, {"matrix": [4, 0], "x": 0, "y": 3, "w": 1.75}, {"matrix": [4, 1], "x": 1.75, "y": 3}, {"matrix": [5, 1], "x": 2.75, "y": 3}, {"matrix": [4, 2], "x": 3.75, "y": 3}, {"matrix": [5, 2], "x": 4.75, "y": 3}, {"matrix": [4, 3], "x": 5.75, "y": 3}, {"matrix": [5, 3], "x": 6.75, "y": 3}, {"matrix": [4, 4], "x": 7.75, "y": 3}, {"matrix": [5, 4], "x": 8.75, "y": 3}, {"matrix": [4, 5], "x": 9.75, "y": 3}, {"matrix": [5, 5], "x": 10.75, "y": 3, "w": 1.25}, {"matrix": [6, 0], "x": 0, "y": 4, "w": 1.25}, {"matrix": [7, 0], "x": 1.25, "y": 4}, {"matrix": [6, 1], "x": 2.25, "y": 4}, {"matrix": [7, 2], "x": 3.25, "y": 4, "w": 6.25}, {"matrix": [6, 5], "x": 9.5, "y": 4, "w": 1.25}, {"matrix": [7, 5], "x": 10.75, "y": 4, "w": 1.25}]}}}