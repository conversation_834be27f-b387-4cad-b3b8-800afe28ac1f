# 0xCB 1337

Macro keypad

* Keyboard Maintainer: [<PERSON>](https://github.com/conor-burns)
* Hardware Supported: https://github.com/0xcb-dev/0xcb-1337
* Hardware Availability: On [tindie](https://www.tindie.com/products/0xcb/0xcb-1337-a-customizable-macro-keyboard-with-qmk/) or order your own parts - the hardware in the repo is Open Source :D
* PCB renders :)

![](https://github.com/0xCB-dev/0xcb-1337/blob/main/PCB/rev3.0/top.png)

![](https://github.com/0xCB-dev/0xcb-1337/blob/main/PCB/rev3.0/bottom.png)

More Pictures [here](https://0xcb.dev/1337/)
To go to bootloader press row 0 col 0 key (top left) while plugging in the board. (Or press the reset button on V2.0 and v3.0)

Make example for this keyboard (after setting up your build environment):

    make 0xcb/1337:default

Flashing example for this keyboard:

    make 0xcb/1337:default:flash

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
