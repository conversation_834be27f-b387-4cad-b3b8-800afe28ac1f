{"manufacturer": "<PERSON><PERSON>", "keyboard_name": "splaytoraid", "maintainer": "freya-irl", "url": "https://github.com/freya-irl/splaytoraid40", "usb": {"device_version": "1.0.0", "pid": "0xCB00", "vid": "0x2004"}, "features": {"extrakey": true, "rgb_matrix": true, "bootmagic": true, "console": true, "mousekey": true, "nkro": true, "encoder": true}, "bootmagic": {"matrix": [1, 0]}, "build": {"lto": true}, "rgb_matrix": {"default": {"animation": "breathing", "hue": 152, "sat": 232, "speed": 50}, "driver": "ws2812", "layout": [{"flags": 4, "matrix": [0, 2], "x": 0, "y": 0}, {"flags": 4, "matrix": [1, 0], "x": 20, "y": 0}, {"flags": 4, "matrix": [7, 0], "x": 61, "y": 0}, {"flags": 4, "matrix": [7, 1], "x": 163, "y": 0}, {"flags": 4, "matrix": [5, 0], "x": 203, "y": 0}, {"flags": 4, "matrix": [4, 2], "x": 224, "y": 0}, {"flags": 4, "matrix": [6, 2], "x": 0, "y": 21}, {"flags": 4, "matrix": [6, 1], "x": 224, "y": 21}, {"flags": 4, "matrix": [3, 3], "x": 20, "y": 43}, {"flags": 4, "matrix": [7, 3], "x": 61, "y": 43}, {"flags": 4, "matrix": [6, 4], "x": 163, "y": 43}, {"flags": 4, "matrix": [6, 3], "x": 203, "y": 43}, {"flags": 4, "matrix": [4, 3], "x": 61, "y": 64}, {"flags": 4, "matrix": [5, 5], "x": 81, "y": 64}, {"flags": 4, "matrix": [7, 4], "x": 101, "y": 64}, {"flags": 4, "matrix": [7, 5], "x": 122, "y": 64}, {"flags": 4, "matrix": [1, 5], "x": 142, "y": 64}, {"flags": 4, "matrix": [0, 3], "x": 163, "y": 64}], "max_brightness": 200}, "layouts": {"LAYOUT_36": {"layout": [{"label": "K10", "matrix": [1, 0], "x": 0, "y": 0}, {"label": "K11", "matrix": [1, 1], "x": 1, "y": 0}, {"label": "K02", "matrix": [0, 2], "x": 2, "y": 0}, {"label": "K01", "matrix": [0, 1], "x": 3, "y": 0}, {"label": "K12", "matrix": [1, 2], "x": 4, "y": 0}, {"label": "K52", "matrix": [5, 2], "x": 6, "y": 0}, {"label": "K04", "matrix": [0, 4], "x": 7, "y": 0}, {"label": "K03", "matrix": [0, 3], "x": 8, "y": 0}, {"label": "K14", "matrix": [1, 4], "x": 9, "y": 0}, {"label": "K15", "matrix": [1, 5], "x": 10, "y": 0}, {"label": "K30", "matrix": [3, 0], "x": 0, "y": 1}, {"label": "K31", "matrix": [3, 1], "x": 1, "y": 1}, {"label": "K22", "matrix": [2, 2], "x": 2, "y": 1}, {"label": "K21", "matrix": [2, 1], "x": 3, "y": 1}, {"label": "K13", "matrix": [1, 3], "x": 4, "y": 1}, {"label": "K53", "matrix": [5, 3], "x": 6, "y": 1}, {"label": "K24", "matrix": [2, 4], "x": 7, "y": 1}, {"label": "K23", "matrix": [2, 3], "x": 8, "y": 1}, {"label": "K34", "matrix": [3, 4], "x": 9, "y": 1}, {"label": "K35", "matrix": [3, 5], "x": 10, "y": 1}, {"label": "K50", "matrix": [5, 0], "x": 0, "y": 2}, {"label": "K51", "matrix": [5, 1], "x": 1, "y": 2}, {"label": "K42", "matrix": [4, 2], "x": 2, "y": 2}, {"label": "K41", "matrix": [4, 1], "x": 3, "y": 2}, {"label": "K32", "matrix": [3, 2], "x": 4, "y": 2}, {"label": "K72", "matrix": [7, 2], "x": 6, "y": 2}, {"label": "K44", "matrix": [4, 4], "x": 7, "y": 2}, {"label": "K43", "matrix": [4, 3], "x": 8, "y": 2}, {"label": "K54", "matrix": [5, 4], "x": 9, "y": 2}, {"label": "K55", "matrix": [5, 5], "x": 10, "y": 2}, {"label": "K62", "matrix": [6, 2], "x": 2, "y": 3}, {"label": "K61", "matrix": [6, 1], "x": 3, "y": 3}, {"label": "K33", "matrix": [3, 3], "x": 4, "y": 3}, {"label": "K66", "matrix": [6, 6], "x": 5, "y": 3}, {"label": "K73", "matrix": [7, 3], "x": 6, "y": 3}, {"label": "K64", "matrix": [6, 4], "x": 7, "y": 3}, {"label": "K63", "matrix": [6, 3], "x": 8, "y": 3}]}, "LAYOUT_40": {"layout": [{"label": "K70", "matrix": [7, 0], "x": 0, "y": 0}, {"label": "K10", "matrix": [1, 0], "x": 1, "y": 0}, {"label": "K11", "matrix": [1, 1], "x": 2, "y": 0}, {"label": "K02", "matrix": [0, 2], "x": 3, "y": 0}, {"label": "K01", "matrix": [0, 1], "x": 4, "y": 0}, {"label": "K12", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "K52", "matrix": [5, 2], "x": 7, "y": 0}, {"label": "K04", "matrix": [0, 4], "x": 8, "y": 0}, {"label": "K03", "matrix": [0, 3], "x": 9, "y": 0}, {"label": "K14", "matrix": [1, 4], "x": 10, "y": 0}, {"label": "K15", "matrix": [1, 5], "x": 11, "y": 0}, {"label": "K75", "matrix": [7, 5], "x": 12, "y": 0}, {"label": "K71", "matrix": [7, 1], "x": 0, "y": 1}, {"label": "K30", "matrix": [3, 0], "x": 1, "y": 1}, {"label": "K31", "matrix": [3, 1], "x": 2, "y": 1}, {"label": "K22", "matrix": [2, 2], "x": 3, "y": 1}, {"label": "K21", "matrix": [2, 1], "x": 4, "y": 1}, {"label": "K13", "matrix": [1, 3], "x": 5, "y": 1}, {"label": "K53", "matrix": [5, 3], "x": 7, "y": 1}, {"label": "K24", "matrix": [2, 4], "x": 8, "y": 1}, {"label": "K23", "matrix": [2, 3], "x": 9, "y": 1}, {"label": "K34", "matrix": [3, 4], "x": 10, "y": 1}, {"label": "K35", "matrix": [3, 5], "x": 11, "y": 1}, {"label": "K74", "matrix": [7, 4], "x": 12, "y": 1}, {"label": "K50", "matrix": [5, 0], "x": 1, "y": 2}, {"label": "K51", "matrix": [5, 1], "x": 2, "y": 2}, {"label": "K42", "matrix": [4, 2], "x": 3, "y": 2}, {"label": "K41", "matrix": [4, 1], "x": 4, "y": 2}, {"label": "K32", "matrix": [3, 2], "x": 5, "y": 2}, {"label": "K72", "matrix": [7, 2], "x": 7, "y": 2}, {"label": "K44", "matrix": [4, 4], "x": 8, "y": 2}, {"label": "K43", "matrix": [4, 3], "x": 9, "y": 2}, {"label": "K54", "matrix": [5, 4], "x": 10, "y": 2}, {"label": "K55", "matrix": [5, 5], "x": 11, "y": 2}, {"label": "K62", "matrix": [6, 2], "x": 3, "y": 3}, {"label": "K61", "matrix": [6, 1], "x": 4, "y": 3}, {"label": "K33", "matrix": [3, 3], "x": 5, "y": 3}, {"label": "K66", "matrix": [6, 6], "x": 6, "y": 3}, {"label": "K73", "matrix": [7, 3], "x": 7, "y": 3}, {"label": "K64", "matrix": [6, 4], "x": 8, "y": 3}, {"label": "K63", "matrix": [6, 3], "x": 9, "y": 3}]}}}