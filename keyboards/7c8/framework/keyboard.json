{"keyboard_name": "Framework", "manufacturer": "7c8", "maintainer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "usb": {"vid": "0x77C8", "pid": "0x0001", "device_version": "0.0.1"}, "features": {"bootmagic": false, "encoder": true, "extrakey": true, "leader": true, "mousekey": true, "nkro": false}, "matrix_pins": {"cols": ["C0", "C1", "C2", "C3", "C4", "C5"], "rows": ["B0", "B1", "D7", "B2", "D6", "B3", "D5", "B4", "D4", "B5"]}, "diode_direction": "COL2ROW", "encoder": {"rotary": [{"pin_a": "D0", "pin_b": "D1"}]}, "qmk": {"tap_keycode_delay": 16}, "leader_key": {"timeout": 250, "timing": true}, "processor": "atmega328p", "bootloader": "usbasploader", "layouts": {"LAYOUT_ortho_5x12": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [1, 0], "x": 6, "y": 0}, {"matrix": [1, 1], "x": 7, "y": 0}, {"matrix": [1, 2], "x": 8, "y": 0}, {"matrix": [1, 3], "x": 9, "y": 0}, {"matrix": [1, 4], "x": 10, "y": 0}, {"matrix": [1, 5], "x": 11, "y": 0}, {"matrix": [2, 0], "x": 0, "y": 1}, {"matrix": [2, 1], "x": 1, "y": 1}, {"matrix": [2, 2], "x": 2, "y": 1}, {"matrix": [2, 3], "x": 3, "y": 1}, {"matrix": [2, 4], "x": 4, "y": 1}, {"matrix": [2, 5], "x": 5, "y": 1}, {"matrix": [3, 0], "x": 6, "y": 1}, {"matrix": [3, 1], "x": 7, "y": 1}, {"matrix": [3, 2], "x": 8, "y": 1}, {"matrix": [3, 3], "x": 9, "y": 1}, {"matrix": [3, 4], "x": 10, "y": 1}, {"matrix": [3, 5], "x": 11, "y": 1}, {"matrix": [4, 0], "x": 0, "y": 2}, {"matrix": [4, 1], "x": 1, "y": 2}, {"matrix": [4, 2], "x": 2, "y": 2}, {"matrix": [4, 3], "x": 3, "y": 2}, {"matrix": [4, 4], "x": 4, "y": 2}, {"matrix": [4, 5], "x": 5, "y": 2}, {"matrix": [5, 0], "x": 6, "y": 2}, {"matrix": [5, 1], "x": 7, "y": 2}, {"matrix": [5, 2], "x": 8, "y": 2}, {"matrix": [5, 3], "x": 9, "y": 2}, {"matrix": [5, 4], "x": 10, "y": 2}, {"matrix": [5, 5], "x": 11, "y": 2}, {"matrix": [6, 0], "x": 0, "y": 3}, {"matrix": [6, 1], "x": 1, "y": 3}, {"matrix": [6, 2], "x": 2, "y": 3}, {"matrix": [6, 3], "x": 3, "y": 3}, {"matrix": [6, 4], "x": 4, "y": 3}, {"matrix": [6, 5], "x": 5, "y": 3}, {"matrix": [7, 0], "x": 6, "y": 3}, {"matrix": [7, 1], "x": 7, "y": 3}, {"matrix": [7, 2], "x": 8, "y": 3}, {"matrix": [7, 3], "x": 9, "y": 3}, {"matrix": [7, 4], "x": 10, "y": 3}, {"matrix": [7, 5], "x": 11, "y": 3}, {"matrix": [8, 0], "x": 0, "y": 4}, {"matrix": [8, 1], "x": 1, "y": 4}, {"matrix": [8, 2], "x": 2, "y": 4}, {"matrix": [8, 3], "x": 3, "y": 4}, {"matrix": [8, 4], "x": 4, "y": 4}, {"matrix": [8, 5], "x": 5, "y": 4}, {"matrix": [9, 0], "x": 6, "y": 4}, {"matrix": [9, 1], "x": 7, "y": 4}, {"matrix": [9, 2], "x": 8, "y": 4}, {"matrix": [9, 3], "x": 9, "y": 4}, {"matrix": [9, 4], "x": 10, "y": 4}, {"matrix": [9, 5], "x": 11, "y": 4}]}, "LAYOUT_ortho_5x12_1x2uC": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [1, 0], "x": 6, "y": 0}, {"matrix": [1, 1], "x": 7, "y": 0}, {"matrix": [1, 2], "x": 8, "y": 0}, {"matrix": [1, 3], "x": 9, "y": 0}, {"matrix": [1, 4], "x": 10, "y": 0}, {"matrix": [1, 5], "x": 11, "y": 0}, {"matrix": [2, 0], "x": 0, "y": 1}, {"matrix": [2, 1], "x": 1, "y": 1}, {"matrix": [2, 2], "x": 2, "y": 1}, {"matrix": [2, 3], "x": 3, "y": 1}, {"matrix": [2, 4], "x": 4, "y": 1}, {"matrix": [2, 5], "x": 5, "y": 1}, {"matrix": [3, 0], "x": 6, "y": 1}, {"matrix": [3, 1], "x": 7, "y": 1}, {"matrix": [3, 2], "x": 8, "y": 1}, {"matrix": [3, 3], "x": 9, "y": 1}, {"matrix": [3, 4], "x": 10, "y": 1}, {"matrix": [3, 5], "x": 11, "y": 1}, {"matrix": [4, 0], "x": 0, "y": 2}, {"matrix": [4, 1], "x": 1, "y": 2}, {"matrix": [4, 2], "x": 2, "y": 2}, {"matrix": [4, 3], "x": 3, "y": 2}, {"matrix": [4, 4], "x": 4, "y": 2}, {"matrix": [4, 5], "x": 5, "y": 2}, {"matrix": [5, 0], "x": 6, "y": 2}, {"matrix": [5, 1], "x": 7, "y": 2}, {"matrix": [5, 2], "x": 8, "y": 2}, {"matrix": [5, 3], "x": 9, "y": 2}, {"matrix": [5, 4], "x": 10, "y": 2}, {"matrix": [5, 5], "x": 11, "y": 2}, {"matrix": [6, 0], "x": 0, "y": 3}, {"matrix": [6, 1], "x": 1, "y": 3}, {"matrix": [6, 2], "x": 2, "y": 3}, {"matrix": [6, 3], "x": 3, "y": 3}, {"matrix": [6, 4], "x": 4, "y": 3}, {"matrix": [6, 5], "x": 5, "y": 3}, {"matrix": [7, 0], "x": 6, "y": 3}, {"matrix": [7, 1], "x": 7, "y": 3}, {"matrix": [7, 2], "x": 8, "y": 3}, {"matrix": [7, 3], "x": 9, "y": 3}, {"matrix": [7, 4], "x": 10, "y": 3}, {"matrix": [7, 5], "x": 11, "y": 3}, {"matrix": [8, 0], "x": 0, "y": 4}, {"matrix": [8, 1], "x": 1, "y": 4}, {"matrix": [8, 2], "x": 2, "y": 4}, {"matrix": [8, 3], "x": 3, "y": 4}, {"matrix": [8, 4], "x": 4, "y": 4}, {"matrix": [8, 5], "x": 5, "y": 4, "w": 2}, {"matrix": [9, 1], "x": 7, "y": 4}, {"matrix": [9, 2], "x": 8, "y": 4}, {"matrix": [9, 3], "x": 9, "y": 4}, {"matrix": [9, 4], "x": 10, "y": 4}, {"matrix": [9, 5], "x": 11, "y": 4}]}, "LAYOUT_preonic_2x2u": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [1, 0], "x": 6, "y": 0}, {"matrix": [1, 1], "x": 7, "y": 0}, {"matrix": [1, 2], "x": 8, "y": 0}, {"matrix": [1, 3], "x": 9, "y": 0}, {"matrix": [1, 4], "x": 10, "y": 0}, {"matrix": [1, 5], "x": 11, "y": 0}, {"matrix": [2, 0], "x": 0, "y": 1}, {"matrix": [2, 1], "x": 1, "y": 1}, {"matrix": [2, 2], "x": 2, "y": 1}, {"matrix": [2, 3], "x": 3, "y": 1}, {"matrix": [2, 4], "x": 4, "y": 1}, {"matrix": [2, 5], "x": 5, "y": 1}, {"matrix": [3, 0], "x": 6, "y": 1}, {"matrix": [3, 1], "x": 7, "y": 1}, {"matrix": [3, 2], "x": 8, "y": 1}, {"matrix": [3, 3], "x": 9, "y": 1}, {"matrix": [3, 4], "x": 10, "y": 1}, {"matrix": [3, 5], "x": 11, "y": 1}, {"matrix": [4, 0], "x": 0, "y": 2}, {"matrix": [4, 1], "x": 1, "y": 2}, {"matrix": [4, 2], "x": 2, "y": 2}, {"matrix": [4, 3], "x": 3, "y": 2}, {"matrix": [4, 4], "x": 4, "y": 2}, {"matrix": [4, 5], "x": 5, "y": 2}, {"matrix": [5, 0], "x": 6, "y": 2}, {"matrix": [5, 1], "x": 7, "y": 2}, {"matrix": [5, 2], "x": 8, "y": 2}, {"matrix": [5, 3], "x": 9, "y": 2}, {"matrix": [5, 4], "x": 10, "y": 2}, {"matrix": [5, 5], "x": 11, "y": 2}, {"matrix": [6, 0], "x": 0, "y": 3}, {"matrix": [6, 1], "x": 1, "y": 3}, {"matrix": [6, 2], "x": 2, "y": 3}, {"matrix": [6, 3], "x": 3, "y": 3}, {"matrix": [6, 4], "x": 4, "y": 3}, {"matrix": [6, 5], "x": 5, "y": 3}, {"matrix": [7, 0], "x": 6, "y": 3}, {"matrix": [7, 1], "x": 7, "y": 3}, {"matrix": [7, 2], "x": 8, "y": 3}, {"matrix": [7, 3], "x": 9, "y": 3}, {"matrix": [7, 4], "x": 10, "y": 3}, {"matrix": [7, 5], "x": 11, "y": 3}, {"matrix": [8, 0], "x": 0, "y": 4}, {"matrix": [8, 1], "x": 1, "y": 4}, {"matrix": [8, 2], "x": 2, "y": 4}, {"matrix": [8, 3], "x": 3, "y": 4}, {"matrix": [8, 5], "x": 4, "y": 4, "w": 2}, {"matrix": [9, 0], "x": 6, "y": 4, "w": 2}, {"matrix": [9, 2], "x": 8, "y": 4}, {"matrix": [9, 3], "x": 9, "y": 4}, {"matrix": [9, 4], "x": 10, "y": 4}, {"matrix": [9, 5], "x": 11, "y": 4}]}}}