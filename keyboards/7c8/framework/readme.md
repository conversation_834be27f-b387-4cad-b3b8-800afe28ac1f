# Framework

![Framework](https://i.imgur.com/njrHoH1.jpeg)

## Features

* All through-hole components (except Kailh hotswap sockets)
* QMK firmware with VIA support
* Rotary encoder
* Hotswappable switches
* FR-4 and acrylic sandwich construction
* USB Type-C
* Supports MIT (one 2u), grid (two 1u), and two 2u space keys

## Info

* Keyboard maintainer: [<PERSON>](https://github.com/stevennguyen)
* Hardware supported: Framework
* Hardware availability: [<PERSON>](https://github.com/stevennguyen)

Make example for this keyboard (after setting up your build environment):

    make 7c8/framework:default # default keymap
    make 7c8/framework:via     # via-compatible keymap

Flashing example for this keyboard:

    make 7c8/framework:default:flash

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).