{"keyboard_name": "<PERSON><PERSON><PERSON><PERSON> dux", "manufacturer": "tapio<PERSON>", "url": "https://github.com/tapioki/cephalopoda", "maintainer": "@tapioki", "usb": {"vid": "0xC2AB", "pid": "0x3939", "device_version": "0.0.1"}, "development_board": "promicro", "features": {"bootmagic": true, "extrakey": true, "mousekey": true, "unicode": true}, "matrix_pins": {"direct": [["C6", "D2", "F7", "B2", "F4"], ["D7", "D0", "F6", "B3", "F5"], ["E6", "D4", "D3", "B1", "B6"], ["B4", "B5", null, null, null]]}, "split": {"enabled": true, "bootmagic": {"matrix": [4, 4]}, "matrix_pins": {"right": {"direct": [["F4", "B2", "F7", "D2", "C6"], ["F5", "B3", "F6", "D0", "D7"], ["B6", "B1", "D3", "D4", "E6"], ["B5", "B4", null, null, null]]}}, "serial": {"pin": "D1"}}, "community_layouts": ["split_3x5_2"], "layout_aliases": {"LAYOUT": "LAYOUT_split_3x5_2"}, "layouts": {"LAYOUT_split_3x5_2": {"layout": [{"label": "L01", "matrix": [0, 0], "x": 0, "y": 1.33}, {"label": "L02", "matrix": [0, 1], "x": 1, "y": 0.31}, {"label": "L03", "matrix": [0, 2], "x": 2, "y": 0}, {"label": "L04", "matrix": [0, 3], "x": 3, "y": 0.28}, {"label": "L05", "matrix": [0, 4], "x": 4, "y": 0.42}, {"label": "R01", "matrix": [4, 0], "x": 8, "y": 0.42}, {"label": "R02", "matrix": [4, 1], "x": 9, "y": 0.28}, {"label": "R03", "matrix": [4, 2], "x": 10, "y": 0}, {"label": "R04", "matrix": [4, 3], "x": 11, "y": 0.31}, {"label": "R05", "matrix": [4, 4], "x": 12, "y": 1.33}, {"label": "L06", "matrix": [1, 0], "x": 0, "y": 2.33}, {"label": "L07", "matrix": [1, 1], "x": 1, "y": 1.31}, {"label": "L08", "matrix": [1, 2], "x": 2, "y": 1}, {"label": "L09", "matrix": [1, 3], "x": 3, "y": 1.28}, {"label": "L10", "matrix": [1, 4], "x": 4, "y": 1.42}, {"label": "R06", "matrix": [5, 0], "x": 8, "y": 1.42}, {"label": "R07", "matrix": [5, 1], "x": 9, "y": 1.28}, {"label": "R08", "matrix": [5, 2], "x": 10, "y": 1}, {"label": "R09", "matrix": [5, 3], "x": 11, "y": 1.31}, {"label": "R10", "matrix": [5, 4], "x": 12, "y": 2.33}, {"label": "L11", "matrix": [2, 0], "x": 0, "y": 3.33}, {"label": "L12", "matrix": [2, 1], "x": 1, "y": 2.31}, {"label": "L13", "matrix": [2, 2], "x": 2, "y": 2}, {"label": "L14", "matrix": [2, 3], "x": 3, "y": 2.28}, {"label": "L15", "matrix": [2, 4], "x": 4, "y": 2.42}, {"label": "R11", "matrix": [6, 0], "x": 8, "y": 2.42}, {"label": "R12", "matrix": [6, 1], "x": 9, "y": 2.28}, {"label": "R13", "matrix": [6, 2], "x": 10, "y": 2}, {"label": "R14", "matrix": [6, 3], "x": 11, "y": 2.31}, {"label": "R15", "matrix": [6, 4], "x": 12, "y": 3.33}, {"label": "L16", "matrix": [3, 0], "x": 4, "y": 3.75}, {"label": "L17", "matrix": [3, 1], "x": 5, "y": 4}, {"label": "R16", "matrix": [7, 0], "x": 7, "y": 4}, {"label": "R17", "matrix": [7, 1], "x": 8, "y": 3.75}]}}}