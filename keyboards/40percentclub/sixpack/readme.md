# Six Pack

![sixpack](https://4.bp.blogspot.com/-sCUGxPAmcu4/WQj4cd9ZlAI/AAAAAAACBhs/174XLIHIfzISucFFEgo53H73HdVJfn3ZwCLcB/s640/IMG_0265.JPG)

A 6-key macropad PCB with its switch and LED pins wired directly to microcontroller IO pins.

You can find the main blog post about this macropad [here](http://www.40percent.club/2017/05/six-pack-11.html), also the Gerber files and other documentation is located [here](https://git.40percent.club/di0ib/Misc/src/branch/master/Six%20Pack).

* Keyboard Maintainer: [The QMK Community](https://github.com/qmk)
* Hardware Supported: Six Pack PCB, Pro Micro
* Hardware Availability: [40percent.club](https://git.40percent.club/di0ib/Misc/src/branch/master/Six%20Pack)

Make example for this macropad (after setting up your build environment):

    make 40percentclub/sixpack:default

Flashing example for this keyboard:

    make 40percentclub/sixpack:default:flash

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
