# UT47 default keymap

![UT47 layout image](https://i.imgur.com/Tsz5qsF.png)

[KLE](http://www.keyboard-layout-editor.com/##@@_y:0%3B&=Esc&=Q&=W&=E&=R&=T&=Y&=U&=I&=O&=P&_w:1.5%3B&=Back%20Space&_x:0.25&a:4&f:4&w:4&h:4&d:true%3B&=%3Cb%3EGNAP!%3C%2F%2Fb%3E%3Cp%3E%3Cp%3EMinimum%20stagger%3Cp%3E47%20key%20layout%3B&@_a:7&f:3&w:1.25%3B&=Tab&=A&=S&=D&=F&=G&=H&=J&=K&=L&=%2F%3B&_w:1.25%3B&=%27%3B&@_w:1.5%3B&=Shift&=Z&=X&=C&=V&=B&=N&=M&=,&=.&=%2F%2F&=Return%3B&@=Ctrl&=Alt&=Super&=Menu&_w:1.25%3B&=%2F&dArr%2F%3B&_w:2%3B&=&_w:1.25%3B&=%2F&uArr%2F%3B&=%2F&larr%2F%3B&=%2F&darr%2F%3B&=%2F&uarr%2F%3B&=%2F&rarr%2F%3B%3B&=undefined)

### LED Controls

Use <kbd>TAB</kbd>+<kbd>Z</kbd> to cycle through brightness (8 steps)

Use <kbd>TAB</kbd>+<kbd>X</kbd> to cycle through the following LED modes: 

- solid
- breathing
- random
- rain
- reactive
- poptang
- off
