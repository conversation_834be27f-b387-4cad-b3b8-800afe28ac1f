/* Copyright 2018 <PERSON>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */
#include "quantum.h"
#ifdef LED_ENABLE
  #include "uart.h"

void matrix_init_kb(void) {
  uart_init(9600);

  matrix_init_user();
}
#endif

bool process_record_kb(uint16_t keycode, keyrecord_t *record) {
	// put your per-action keyboard code here
	// runs for every action, just before processing by the firmware
  if (record->event.pressed) {
    #ifdef LED_ENABLE
      uart_write((record->event.key.row*16)+record->event.key.col);
    #endif
  }
	return process_record_user(keycode, record);
}
