# 4x4

![4x4](https://2.bp.blogspot.com/-xRZOpwlTT4c/WlOwRIVAecI/AAAAAAACKo4/d75juHTX2W0Nrch7NigssMbagvl3I4w_ACEwYBhgL/s1600/e.jpg)
===

**Modular keypad/keyboard**  
The basic unit is a 4x4 matrix with 16 keys. Up to 4 of these can be connected to each other side by side.  
4x4, 4x8, 4x12 and 4x16 are possible.  
There are pads for header pins on each side that complete the circuits from board to board. These can be permanently connected with solder bridges or temporarily with pin headers and shunt jumpers.  
**_All configurations are powered by a SINGLE Arduino Micro or clone (NOT a Pro Micro)._**  

* [The original TMK firmware](https://github.com/di0ib/tmk_keyboard/tree/master/keyboard/4x4)

Keyboard Maintainer: QMK Community  
Hardware Supported: 4x4 PCB  
Hardware Availability: [4x4x4x4x4 project on 40% Keyboards](http://www.40percent.club/2018/01/4x4x4x4x4.html)  

Make example for this keyboard (after setting up your build environment):  

    make 40percentclub/4x4:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).

First pass at adding support for the 4x4 keyboard. Compiles but completely untested. Intended to kick-start development.  
