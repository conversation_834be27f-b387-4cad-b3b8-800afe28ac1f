{"keyboard_name": "The 4x4 Keyboard", "manufacturer": "di0ib", "maintainer": "qmk", "usb": {"vid": "0x4025", "pid": "0x0A0C", "device_version": "44.4.4"}, "matrix_pins": {"cols": ["C6", "D7", "E6", "B4", "B5", "B6", "B7", "D6", "F7", "F6", "F5", "F4", "F1", "F0", "B3", "B1"], "rows": ["B2", "D1", "D0", "D4"]}, "diode_direction": "COL2ROW", "processor": "atmega32u4", "bootloader": "caterina", "features": {"bootmagic": false, "mousekey": true, "extrakey": true, "console": true, "command": true}, "qmk": {"locking": {"enabled": true, "resync": true}}, "community_layouts": ["ortho_4x4", "ortho_4x12"], "layouts": {"LAYOUT_ortho_4x4": {"layout": [{"x": 0, "y": 0, "matrix": [0, 0]}, {"x": 1, "y": 0, "matrix": [0, 1]}, {"x": 2, "y": 0, "matrix": [0, 2]}, {"x": 3, "y": 0, "matrix": [0, 3]}, {"x": 0, "y": 1, "matrix": [1, 0]}, {"x": 1, "y": 1, "matrix": [1, 1]}, {"x": 2, "y": 1, "matrix": [1, 2]}, {"x": 3, "y": 1, "matrix": [1, 3]}, {"x": 0, "y": 2, "matrix": [2, 0]}, {"x": 1, "y": 2, "matrix": [2, 1]}, {"x": 2, "y": 2, "matrix": [2, 2]}, {"x": 3, "y": 2, "matrix": [2, 3]}, {"x": 0, "y": 3, "matrix": [3, 0]}, {"x": 1, "y": 3, "matrix": [3, 1]}, {"x": 2, "y": 3, "matrix": [3, 2]}, {"x": 3, "y": 3, "matrix": [3, 3]}]}, "LAYOUT_ortho_4x8": {"layout": [{"x": 0, "y": 0, "matrix": [0, 0]}, {"x": 1, "y": 0, "matrix": [0, 1]}, {"x": 2, "y": 0, "matrix": [0, 2]}, {"x": 3, "y": 0, "matrix": [0, 3]}, {"x": 4, "y": 0, "matrix": [0, 4]}, {"x": 5, "y": 0, "matrix": [0, 5]}, {"x": 6, "y": 0, "matrix": [0, 6]}, {"x": 7, "y": 0, "matrix": [0, 7]}, {"x": 0, "y": 1, "matrix": [1, 0]}, {"x": 1, "y": 1, "matrix": [1, 1]}, {"x": 2, "y": 1, "matrix": [1, 2]}, {"x": 3, "y": 1, "matrix": [1, 3]}, {"x": 4, "y": 1, "matrix": [1, 4]}, {"x": 5, "y": 1, "matrix": [1, 5]}, {"x": 6, "y": 1, "matrix": [1, 6]}, {"x": 7, "y": 1, "matrix": [1, 7]}, {"x": 0, "y": 2, "matrix": [2, 0]}, {"x": 1, "y": 2, "matrix": [2, 1]}, {"x": 2, "y": 2, "matrix": [2, 2]}, {"x": 3, "y": 2, "matrix": [2, 3]}, {"x": 4, "y": 2, "matrix": [2, 4]}, {"x": 5, "y": 2, "matrix": [2, 5]}, {"x": 6, "y": 2, "matrix": [2, 6]}, {"x": 7, "y": 2, "matrix": [2, 7]}, {"x": 0, "y": 3, "matrix": [3, 0]}, {"x": 1, "y": 3, "matrix": [3, 1]}, {"x": 2, "y": 3, "matrix": [3, 2]}, {"x": 3, "y": 3, "matrix": [3, 3]}, {"x": 4, "y": 3, "matrix": [3, 4]}, {"x": 5, "y": 3, "matrix": [3, 5]}, {"x": 6, "y": 3, "matrix": [3, 6]}, {"x": 7, "y": 3, "matrix": [3, 7]}]}, "LAYOUT_ortho_4x12": {"layout": [{"x": 0, "y": 0, "matrix": [0, 0]}, {"x": 1, "y": 0, "matrix": [0, 1]}, {"x": 2, "y": 0, "matrix": [0, 2]}, {"x": 3, "y": 0, "matrix": [0, 3]}, {"x": 4, "y": 0, "matrix": [0, 4]}, {"x": 5, "y": 0, "matrix": [0, 5]}, {"x": 6, "y": 0, "matrix": [0, 6]}, {"x": 7, "y": 0, "matrix": [0, 7]}, {"x": 8, "y": 0, "matrix": [0, 8]}, {"x": 9, "y": 0, "matrix": [0, 9]}, {"x": 10, "y": 0, "matrix": [0, 10]}, {"x": 11, "y": 0, "matrix": [0, 11]}, {"x": 0, "y": 1, "matrix": [1, 0]}, {"x": 1, "y": 1, "matrix": [1, 1]}, {"x": 2, "y": 1, "matrix": [1, 2]}, {"x": 3, "y": 1, "matrix": [1, 3]}, {"x": 4, "y": 1, "matrix": [1, 4]}, {"x": 5, "y": 1, "matrix": [1, 5]}, {"x": 6, "y": 1, "matrix": [1, 6]}, {"x": 7, "y": 1, "matrix": [1, 7]}, {"x": 8, "y": 1, "matrix": [1, 8]}, {"x": 9, "y": 1, "matrix": [1, 9]}, {"x": 10, "y": 1, "matrix": [1, 10]}, {"x": 11, "y": 1, "matrix": [1, 11]}, {"x": 0, "y": 2, "matrix": [2, 0]}, {"x": 1, "y": 2, "matrix": [2, 1]}, {"x": 2, "y": 2, "matrix": [2, 2]}, {"x": 3, "y": 2, "matrix": [2, 3]}, {"x": 4, "y": 2, "matrix": [2, 4]}, {"x": 5, "y": 2, "matrix": [2, 5]}, {"x": 6, "y": 2, "matrix": [2, 6]}, {"x": 7, "y": 2, "matrix": [2, 7]}, {"x": 8, "y": 2, "matrix": [2, 8]}, {"x": 9, "y": 2, "matrix": [2, 9]}, {"x": 10, "y": 2, "matrix": [2, 10]}, {"x": 11, "y": 2, "matrix": [2, 11]}, {"x": 0, "y": 3, "matrix": [3, 0]}, {"x": 1, "y": 3, "matrix": [3, 1]}, {"x": 2, "y": 3, "matrix": [3, 2]}, {"x": 3, "y": 3, "matrix": [3, 3]}, {"x": 4, "y": 3, "matrix": [3, 4]}, {"x": 5, "y": 3, "matrix": [3, 5]}, {"x": 6, "y": 3, "matrix": [3, 6]}, {"x": 7, "y": 3, "matrix": [3, 7]}, {"x": 8, "y": 3, "matrix": [3, 8]}, {"x": 9, "y": 3, "matrix": [3, 9]}, {"x": 10, "y": 3, "matrix": [3, 10]}, {"x": 11, "y": 3, "matrix": [3, 11]}]}, "LAYOUT_ortho_4x16": {"layout": [{"x": 0, "y": 0, "matrix": [0, 0]}, {"x": 1, "y": 0, "matrix": [0, 1]}, {"x": 2, "y": 0, "matrix": [0, 2]}, {"x": 3, "y": 0, "matrix": [0, 3]}, {"x": 4, "y": 0, "matrix": [0, 4]}, {"x": 5, "y": 0, "matrix": [0, 5]}, {"x": 6, "y": 0, "matrix": [0, 6]}, {"x": 7, "y": 0, "matrix": [0, 7]}, {"x": 8, "y": 0, "matrix": [0, 8]}, {"x": 9, "y": 0, "matrix": [0, 9]}, {"x": 10, "y": 0, "matrix": [0, 10]}, {"x": 11, "y": 0, "matrix": [0, 11]}, {"x": 12, "y": 0, "matrix": [0, 12]}, {"x": 13, "y": 0, "matrix": [0, 13]}, {"x": 14, "y": 0, "matrix": [0, 14]}, {"x": 15, "y": 0, "matrix": [0, 15]}, {"x": 0, "y": 1, "matrix": [1, 0]}, {"x": 1, "y": 1, "matrix": [1, 1]}, {"x": 2, "y": 1, "matrix": [1, 2]}, {"x": 3, "y": 1, "matrix": [1, 3]}, {"x": 4, "y": 1, "matrix": [1, 4]}, {"x": 5, "y": 1, "matrix": [1, 5]}, {"x": 6, "y": 1, "matrix": [1, 6]}, {"x": 7, "y": 1, "matrix": [1, 7]}, {"x": 8, "y": 1, "matrix": [1, 8]}, {"x": 9, "y": 1, "matrix": [1, 9]}, {"x": 10, "y": 1, "matrix": [1, 10]}, {"x": 11, "y": 1, "matrix": [1, 11]}, {"x": 12, "y": 1, "matrix": [1, 12]}, {"x": 13, "y": 1, "matrix": [1, 13]}, {"x": 14, "y": 1, "matrix": [1, 14]}, {"x": 15, "y": 1, "matrix": [1, 15]}, {"x": 0, "y": 2, "matrix": [2, 0]}, {"x": 1, "y": 2, "matrix": [2, 1]}, {"x": 2, "y": 2, "matrix": [2, 2]}, {"x": 3, "y": 2, "matrix": [2, 3]}, {"x": 4, "y": 2, "matrix": [2, 4]}, {"x": 5, "y": 2, "matrix": [2, 5]}, {"x": 6, "y": 2, "matrix": [2, 6]}, {"x": 7, "y": 2, "matrix": [2, 7]}, {"x": 8, "y": 2, "matrix": [2, 8]}, {"x": 9, "y": 2, "matrix": [2, 9]}, {"x": 10, "y": 2, "matrix": [2, 10]}, {"x": 11, "y": 2, "matrix": [2, 11]}, {"x": 12, "y": 2, "matrix": [2, 12]}, {"x": 13, "y": 2, "matrix": [2, 13]}, {"x": 14, "y": 2, "matrix": [2, 14]}, {"x": 15, "y": 2, "matrix": [2, 15]}, {"x": 0, "y": 3, "matrix": [3, 0]}, {"x": 1, "y": 3, "matrix": [3, 1]}, {"x": 2, "y": 3, "matrix": [3, 2]}, {"x": 3, "y": 3, "matrix": [3, 3]}, {"x": 4, "y": 3, "matrix": [3, 4]}, {"x": 5, "y": 3, "matrix": [3, 5]}, {"x": 6, "y": 3, "matrix": [3, 6]}, {"x": 7, "y": 3, "matrix": [3, 7]}, {"x": 8, "y": 3, "matrix": [3, 8]}, {"x": 9, "y": 3, "matrix": [3, 9]}, {"x": 10, "y": 3, "matrix": [3, 10]}, {"x": 11, "y": 3, "matrix": [3, 11]}, {"x": 12, "y": 3, "matrix": [3, 12]}, {"x": 13, "y": 3, "matrix": [3, 13]}, {"x": 14, "y": 3, "matrix": [3, 14]}, {"x": 15, "y": 3, "matrix": [3, 15]}]}}}