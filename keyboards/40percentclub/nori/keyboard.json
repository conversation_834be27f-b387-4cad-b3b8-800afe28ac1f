{"keyboard_name": "The nori Keyboard", "manufacturer": "di0ib", "maintainer": "qmk", "usb": {"vid": "0x4025", "pid": "0x0A0C", "device_version": "4.4.4"}, "matrix_pins": {"cols": ["F4", "F5", "F6", "F7", "B1", "B3", "B2", "B6", "D4", "C6", "D7", "E6"], "rows": ["D3", "D2", "D1", "D0"]}, "diode_direction": "COL2ROW", "features": {"bootmagic": true, "mousekey": true, "extrakey": true, "command": true, "nkro": true, "backlight": true, "rgblight": true}, "qmk": {"locking": {"enabled": true, "resync": true}}, "backlight": {"pin": "B5"}, "rgblight": {"saturation_steps": 8, "brightness_steps": 8, "led_count": 10, "animations": {"breathing": true, "rainbow_mood": true, "rainbow_swirl": true, "snake": true, "knight": true, "christmas": true, "static_gradient": true, "rgb_test": true, "alternating": true, "twinkle": true}}, "ws2812": {"pin": "B4"}, "development_board": "promicro", "community_layouts": ["ortho_4x4", "ortho_4x12"], "layouts": {"LAYOUT_ortho_4x4": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3}, {"matrix": [3, 1], "x": 1, "y": 3}, {"matrix": [3, 2], "x": 2, "y": 3}, {"matrix": [3, 3], "x": 3, "y": 3}]}, "LAYOUT_ortho_4x8": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [1, 4], "x": 4, "y": 1}, {"matrix": [1, 5], "x": 5, "y": 1}, {"matrix": [1, 6], "x": 6, "y": 1}, {"matrix": [1, 7], "x": 7, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2}, {"matrix": [2, 4], "x": 4, "y": 2}, {"matrix": [2, 5], "x": 5, "y": 2}, {"matrix": [2, 6], "x": 6, "y": 2}, {"matrix": [2, 7], "x": 7, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3}, {"matrix": [3, 1], "x": 1, "y": 3}, {"matrix": [3, 2], "x": 2, "y": 3}, {"matrix": [3, 3], "x": 3, "y": 3}, {"matrix": [3, 4], "x": 4, "y": 3}, {"matrix": [3, 5], "x": 5, "y": 3}, {"matrix": [3, 6], "x": 6, "y": 3}, {"matrix": [3, 7], "x": 7, "y": 3}]}, "LAYOUT_ortho_4x12": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [1, 4], "x": 4, "y": 1}, {"matrix": [1, 5], "x": 5, "y": 1}, {"matrix": [1, 6], "x": 6, "y": 1}, {"matrix": [1, 7], "x": 7, "y": 1}, {"matrix": [1, 8], "x": 8, "y": 1}, {"matrix": [1, 9], "x": 9, "y": 1}, {"matrix": [1, 10], "x": 10, "y": 1}, {"matrix": [1, 11], "x": 11, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2}, {"matrix": [2, 4], "x": 4, "y": 2}, {"matrix": [2, 5], "x": 5, "y": 2}, {"matrix": [2, 6], "x": 6, "y": 2}, {"matrix": [2, 7], "x": 7, "y": 2}, {"matrix": [2, 8], "x": 8, "y": 2}, {"matrix": [2, 9], "x": 9, "y": 2}, {"matrix": [2, 10], "x": 10, "y": 2}, {"matrix": [2, 11], "x": 11, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3}, {"matrix": [3, 1], "x": 1, "y": 3}, {"matrix": [3, 2], "x": 2, "y": 3}, {"matrix": [3, 3], "x": 3, "y": 3}, {"matrix": [3, 4], "x": 4, "y": 3}, {"matrix": [3, 5], "x": 5, "y": 3}, {"matrix": [3, 6], "x": 6, "y": 3}, {"matrix": [3, 7], "x": 7, "y": 3}, {"matrix": [3, 8], "x": 8, "y": 3}, {"matrix": [3, 9], "x": 9, "y": 3}, {"matrix": [3, 10], "x": 10, "y": 3}, {"matrix": [3, 11], "x": 11, "y": 3}]}}}