# Luddite

![Luddite](https://1.bp.blogspot.com/-GAAa-sMU_WU/W7uYLJJ8x1I/AAAAAAACS44/31n2z69BSboM4KT48YkNMJRYciC8LUMWgCLcBGAs/s640/a.jpg)
===

Luddite 60% keyboard with backlight and RGB underglow.

* [The original TMK firmware](https://github.com/di0ib/tmk_keyboard/tree/master/keyboard/luddite)

Keyboard Maintainer: QMK Community  
Hardware Supported: Luddite PCB  
Hardware Availability: [Luddite project on 40% Keyboards](http://www.40percent.club/search/label/luddite)

Make example for this keyboard (after setting up your build environment):

    make 40percentclub/luddite:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).

First pass at adding support for the luddite keyboard. Compiles but completely
untested. Intended to kick-start development.
