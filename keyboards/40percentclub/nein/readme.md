# nein

![nein](https://2.bp.blogspot.com/-avYV4grcAPQ/XL9a67SxgKI/AAAAAAACVCE/GSGVYRThaEEDd14M3LG34gQTv5ZabRI0QCEwYBhgL/s640/a.jpg)
===

A 9 key macropad.

Powered by a Pro Micro and can fit any of the various different sized variations of Pro Micro.

Keyboard Maintainer: QMK Community  
Hardware Supported: nein PCB  
Hardware Availability: [nein project on 40% Keyboards](https://www.40percent.club/2019/04/nein.html)

Make example for this keyboard (after setting up your build environment):

    make 40percentclub/nein:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
