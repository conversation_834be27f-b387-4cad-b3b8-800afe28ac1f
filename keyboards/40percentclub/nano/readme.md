# nano

![nano](https://1.bp.blogspot.com/-goa_eXx1McA/WEhvoSdfDSI/AAAAAAAB_bM/XdstcXe_eKkQxuYcNI5cTySTVo3xZxaeQCEw/s640/IMG_20161207_101051.jpg)
===

A 2x4 mini switch pad built using 6mm x 6mm tactile switches and a Pro Micro. [More info here.](http://www.40percent.club/2016/12/nano-tmk.html)

Keyboard Maintainer: QMK Community   
Hardware Supported: Pro Micro ATmega32U4  
Hardware Availability: [PCB Files](https://github.com/di0ib/tmk_keyboard/tree/master/keyboard/nano/pcb)

Make example for this keyboard (after setting up your build environment):  

    make 40percentclub/nano:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
