/* Copyright 2019 Boy_314
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */
#include QMK_KEYBOARD_H

const uint16_t PROGMEM keymaps[][MATRIX_ROWS][MATRIX_COLS] = {
  [0] = LAYOUT( /* Base */
    QK_GESC, KC_Q, KC_W, KC_E, KC_R,   KC_T, KC_LCTL, KC_RCTL, KC_Y, KC_U,   KC_I,    <PERSON>_<PERSON>,   <PERSON>_<PERSON>,    KC_BSPC,
    KC_TAB,  KC_A, KC_<PERSON>, <PERSON>_<PERSON>, <PERSON>_<PERSON>,   <PERSON>_<PERSON>, KC_LALT, KC_RALT, KC_<PERSON>, <PERSON>_<PERSON>,   <PERSON>_<PERSON>,    KC_L,   KC_SCLN, KC_QUOT,
    KC_LSFT, KC_Z, KC_X, KC_C, KC_V,   KC_B, KC_LGUI, KC_APP,  KC_N, KC_M,   KC_COMM, KC_DOT, KC_SLSH, KC_ENT,
                               KC_SPC,                               KC_SPC
  ),
};
