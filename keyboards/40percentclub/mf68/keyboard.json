{"keyboard_name": "MF68", "manufacturer": "di0ib", "maintainer": "qmk", "usb": {"vid": "0x4025", "pid": "0x4D68", "device_version": "1.0.1"}, "features": {"backlight": true, "bootmagic": true, "extrakey": true, "mousekey": true, "nkro": true}, "qmk": {"locking": {"enabled": true, "resync": true}}, "matrix_pins": {"cols": ["D3", "D2", "D1", "D0", "D4", "C6", "D7", "E6", "B4"], "rows": ["B6", "B2", "B3", "B1", "F7", "F6", "F5", "F4"]}, "diode_direction": "COL2ROW", "backlight": {"pin": "B5", "breathing": true}, "development_board": "promicro", "community_layouts": ["68_ansi"], "layouts": {"LAYOUT_68_ansi": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [1, 0], "x": 9, "y": 0}, {"matrix": [1, 1], "x": 10, "y": 0}, {"matrix": [1, 2], "x": 11, "y": 0}, {"matrix": [1, 3], "x": 12, "y": 0}, {"matrix": [1, 4], "x": 13, "y": 0, "w": 2}, {"matrix": [1, 5], "x": 15.25, "y": 0}, {"matrix": [1, 6], "x": 16.25, "y": 0}, {"matrix": [1, 7], "x": 0, "y": 1, "w": 1.5}, {"matrix": [1, 8], "x": 1.5, "y": 1}, {"matrix": [2, 0], "x": 2.5, "y": 1}, {"matrix": [2, 1], "x": 3.5, "y": 1}, {"matrix": [2, 2], "x": 4.5, "y": 1}, {"matrix": [2, 3], "x": 5.5, "y": 1}, {"matrix": [2, 4], "x": 6.5, "y": 1}, {"matrix": [2, 5], "x": 7.5, "y": 1}, {"matrix": [2, 6], "x": 8.5, "y": 1}, {"matrix": [2, 7], "x": 9.5, "y": 1}, {"matrix": [2, 8], "x": 10.5, "y": 1}, {"matrix": [3, 0], "x": 11.5, "y": 1}, {"matrix": [3, 1], "x": 12.5, "y": 1}, {"matrix": [3, 2], "x": 13.5, "y": 1, "w": 1.5}, {"matrix": [3, 3], "x": 15.25, "y": 1}, {"matrix": [3, 4], "x": 16.25, "y": 1}, {"matrix": [3, 5], "x": 0, "y": 2, "w": 1.75}, {"matrix": [3, 6], "x": 1.75, "y": 2}, {"matrix": [3, 7], "x": 2.75, "y": 2}, {"matrix": [3, 8], "x": 3.75, "y": 2}, {"matrix": [4, 0], "x": 4.75, "y": 2}, {"matrix": [4, 1], "x": 5.75, "y": 2}, {"matrix": [4, 2], "x": 6.75, "y": 2}, {"matrix": [4, 3], "x": 7.75, "y": 2}, {"matrix": [4, 4], "x": 8.75, "y": 2}, {"matrix": [4, 5], "x": 9.75, "y": 2}, {"matrix": [4, 6], "x": 10.75, "y": 2}, {"matrix": [4, 7], "x": 11.75, "y": 2}, {"matrix": [4, 8], "x": 12.75, "y": 2, "w": 2.25}, {"matrix": [5, 0], "x": 0, "y": 3, "w": 2.25}, {"matrix": [5, 1], "x": 2.25, "y": 3}, {"matrix": [5, 2], "x": 3.25, "y": 3}, {"matrix": [5, 3], "x": 4.25, "y": 3}, {"matrix": [5, 4], "x": 5.25, "y": 3}, {"matrix": [5, 5], "x": 6.25, "y": 3}, {"matrix": [5, 6], "x": 7.25, "y": 3}, {"matrix": [5, 7], "x": 8.25, "y": 3}, {"matrix": [5, 8], "x": 9.25, "y": 3}, {"matrix": [6, 0], "x": 10.25, "y": 3}, {"matrix": [6, 1], "x": 11.25, "y": 3}, {"matrix": [6, 2], "x": 12.25, "y": 3, "w": 2.75}, {"matrix": [6, 3], "x": 15.25, "y": 3}, {"matrix": [6, 4], "x": 0, "y": 4, "w": 1.25}, {"matrix": [6, 5], "x": 1.25, "y": 4, "w": 1.25}, {"matrix": [6, 6], "x": 2.5, "y": 4, "w": 1.25}, {"matrix": [6, 7], "x": 3.75, "y": 4, "w": 6.25}, {"matrix": [6, 8], "x": 10, "y": 4, "w": 1.25}, {"matrix": [7, 0], "x": 11.25, "y": 4, "w": 1.25}, {"matrix": [7, 1], "x": 12.5, "y": 4, "w": 1.25}, {"matrix": [7, 2], "x": 14.25, "y": 4}, {"matrix": [7, 3], "x": 15.25, "y": 4}, {"matrix": [7, 4], "x": 16.25, "y": 4}]}}}