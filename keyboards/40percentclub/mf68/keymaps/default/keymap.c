#include QM<PERSON>_KEYBOARD_H

#define _QWERTY 0
#define _FN1 1
#define _FN2 2

#define FN2_GRV LT(_FN2, KC_GRV)

const uint16_t PROGMEM keymaps[][MATRIX_ROWS][MATRIX_COLS] = {
  [_QWERTY] = LAYOUT_68_ansi(
    KC_ESC,  KC_1,    KC_2,    KC_3,    KC_4,    KC_5,    KC_6,    KC_7,    KC_8,    KC_9,    KC_0,    KC_MINS, KC_EQL,  KC_BSPC,          KC_INS,  KC_PGUP,
    KC_TAB,  KC_Q,    KC_W,    KC_E,    KC_R,    KC_<PERSON>,    KC_Y,    KC_U,    KC_<PERSON>,    KC_<PERSON>,    KC_P,    KC_LBRC, KC_RBRC, KC_BSLS,          KC_DEL,  KC_PGDN,
    FN2_GRV, KC_A,    KC_S,    KC_D,    KC_F,    KC_G,    KC_<PERSON>,    KC_J,    KC_K,    KC_L,    KC_SCLN, KC_QUOT,          KC_ENT,
    <PERSON><PERSON><PERSON>SF<PERSON>, <PERSON><PERSON><PERSON>,    <PERSON><PERSON><PERSON>,    <PERSON><PERSON><PERSON>,    <PERSON><PERSON><PERSON>,    <PERSON><PERSON><PERSON>,    <PERSON><PERSON><PERSON>,    <PERSON><PERSON><PERSON>,    <PERSON><PERSON><PERSON>, KC_DOT,  KC_SLSH,                   <PERSON><PERSON><PERSON><PERSON>,          <PERSON>_<PERSON>,
    <PERSON>_LCTL, KC_LGUI, KC_LALT,                            KC_SPC,                             MO(1),   KC_RALT, KC_RCTL,          KC_LEFT, KC_DOWN, KC_RGHT
  ),
  [_FN1] = LAYOUT_68_ansi(
    KC_GRV,  KC_F1,   KC_F2,   KC_F3,   KC_F4,   KC_F5,   KC_F6,   KC_F7,   KC_F8,   KC_F9,   KC_F10,  KC_F11,  KC_F12,  KC_BSPC,          KC_VOLU, KC_HOME,
    _______, _______, _______, KC_UP,   _______, _______, _______, _______, _______, _______, _______, _______, BL_STEP, _______,          KC_VOLD, KC_END,
    _______, _______, KC_LEFT, KC_DOWN, KC_RGHT, _______, _______, _______, _______, _______, _______, _______,          _______,
    _______, _______, _______, _______, _______, _______, _______, KC_MUTE, _______, _______, _______,                   _______,          KC_MUTE,
    _______, _______, _______,                            _______,                            _______, _______, _______,          KC_MPRV, KC_MPLY, KC_MNXT
  ),
  [_FN2] = LAYOUT_68_ansi(
    KC_GRV,  KC_F1,   KC_F2,   KC_F3,   KC_F4,   KC_F5,   KC_F6,   KC_F7,   KC_F8,   KC_F9,   KC_F10,  KC_F11,  KC_F12,  KC_BSPC,          KC_VOLU, KC_HOME,
    _______, _______, _______, KC_UP,   _______, _______, _______, KC_7,    KC_8,    KC_9,    _______, _______, _______, _______,          KC_VOLD, KC_END,
    _______, _______, KC_LEFT, KC_DOWN, KC_RGHT, _______, _______, KC_4,    KC_5,    KC_6,    _______, _______,          _______,
    _______, _______, _______, _______, _______, _______, KC_0,    KC_1,    KC_2,    KC_3,    _______,                   _______,          KC_MUTE,
    _______, _______, _______,                            _______,                            _______, _______, _______,          KC_MPRV, KC_MPLY, KC_MNXT
  )
};
