# 4 Pack

![4pack](https://i.imgur.com/rioXXq5l.jpg)

A 4-key macropad PCB with its switch and LED pins wired directly to microcontroller IO pins.

You can find the main blog post about this macropad [here](http://www.40percent.club/2017/07/4-pack.html), also the Gerber files and other documentation is located [here](https://git.40percent.club/di0ib/Misc/src/branch/master/4pack).

Keyboard Maintainer: [<PERSON><PERSON>](http://github.com/ardakilic), [The QMK Community](https://github.com/qmk)  
Hardware Supported: 4 Pack PCB, Pro Micro  
Hardware Availability: [40percent.club](https://git.40percent.club/di0ib/Misc/src/branch/master/4pack)

Make example for this macropad (after setting up your build environment):

    make 40percentclub/4pack:default:flash

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
