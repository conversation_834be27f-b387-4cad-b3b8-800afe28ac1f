# Tomato

![<PERSON><PERSON>](https://2.bp.blogspot.com/-k6lyvfZz2WA/WXYyr508D7I/AAAAAAACB8A/UCNP2WCfvWAT8UWsCDICMRXwip5tAZsOwCLcBGAs/s640/a.JPG)
===

A 30 key keyboard with programmable rgb backlighting.

* [The fluff](http://www.40percent.club/2017/07/tomato-in-gherkin.html)
* [The meat](https://github.com/di0ib/Misc/tree/master/tomato)

Keyboard Maintainer: QMK Community  
Hardware Supported: Tomato PCB  
Hardware Availability: [Gherkin project on 40% Keyboards](http://www.40percent.club/2017/07/tomato-in-gherkin.html)

Make example for this keyboard (after setting up your build environment):

    make 40percentclub/tomato:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).

First pass at adding support for the tomato keyboard. Completely
untested. Intended to kick-start development.
