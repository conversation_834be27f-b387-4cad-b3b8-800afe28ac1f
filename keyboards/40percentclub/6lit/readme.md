# 6lit

![6lit](https://1.bp.blogspot.com/-Pa8RgYZ0hy8/Wbmr4bjuV0I/AAAAAAACDbI/WLKQMDlcDVAVf1lAIRMN51usR1XcCCVNgCLcBGAs/s1600/a.JPG)
===

6 key macropad that fits within the 100mm x 100mm PCB size. Can be used singly as a regular 6 key macropad as well.

* [The original TMK firmware](https://github.com/di0ib/tmk_keyboard/tree/master/keyboard/6lit)

Keyboard Maintainer: QMK Community  
Hardware Supported: 6lit PCB  
Hardware Availability: [6lit project on 40% Keyboards](http://www.40percent.club/2017/09/6lit.html)

Make example for this keyboard (after setting up your build environment):

    make 40percentclub/6lit:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
