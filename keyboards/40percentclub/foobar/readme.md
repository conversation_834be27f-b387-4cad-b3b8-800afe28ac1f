# foobar

![foobar](https://2.bp.blogspot.com/-rr8TK6xg2N0/WbwVL2O7EfI/AAAAAAACDc8/209wN69ju0wAFICYYPNLIEGQhat5-5MbwCLcBGAs/s640/a.JPG)
===

Split 30% keyboard that fits within the 100mm x 100mm PCB size. Can be used together as a split keyboard or as a single 15 key macropad.

* [The original TMK firmware](https://github.com/di0ib/tmk_keyboard/tree/master/keyboard/foobar)

Keyboard Maintainer: QMK Community  
Hardware Supported: foobar PCB  
Hardware Availability: [foobar project on 40% Keyboards](http://www.40percent.club/2017/09/foobar-10.html)

Make example for this keyboard (after setting up your build environment):

    make 40percentclub/foobar:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
