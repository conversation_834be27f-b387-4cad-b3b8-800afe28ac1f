{"keyboard_name": "The 5x5 Keyboard", "manufacturer": "di0ib", "maintainer": "qmk", "usb": {"vid": "0x4025", "pid": "0x0F25", "device_version": "1.0.0"}, "matrix_pins": {"cols": ["F4", "F5", "F6", "F7", "B1"], "rows": ["D4", "C6", "D7", "E6", "B4"]}, "diode_direction": "COL2ROW", "features": {"bootmagic": true, "mousekey": true, "extrakey": true, "command": true, "nkro": true}, "qmk": {"locking": {"enabled": true, "resync": true}}, "split": {"enabled": true, "serial": {"pin": "D0"}}, "development_board": "promicro", "community_layouts": ["ortho_5x5", "ortho_5x10"], "layout_aliases": {"LAYOUT_macro": "LAYOUT_ortho_5x5", "LAYOUT_split": "LAYOUT_ortho_5x10"}, "layouts": {"LAYOUT_ortho_5x5": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [1, 4], "x": 4, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2}, {"matrix": [2, 4], "x": 4, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3}, {"matrix": [3, 1], "x": 1, "y": 3}, {"matrix": [3, 2], "x": 2, "y": 3}, {"matrix": [3, 3], "x": 3, "y": 3}, {"matrix": [3, 4], "x": 4, "y": 3}, {"matrix": [4, 0], "x": 0, "y": 4}, {"matrix": [4, 1], "x": 1, "y": 4}, {"matrix": [4, 2], "x": 2, "y": 4}, {"matrix": [4, 3], "x": 3, "y": 4}, {"matrix": [4, 4], "x": 4, "y": 4}]}, "LAYOUT_ortho_5x10": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [5, 4], "x": 5, "y": 0}, {"matrix": [5, 3], "x": 6, "y": 0}, {"matrix": [5, 2], "x": 7, "y": 0}, {"matrix": [5, 1], "x": 8, "y": 0}, {"matrix": [5, 0], "x": 9, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [1, 4], "x": 4, "y": 1}, {"matrix": [6, 4], "x": 5, "y": 1}, {"matrix": [6, 3], "x": 6, "y": 1}, {"matrix": [6, 2], "x": 7, "y": 1}, {"matrix": [6, 1], "x": 8, "y": 1}, {"matrix": [6, 0], "x": 9, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2}, {"matrix": [2, 4], "x": 4, "y": 2}, {"matrix": [7, 4], "x": 5, "y": 2}, {"matrix": [7, 3], "x": 6, "y": 2}, {"matrix": [7, 2], "x": 7, "y": 2}, {"matrix": [7, 1], "x": 8, "y": 2}, {"matrix": [7, 0], "x": 9, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3}, {"matrix": [3, 1], "x": 1, "y": 3}, {"matrix": [3, 2], "x": 2, "y": 3}, {"matrix": [3, 3], "x": 3, "y": 3}, {"matrix": [3, 4], "x": 4, "y": 3}, {"matrix": [8, 4], "x": 5, "y": 3}, {"matrix": [8, 3], "x": 6, "y": 3}, {"matrix": [8, 2], "x": 7, "y": 3}, {"matrix": [8, 1], "x": 8, "y": 3}, {"matrix": [8, 0], "x": 9, "y": 3}, {"matrix": [4, 0], "x": 0, "y": 4}, {"matrix": [4, 1], "x": 1, "y": 4}, {"matrix": [4, 2], "x": 2, "y": 4}, {"matrix": [4, 3], "x": 3, "y": 4}, {"matrix": [4, 4], "x": 4, "y": 4}, {"matrix": [9, 4], "x": 5, "y": 4}, {"matrix": [9, 3], "x": 6, "y": 4}, {"matrix": [9, 2], "x": 7, "y": 4}, {"matrix": [9, 1], "x": 8, "y": 4}, {"matrix": [9, 0], "x": 9, "y": 4}]}}}