# 25

![25](https://2.bp.blogspot.com/-lBK_ZlB3a9Q/WcAM0B_vwdI/AAAAAAACDgs/qnI9YB53pzck4Bw0v5QRvypDMs80CxWVQCLcBGAs/s640/IMG_0695.JPG)
===

Split 50 key keyboard - it is the largest keybad that fits within the 100mm x 100mm PCB size. Can be used together as a split keyboard or as a single 25 key macropad.

* [The original TMK firmware](https://github.com/di0ib/tmk_keyboard/tree/master/keyboard/25)

Keyboard Maintainer: QMK Community  
Hardware Supported: 25 PCB  
Hardware Availability: [25 project on 40% Keyboards](http://www.40percent.club/2017/09/25.html)

Make example for this keyboard (after setting up your build environment):

    make 40percentclub/25:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
