# Gherkin

![G<PERSON><PERSON>](https://i.imgur.com/XrqqtTq.jpg)
![KB2040](https://cdn-shop.adafruit.com/640x480/5302-12.jpg)

===

A 30 key keyboard with Adafruit's KB2040 as microcontroller.

* [The original TMK firmware](https://github.com/di0ib/tmk_keyboard/tree/master/keyboard/gherkin)

Keyboard Maintainer: QMK Community  
Hardware Supported: Gherkin PCB  & Adafruit KB2040
Hardware Availability: [Gherkin project on 40% Keyboards](http://www.40percent.club/2016/11/gherkin.html) and [Adafruit KB2040](https://www.adafruit.com/product/5302)

Make example for this keyboard (after setting up your build environment):

    make 40percentclub/gherkin/kb2040:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).

Use the KB2040 microcontroller as a drop-in replacement for the Pro Micro in this cute 30% keyboard.
