# Gherkin

![Gherkin](https://i.imgur.com/XrqqtTq.jpg)
===

A 30 key keyboard.

* [The original TMK firmware](https://github.com/di0ib/tmk_keyboard/tree/master/keyboard/gherkin)

Keyboard Maintainer: QMK Community  
Hardware Supported: Gherkin PCB  
Hardware Availability: [Gherkin project on 40% Keyboards](http://www.40percent.club/2016/11/gherkin.html)

Make example for this keyboard (after setting up your build environment):

    make 40percentclub/gherkin:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).

First pass at adding support for the gherkin keyboard. Compiles but completely
untested. Intended to kick-start development.
