# Abacus

![abacus](https://i.imgur.com/IFtuWaK.jpg)

A first attempt at a PCB design for a mechanical keyboard. Includes rotary encoder and RGB underglow.

* Keyboard Maintainer: [nickolaij](https://github.com/nickolaij)
* Hardware Supported: Abacus PCB, [Elite C Microcontroller](https://keeb.io/products/elite-c-usb-c-pro-micro-replacement-arduino-compatible-atmega32u4) or Pro Micro Microcontroller (Elite C has additional pins for encoder)
* Hardware Availability: [Abacus PCB GitHub](https://github.com/nickolaij/Abacus_Rev2)

Make example for this keyboard (after setting up your build environment):

    make abacus:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
