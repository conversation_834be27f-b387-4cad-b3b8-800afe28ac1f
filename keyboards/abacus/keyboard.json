{"keyboard_name": "Abacus", "manufacturer": "nick<PERSON><PERSON><PERSON>", "url": "https://www.github.com/nickolaij", "maintainer": "nick<PERSON><PERSON><PERSON>", "usb": {"vid": "0xFEED", "pid": "0x0000", "device_version": "0.0.1"}, "build": {"lto": true}, "features": {"bootmagic": false, "command": true, "console": true, "dip_switch": true, "encoder": true, "extrakey": true, "mousekey": false, "nkro": false, "rgblight": true}, "qmk": {"locking": {"enabled": true, "resync": true}}, "matrix_pins": {"cols": ["F4", "F5", "F6", "F7", "B1", "D7", "B3", "E6", "B2", "B4", "B6", "B5"], "rows": ["D3", "D2", "D4", "C6"]}, "diode_direction": "COL2ROW", "dip_switch": {"pins": ["D0"]}, "encoder": {"rotary": [{"pin_a": "F1", "pin_b": "F0"}]}, "rgblight": {"saturation_steps": 8, "brightness_steps": 8, "led_count": 17, "sleep": true, "animations": {"breathing": true, "rainbow_mood": true, "rainbow_swirl": true, "snake": true, "knight": true, "static_gradient": true}}, "ws2812": {"pin": "D1"}, "processor": "atmega32u4", "bootloader": "atmel-dfu", "layouts": {"LAYOUT": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0, "w": 1.75}, {"matrix": [1, 0], "x": 0, "y": 1, "w": 1.25}, {"matrix": [1, 1], "x": 1.25, "y": 1}, {"matrix": [1, 2], "x": 2.25, "y": 1}, {"matrix": [1, 3], "x": 3.25, "y": 1}, {"matrix": [1, 4], "x": 4.25, "y": 1}, {"matrix": [1, 5], "x": 5.25, "y": 1}, {"matrix": [1, 6], "x": 6.25, "y": 1}, {"matrix": [1, 7], "x": 7.25, "y": 1}, {"matrix": [1, 8], "x": 8.25, "y": 1}, {"matrix": [1, 9], "x": 9.25, "y": 1}, {"matrix": [1, 10], "x": 10.25, "y": 1}, {"matrix": [1, 11], "x": 11.25, "y": 1, "w": 1.5}, {"matrix": [2, 0], "x": 0, "y": 2, "w": 1.75}, {"matrix": [2, 1], "x": 1.75, "y": 2}, {"matrix": [2, 2], "x": 2.75, "y": 2}, {"matrix": [2, 3], "x": 3.75, "y": 2}, {"matrix": [2, 4], "x": 4.75, "y": 2}, {"matrix": [2, 5], "x": 5.75, "y": 2}, {"matrix": [2, 6], "x": 6.75, "y": 2}, {"matrix": [2, 7], "x": 7.75, "y": 2}, {"matrix": [2, 8], "x": 8.75, "y": 2}, {"matrix": [2, 9], "x": 9.75, "y": 2}, {"matrix": [2, 10], "x": 10.75, "y": 2}, {"matrix": [2, 11], "x": 11.75, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3, "w": 1.25}, {"matrix": [3, 1], "x": 1.25, "y": 3}, {"matrix": [3, 2], "x": 2.25, "y": 3}, {"matrix": [3, 3], "x": 3.25, "y": 3, "w": 2.75}, {"matrix": [3, 6], "x": 6, "y": 3, "w": 2.75}, {"matrix": [3, 8], "x": 8.75, "y": 3}, {"matrix": [3, 9], "x": 9.75, "y": 3}, {"matrix": [3, 10], "x": 10.75, "y": 3}, {"matrix": [3, 11], "x": 11.75, "y": 3}]}}}