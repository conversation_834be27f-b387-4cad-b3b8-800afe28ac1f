{"manufacturer": "abko", "keyboard_name": "ak84bt", "bootloader": "stm32duino", "bootmagic": {"matrix": [1, 0]}, "diode_direction": "COL2ROW", "dynamic_keymap": {"layer_count": 2}, "features": {"bootmagic": true, "console": true, "extrakey": true, "mousekey": true, "nkro": true, "rgb_matrix": true}, "indicators": {"caps_lock": "B8", "on_state": 0}, "matrix_pins": {"cols": ["A6", "A7", "C4", "C5", "B0", "B1", "C6", "C7", "C8", "C9", "A8", "A9", "A10", "A13", "A14", "A15"], "rows": ["A0", "A1", "A2", "A3", "A4", "A5"]}, "processor": "STM32F103", "rgb_matrix": {"animations": {"alphas_mods": true, "gradient_up_down": true, "gradient_left_right": true, "breathing": true, "band_sat": true, "band_val": true, "band_spiral_val": true, "cycle_all": true, "cycle_left_right": true, "cycle_up_down": true, "hue_wave": true, "pixel_flow": true}, "driver": "is31fl3733", "layout": [{"matrix": [0, 0], "x": 7, "y": 2, "flags": 4}, {"matrix": [0, 2], "x": 34, "y": 2, "flags": 4}, {"matrix": [0, 3], "x": 48, "y": 2, "flags": 4}, {"matrix": [0, 4], "x": 61, "y": 2, "flags": 4}, {"matrix": [0, 5], "x": 75, "y": 2, "flags": 4}, {"matrix": [0, 6], "x": 95, "y": 2, "flags": 4}, {"matrix": [0, 7], "x": 109, "y": 2, "flags": 4}, {"matrix": [0, 8], "x": 122, "y": 2, "flags": 4}, {"matrix": [0, 9], "x": 136, "y": 2, "flags": 4}, {"matrix": [0, 10], "x": 157, "y": 2, "flags": 4}, {"matrix": [0, 11], "x": 170, "y": 2, "flags": 4}, {"matrix": [0, 12], "x": 184, "y": 2, "flags": 4}, {"matrix": [0, 13], "x": 198, "y": 2, "flags": 4}, {"matrix": [0, 14], "x": 217, "y": 2, "flags": 4}, {"matrix": [1, 0], "x": 7, "y": 14, "flags": 4}, {"matrix": [1, 1], "x": 20, "y": 14, "flags": 4}, {"matrix": [1, 2], "x": 34, "y": 14, "flags": 4}, {"matrix": [1, 3], "x": 48, "y": 14, "flags": 4}, {"matrix": [1, 4], "x": 61, "y": 14, "flags": 4}, {"matrix": [1, 5], "x": 75, "y": 14, "flags": 4}, {"matrix": [1, 6], "x": 89, "y": 14, "flags": 4}, {"matrix": [1, 7], "x": 102, "y": 14, "flags": 4}, {"matrix": [1, 8], "x": 116, "y": 14, "flags": 4}, {"matrix": [1, 9], "x": 129, "y": 14, "flags": 4}, {"matrix": [1, 10], "x": 143, "y": 14, "flags": 4}, {"matrix": [1, 11], "x": 157, "y": 14, "flags": 4}, {"matrix": [1, 12], "x": 170, "y": 14, "flags": 4}, {"matrix": [1, 13], "x": 190, "y": 14, "flags": 4}, {"matrix": [1, 14], "x": 217, "y": 14, "flags": 4}, {"matrix": [2, 0], "x": 10, "y": 24, "flags": 4}, {"matrix": [2, 1], "x": 27, "y": 24, "flags": 4}, {"matrix": [2, 2], "x": 40, "y": 24, "flags": 4}, {"matrix": [2, 3], "x": 54, "y": 24, "flags": 4}, {"matrix": [2, 4], "x": 68, "y": 24, "flags": 4}, {"matrix": [2, 5], "x": 81, "y": 24, "flags": 4}, {"matrix": [2, 6], "x": 95, "y": 24, "flags": 4}, {"matrix": [2, 7], "x": 109, "y": 24, "flags": 4}, {"matrix": [2, 8], "x": 122, "y": 24, "flags": 4}, {"matrix": [2, 9], "x": 136, "y": 24, "flags": 4}, {"matrix": [2, 10], "x": 150, "y": 24, "flags": 4}, {"matrix": [2, 11], "x": 163, "y": 24, "flags": 4}, {"matrix": [2, 12], "x": 177, "y": 24, "flags": 4}, {"matrix": [2, 13], "x": 194, "y": 24, "flags": 4}, {"matrix": [2, 14], "x": 217, "y": 24, "flags": 4}, {"matrix": [3, 0], "x": 12, "y": 34, "flags": 4}, {"matrix": [3, 1], "x": 30, "y": 34, "flags": 4}, {"matrix": [3, 2], "x": 44, "y": 34, "flags": 4}, {"matrix": [3, 3], "x": 58, "y": 34, "flags": 4}, {"matrix": [3, 4], "x": 71, "y": 34, "flags": 4}, {"matrix": [3, 5], "x": 85, "y": 34, "flags": 4}, {"matrix": [3, 6], "x": 99, "y": 34, "flags": 4}, {"matrix": [3, 7], "x": 112, "y": 34, "flags": 4}, {"matrix": [3, 8], "x": 126, "y": 34, "flags": 4}, {"matrix": [3, 9], "x": 140, "y": 34, "flags": 4}, {"matrix": [3, 10], "x": 153, "y": 34, "flags": 4}, {"matrix": [3, 11], "x": 167, "y": 34, "flags": 4}, {"x": 180, "y": 34, "flags": 4}, {"matrix": [3, 13], "x": 189, "y": 34, "flags": 4}, {"matrix": [3, 14], "x": 217, "y": 34, "flags": 4}, {"matrix": [4, 0], "x": 8, "y": 44, "flags": 4}, {"x": 24, "y": 44, "flags": 4}, {"matrix": [4, 2], "x": 38, "y": 44, "flags": 4}, {"matrix": [4, 3], "x": 51, "y": 44, "flags": 4}, {"matrix": [4, 4], "x": 65, "y": 44, "flags": 4}, {"matrix": [4, 5], "x": 78, "y": 44, "flags": 4}, {"matrix": [4, 6], "x": 92, "y": 44, "flags": 4}, {"matrix": [4, 7], "x": 106, "y": 44, "flags": 4}, {"matrix": [4, 8], "x": 119, "y": 44, "flags": 4}, {"matrix": [4, 9], "x": 133, "y": 44, "flags": 4}, {"matrix": [4, 10], "x": 147, "y": 44, "flags": 4}, {"matrix": [4, 11], "x": 160, "y": 44, "flags": 4}, {"matrix": [4, 12], "x": 179, "y": 44, "flags": 4}, {"matrix": [4, 13], "x": 201, "y": 46, "flags": 4}, {"matrix": [4, 14], "x": 217, "y": 44, "flags": 4}, {"matrix": [5, 0], "x": 8, "y": 54, "flags": 4}, {"matrix": [5, 1], "x": 25, "y": 54, "flags": 4}, {"matrix": [5, 2], "x": 43, "y": 54, "flags": 4}, {"x": 67, "y": 57, "flags": 4}, {"x": 80, "y": 57, "flags": 4}, {"x": 94, "y": 54, "flags": 4}, {"matrix": [5, 6], "x": 107, "y": 57, "flags": 4}, {"x": 120, "y": 57, "flags": 4}, {"matrix": [5, 9], "x": 143, "y": 54, "flags": 4}, {"matrix": [5, 10], "x": 157, "y": 54, "flags": 4}, {"matrix": [5, 11], "x": 170, "y": 54, "flags": 4}, {"matrix": [5, 12], "x": 187, "y": 56, "flags": 4}, {"matrix": [5, 13], "x": 201, "y": 56, "flags": 4}, {"matrix": [5, 14], "x": 214, "y": 56, "flags": 4}, {"x": 207, "y": 23, "flags": 8}, {"x": 207, "y": 27, "flags": 8}]}, "usb": {"device_version": "0.0.1", "pid": "0x4321", "vid": "0x7654"}, "layout_aliases": {"LAYOUT_75_ansi": "LAYOUT"}, "layouts": {"LAYOUT": {"layout": [{"label": "ESC", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [0, 2], "x": 1.5, "y": 0}, {"label": "F2", "matrix": [0, 3], "x": 2.5, "y": 0}, {"label": "F3", "matrix": [0, 4], "x": 3.5, "y": 0}, {"label": "F4", "matrix": [0, 5], "x": 4.5, "y": 0}, {"label": "F5", "matrix": [0, 6], "x": 6, "y": 0}, {"label": "F6", "matrix": [0, 7], "x": 7, "y": 0}, {"label": "F7", "matrix": [0, 8], "x": 8, "y": 0}, {"label": "F8", "matrix": [0, 9], "x": 9, "y": 0}, {"label": "F9", "matrix": [0, 10], "x": 10.5, "y": 0}, {"label": "F10", "matrix": [0, 11], "x": 11.5, "y": 0}, {"label": "F11", "matrix": [0, 12], "x": 12.5, "y": 0}, {"label": "F12", "matrix": [0, 13], "x": 13.5, "y": 0}, {"label": "END", "matrix": [0, 15], "x": 15, "y": 0}, {"label": "GRV", "matrix": [1, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [1, 1], "x": 1, "y": 1.25}, {"label": "2", "matrix": [1, 2], "x": 2, "y": 1.25}, {"label": "3", "matrix": [1, 3], "x": 3, "y": 1.25}, {"label": "4", "matrix": [1, 4], "x": 4, "y": 1.25}, {"label": "5", "matrix": [1, 5], "x": 5, "y": 1.25}, {"label": "6", "matrix": [1, 6], "x": 6, "y": 1.25}, {"label": "7", "matrix": [1, 7], "x": 7, "y": 1.25}, {"label": "8", "matrix": [1, 8], "x": 8, "y": 1.25}, {"label": "9", "matrix": [1, 9], "x": 9, "y": 1.25}, {"label": "0", "matrix": [1, 10], "x": 10, "y": 1.25}, {"label": "MINS", "matrix": [1, 11], "x": 11, "y": 1.25}, {"label": "EQL", "matrix": [1, 12], "x": 12, "y": 1.25}, {"label": "BSPC", "matrix": [1, 13], "x": 13, "y": 1.25, "w": 2}, {"label": "HOME", "matrix": [1, 14], "x": 15, "y": 1.25}, {"label": "TAB", "matrix": [2, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [2, 1], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [2, 2], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [2, 3], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [2, 4], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [2, 5], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [2, 6], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [2, 7], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [2, 8], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [2, 9], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [2, 10], "x": 10.5, "y": 2.25}, {"label": "LBRC", "matrix": [2, 11], "x": 11.5, "y": 2.25}, {"label": "RBRC", "matrix": [2, 12], "x": 12.5, "y": 2.25}, {"label": "BSLS", "matrix": [2, 13], "x": 13.5, "y": 2.25, "w": 1.5}, {"label": "DEL", "matrix": [2, 14], "x": 15, "y": 2.25}, {"label": "CAPS", "matrix": [3, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [3, 1], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [3, 2], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [3, 3], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [3, 4], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [3, 5], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [3, 6], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [3, 7], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [3, 8], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [3, 9], "x": 9.75, "y": 3.25}, {"label": "SCLN", "matrix": [3, 10], "x": 10.75, "y": 3.25}, {"label": "QUOT", "matrix": [3, 11], "x": 11.75, "y": 3.25}, {"label": "ENT", "matrix": [3, 13], "x": 12.75, "y": 3.25, "w": 2.25}, {"label": "PGUP", "matrix": [3, 14], "x": 15, "y": 3.25}, {"label": "LSFT", "matrix": [4, 0], "x": 0, "y": 4.25, "w": 2.25}, {"label": "Z", "matrix": [4, 2], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [4, 3], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [4, 4], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [4, 5], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [4, 6], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [4, 7], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [4, 8], "x": 8.25, "y": 4.25}, {"label": "COMM", "matrix": [4, 9], "x": 9.25, "y": 4.25}, {"label": "DOT", "matrix": [4, 10], "x": 10.25, "y": 4.25}, {"label": "SLSH", "matrix": [4, 11], "x": 11.25, "y": 4.25}, {"label": "RSFT", "matrix": [4, 12], "x": 12.25, "y": 4.25, "w": 1.75}, {"label": "UP", "matrix": [4, 13], "x": 14, "y": 4.25}, {"label": "PGDN", "matrix": [4, 14], "x": 15, "y": 4.25}, {"label": "LCTL", "matrix": [5, 0], "x": 0, "y": 5.25, "w": 1.25}, {"label": "LGUI", "matrix": [5, 1], "x": 1.25, "y": 5.25, "w": 1.25}, {"label": "LALT", "matrix": [5, 2], "x": 2.5, "y": 5.25, "w": 1.25}, {"label": "SPC", "matrix": [5, 6], "x": 3.75, "y": 5.25, "w": 6.25}, {"label": "RALT", "matrix": [5, 9], "x": 10, "y": 5.25}, {"label": "MO(1)", "matrix": [5, 10], "x": 11, "y": 5.25}, {"label": "RCTL", "matrix": [5, 11], "x": 12, "y": 5.25}, {"label": "LEFT", "matrix": [5, 12], "x": 13, "y": 5.25}, {"label": "DOWN", "matrix": [5, 13], "x": 14, "y": 5.25}, {"label": "RGHT", "matrix": [5, 14], "x": 15, "y": 5.25}]}}}