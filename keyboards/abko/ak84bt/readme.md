# AK84BT

![ak84bt](https://i.imgur.com/tQJ19vxh.jpg)

A customizable 80% keyboard.

- Keyboard Maintainer: [temp4gh](https://github.com/temp4gh)
- Hardware Supported: AK84BT PCB
- Hardware Availability: www.abko.co.kr

Make example for this keyboard (after setting up your build environment):

    make abko/ak84bt:default

Flashing example for this keyboard:

    make abko/ak84bt:default:flash

**Reset Key**: Hold down the key located at *K10*, commonly programmed as *Grave* while plugging in the keyboard.

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
