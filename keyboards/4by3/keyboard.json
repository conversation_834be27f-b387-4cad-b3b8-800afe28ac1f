{"keyboard_name": "4by3", "manufacturer": "<PERSON>", "url": "https://github.com/eliassjogreen/4by3", "maintainer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usb": {"vid": "0xEEEE", "pid": "0x2019", "device_version": "0.0.1"}, "features": {"bootmagic": false, "command": true, "console": true, "extrakey": true, "mousekey": false, "nkro": true}, "matrix_pins": {"cols": ["C6", "D7", "E6", "B4"], "rows": ["D1", "D0", "D4"]}, "diode_direction": "COL2ROW", "development_board": "promicro", "layout_aliases": {"LAYOUT": "LAYOUT_horizontal"}, "layouts": {"LAYOUT_horizontal": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2}]}, "LAYOUT_vertical_right": {"layout": [{"matrix": [2, 0], "x": 0, "y": 0}, {"matrix": [1, 0], "x": 1, "y": 0}, {"matrix": [0, 0], "x": 2, "y": 0}, {"matrix": [2, 1], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [0, 1], "x": 2, "y": 1}, {"matrix": [2, 2], "x": 0, "y": 2}, {"matrix": [1, 2], "x": 1, "y": 2}, {"matrix": [0, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 0, "y": 3}, {"matrix": [1, 3], "x": 1, "y": 3}, {"matrix": [0, 3], "x": 2, "y": 3}]}, "LAYOUT_vertical_left": {"layout": [{"matrix": [0, 3], "x": 0, "y": 0}, {"matrix": [1, 3], "x": 1, "y": 0}, {"matrix": [2, 3], "x": 2, "y": 0}, {"matrix": [0, 2], "x": 0, "y": 1}, {"matrix": [1, 2], "x": 1, "y": 1}, {"matrix": [2, 2], "x": 2, "y": 1}, {"matrix": [0, 1], "x": 0, "y": 2}, {"matrix": [1, 1], "x": 1, "y": 2}, {"matrix": [2, 1], "x": 2, "y": 2}, {"matrix": [0, 0], "x": 0, "y": 3}, {"matrix": [1, 0], "x": 1, "y": 3}, {"matrix": [2, 0], "x": 2, "y": 3}]}}}