{"keyboard_name": "8-Pack", "manufacturer": "<PERSON>", "url": "https://github.com/cgarcia2097/8-Pack", "maintainer": "<PERSON>", "usb": {"vid": "0xFEED", "pid": "0x2171"}, "qmk": {"locking": {"enabled": true, "resync": true}}, "backlight": {"driver": "timer", "pins": ["D1", "D0", "D4", "C6", "D7", "E6", "B4", "B5"], "levels": 8}, "rgblight": {"led_count": 8, "animations": {"breathing": true, "rainbow_mood": true, "rainbow_swirl": true, "snake": true, "knight": true, "christmas": true, "static_gradient": true, "rgb_test": true, "alternating": true, "twinkle": true}}, "ws2812": {"pin": "D2"}, "development_board": "promicro", "features": {"backlight": true, "bootmagic": false, "command": true, "console": true, "extrakey": false, "mousekey": false, "nkro": false, "rgblight": true}, "matrix_pins": {"direct": [["F4", "F5", "F6", "F7"], ["B1", "B3", "B2", "B6"]]}}