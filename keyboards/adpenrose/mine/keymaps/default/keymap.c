// Copyright 2022 <PERSON> (@<PERSON>)
// SPDX-License-Identifier: GPL-2.0-or-later

#include QM<PERSON>_KEYBOARD_H

const uint16_t PROGMEM keymaps[][MATRIX_ROWS][MATRIX_COLS] = {
    [0] = LAYOUT_1800_alice_ansi(
                                                                                                                                                                                              KC_MUTE,
        KC_ESC,    KC_F1,   KC_F2,    KC_F3,    KC_F4,   KC_F5,   KC_F6,       KC_F7,   KC_F8,   KC_F9,    <PERSON>_F10,   KC_F11,   KC_F12,   KC_HOME,   KC_END,         KC_NUM, KC_PSLS, KC_PAST, KC_PMNS, 
        KC_GRV,    KC_1,    KC_2,     KC_3,     KC_4,    <PERSON>_5,    <PERSON>_6,        <PERSON>_7,    <PERSON>_8,    <PERSON>_9,     <PERSON>_<PERSON>,     <PERSON><PERSON><PERSON>NS,  KC_EQL,   KC_BSPC,                   KC_P7,  KC_<PERSON>8,   KC_<PERSON>9,   <PERSON>_<PERSON>LS,
        <PERSON>_TAB,    KC_Q,    <PERSON>_<PERSON>,     <PERSON><PERSON><PERSON>,     <PERSON><PERSON><PERSON>,    <PERSON><PERSON><PERSON>,                 <PERSON><PERSON><PERSON>,    <PERSON><PERSON><PERSON>,    <PERSON><PERSON><PERSON>,     <PERSON><PERSON><PERSON>,     <PERSON><PERSON><PERSON>,     <PERSON><PERSON>,  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,          <PERSON><PERSON><PERSON><PERSON>,  <PERSON><PERSON><PERSON><PERSON>,   <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>,   <PERSON><PERSON><PERSON>,    <PERSON><PERSON><PERSON>,     <PERSON>_<PERSON>,     <PERSON><PERSON><PERSON>,    <PERSON><PERSON><PERSON>,                 <PERSON>_H,    KC_J,    KC_K,     KC_L,     KC_SCLN,  KC_QUOT,           KC_ENT,           KC_P1,  KC_P2,   KC_P3,   KC_PENT,
        KC_LSFT,   KC_Z,    KC_X,     KC_C,     KC_V,    KC_B,                 KC_B,    KC_N,    KC_M,     KC_COMM,  KC_DOT,   KC_SLSH,  KC_RSFT,           KC_UP,          KC_P0,   KC_PDOT, 
        KC_LCTL,   KC_LGUI,                     KC_LALT, KC_SPC,               KC_SPC,  KC_RALT, KC_RGUI,  KC_RCTL,                                KC_LEFT, KC_DOWN,  KC_RIGHT  
    ),

    [1] = LAYOUT_1800_alice_ansi(
                                                                                                                                                                                                                             KC_TRNS,
        QK_BOOT,   KC_TRNS,   KC_TRNS,    KC_TRNS,    KC_TRNS,   KC_TRNS,   KC_TRNS,       KC_TRNS,   KC_TRNS,   KC_TRNS,    KC_TRNS,   KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,              KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS, 
        KC_TRNS,   KC_TRNS,   KC_TRNS,    KC_TRNS,    KC_TRNS,   KC_TRNS,   KC_TRNS,       KC_TRNS,   KC_TRNS,   KC_TRNS,    KC_TRNS,   KC_TRNS,  KC_TRNS,   KC_TRNS,                        KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
        KC_TRNS,   KC_TRNS,   KC_TRNS,    KC_TRNS,    KC_TRNS,   KC_TRNS,                  KC_TRNS,   KC_TRNS,   KC_TRNS,    KC_TRNS,   KC_TRNS,  KC_TRNS,   KC_TRNS,  KC_TRNS,              KC_TRNS,  KC_TRNS,   KC_TRNS,
        KC_TRNS,   KC_TRNS,   KC_TRNS,    KC_TRNS,    KC_TRNS,   KC_TRNS,                  KC_TRNS,   KC_TRNS,   KC_TRNS,    KC_TRNS,   KC_TRNS,  KC_TRNS,             KC_TRNS,              KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
        KC_TRNS,   KC_TRNS,   KC_TRNS,    KC_TRNS,    KC_TRNS,   KC_TRNS,                  KC_TRNS,   KC_TRNS,   KC_TRNS,    KC_TRNS,   KC_TRNS,  KC_TRNS,   KC_TRNS,             KC_TRNS,             KC_TRNS,  KC_TRNS, 
        KC_TRNS,   KC_TRNS,                           KC_TRNS,   KC_TRNS,                  KC_TRNS,   KC_TRNS,   KC_TRNS,    KC_TRNS,                                    KC_TRNS, KC_TRNS,  KC_TRNS 
    )
};

#if defined(ENCODER_MAP_ENABLE)
const uint16_t PROGMEM encoder_map[][NUM_ENCODERS][NUM_DIRECTIONS] = {
    [0] =   { ENCODER_CCW_CW(KC_VOLD, KC_VOLU) },
    [1] =  { ENCODER_CCW_CW(KC_TRNS, KC_TRNS) }
};
#endif