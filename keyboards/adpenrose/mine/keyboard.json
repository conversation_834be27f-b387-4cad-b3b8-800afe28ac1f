{"manufacturer": "ADPenrose", "keyboard_name": "Mine", "maintainer": "<PERSON>", "usb": {"device_version": "1.0.0", "pid": "0x0006", "vid": "0x4450"}, "features": {"encoder": true, "bootmagic": true, "extrakey": true}, "build": {"lto": true}, "dynamic_keymap": {"layer_count": 3}, "encoder": {"rotary": [{"pin_a": "C4", "pin_b": "C3"}]}, "diode_direction": "COL2ROW", "matrix_pins": {"rows": ["A0", "A1", "A4", "A3", "C7", "B4", "B5", "B6", "A6", "A2", "A5"], "cols": ["B0", "B1", "B2", "B3", "D0", "D1", "D5", "D6", "C6", "C5"]}, "processor": "atmega32a", "bootloader": "usbasploader", "layouts": {"LAYOUT_all": {"layout": [{"label": "Enc", "matrix": [0, 9], "x": 20.75, "y": 0}, {"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [0, 1], "x": 1.25, "y": 0}, {"label": "F2", "matrix": [1, 1], "x": 2.25, "y": 0}, {"label": "F3", "matrix": [0, 2], "x": 3.5, "y": 0}, {"label": "F4", "matrix": [1, 2], "x": 4.5, "y": 0}, {"label": "F5", "matrix": [0, 3], "x": 5.75, "y": 0}, {"label": "F6", "matrix": [1, 3], "x": 6.75, "y": 0}, {"label": "F7", "matrix": [9, 3], "x": 9.25, "y": 0}, {"label": "F8", "matrix": [10, 3], "x": 10.25, "y": 0}, {"label": "F9", "matrix": [0, 4], "x": 11.5, "y": 0}, {"label": "F10", "matrix": [0, 5], "x": 12.5, "y": 0}, {"label": "F11", "matrix": [0, 6], "x": 13.75, "y": 0}, {"label": "F12", "matrix": [1, 6], "x": 14.75, "y": 0}, {"label": "M1", "matrix": [10, 6], "x": 16, "y": 0}, {"label": "M2", "matrix": [0, 7], "x": 17, "y": 0}, {"label": "Num Lock", "matrix": [1, 7], "x": 17.75, "y": 1.25}, {"label": "/", "matrix": [0, 8], "x": 18.75, "y": 1.25}, {"label": "*", "matrix": [10, 8], "x": 19.75, "y": 1.25}, {"label": "-", "matrix": [1, 9], "x": 20.75, "y": 1.25}, {"label": "~", "matrix": [1, 0], "x": 0, "y": 1.25}, {"label": "!", "matrix": [10, 0], "x": 1, "y": 1.25}, {"label": "@", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "#", "matrix": [10, 1], "x": 3, "y": 1.25}, {"label": "$", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "%", "matrix": [10, 2], "x": 5, "y": 1.25}, {"label": "^", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "&", "matrix": [8, 3], "x": 9.25, "y": 1.25}, {"label": "*", "matrix": [1, 4], "x": 10.25, "y": 1.25}, {"label": "(", "matrix": [10, 4], "x": 11.25, "y": 1.25}, {"label": ")", "matrix": [1, 5], "x": 12.25, "y": 1.25}, {"label": "_", "matrix": [10, 5], "x": 13.25, "y": 1.25}, {"label": "+", "matrix": [2, 6], "x": 14.25, "y": 1.25}, {"label": "Backspace", "matrix": [9, 6], "w": 2, "x": 15.25, "y": 1.25}, {"label": "7", "matrix": [2, 7], "x": 17.75, "y": 2.25}, {"label": "8", "matrix": [1, 8], "x": 18.75, "y": 2.25}, {"label": "9", "matrix": [9, 8], "x": 19.75, "y": 2.25}, {"h": 2, "label": "+", "matrix": [8, 8], "x": 20.75, "y": 2.25}, {"label": "Tab", "matrix": [2, 0], "w": 1.5, "x": 0, "y": 2.25}, {"label": "Q", "matrix": [9, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [3, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [9, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [3, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [9, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [7, 3], "x": 8.75, "y": 2.25}, {"label": "U", "matrix": [2, 4], "x": 9.75, "y": 2.25}, {"label": "I", "matrix": [9, 4], "x": 10.75, "y": 2.25}, {"label": "O", "matrix": [2, 5], "x": 11.75, "y": 2.25}, {"label": "P", "matrix": [9, 5], "x": 12.75, "y": 2.25}, {"label": "{", "matrix": [3, 6], "x": 13.75, "y": 2.25}, {"label": "}", "matrix": [8, 6], "x": 14.75, "y": 2.25}, {"label": "|", "matrix": [7, 7], "w": 1.5, "x": 15.75, "y": 2.25}, {"label": "4", "matrix": [3, 7], "x": 17.75, "y": 3.25}, {"label": "5", "matrix": [2, 8], "x": 18.75, "y": 3.25}, {"label": "6", "matrix": [7, 8], "x": 19.75, "y": 3.25}, {"label": "Caps Lock", "matrix": [3, 0], "w": 1.75, "x": 0, "y": 3.25}, {"label": "A", "matrix": [8, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [4, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [8, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [4, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [8, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 9, "y": 3.25}, {"label": "J", "matrix": [3, 4], "x": 10, "y": 3.25}, {"label": "K", "matrix": [8, 4], "x": 11, "y": 3.25}, {"label": "L", "matrix": [3, 5], "x": 12, "y": 3.25}, {"label": ":", "matrix": [8, 5], "x": 13, "y": 3.25}, {"label": "\\", "matrix": [4, 6], "x": 14, "y": 3.25}, {"label": "#", "matrix": [6, 6], "x": 15, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "w": 2.25, "x": 15, "y": 3.25}, {"label": "1", "matrix": [4, 7], "x": 17.75, "y": 4.25}, {"label": "2", "matrix": [3, 8], "x": 18.75, "y": 4.25}, {"label": "3", "matrix": [6, 8], "x": 19.75, "y": 4.25}, {"h": 2, "label": "Enter", "matrix": [5, 8], "x": 20.75, "y": 4.25}, {"label": "Shift", "matrix": [4, 0], "w": 1.25, "x": 0, "y": 4.25}, {"label": "~", "matrix": [7, 0], "x": 1.25, "y": 4.25}, {"label": "Z", "matrix": [5, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [7, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [5, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [7, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [3, 3], "x": 6.25, "y": 4.25}, {"label": "B", "matrix": [5, 3], "x": 8.5, "y": 4.25}, {"label": "N", "matrix": [4, 4], "x": 9.5, "y": 4.25}, {"label": "M", "matrix": [5, 4], "x": 10.5, "y": 4.25}, {"label": "<", "matrix": [7, 4], "x": 11.5, "y": 4.25}, {"label": ">", "matrix": [4, 5], "x": 12.5, "y": 4.25}, {"label": "?", "matrix": [7, 5], "x": 13.5, "y": 4.25}, {"label": "Shift", "matrix": [5, 6], "w": 2.75, "x": 14.5, "y": 4.25}, {"label": "Up", "matrix": [5, 7], "x": 17.5, "y": 5.5}, {"label": "0", "matrix": [10, 7], "x": 18.75, "y": 5.25}, {"label": ".", "matrix": [4, 8], "x": 19.75, "y": 5.25}, {"label": "LCtrl", "matrix": [5, 0], "w": 1.25, "x": 0, "y": 5.25}, {"label": "LWin", "matrix": [6, 0], "w": 1.25, "x": 1.25, "y": 5.25}, {"label": "LAlt", "matrix": [6, 1], "w": 1.25, "x": 3.25, "y": 5.25}, {"label": "LSpacebar", "matrix": [6, 2], "w": 2.75, "x": 4.5, "y": 5.25}, {"label": "RSpacebar", "matrix": [4, 3], "w": 2.25, "x": 8.5, "y": 5.25}, {"label": "RAlt", "matrix": [6, 4], "w": 1.25, "x": 10.75, "y": 5.25}, {"label": "RWin", "matrix": [5, 5], "w": 1.25, "x": 14.75, "y": 5.25}, {"label": "RCtrl", "matrix": [6, 5], "w": 1.25, "x": 16, "y": 5.25}, {"label": "Left", "matrix": [6, 7], "x": 16.5, "y": 6.5}, {"label": "Down", "matrix": [8, 7], "x": 17.5, "y": 6.5}, {"label": "Right", "matrix": [9, 7], "x": 18.5, "y": 6.5}]}, "LAYOUT_1800_alice_ansi": {"layout": [{"label": "Enc", "matrix": [0, 9], "x": 20.75, "y": 0}, {"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [0, 1], "x": 1.25, "y": 0}, {"label": "F2", "matrix": [1, 1], "x": 2.25, "y": 0}, {"label": "F3", "matrix": [0, 2], "x": 3.5, "y": 0}, {"label": "F4", "matrix": [1, 2], "x": 4.5, "y": 0}, {"label": "F5", "matrix": [0, 3], "x": 5.75, "y": 0}, {"label": "F6", "matrix": [1, 3], "x": 6.75, "y": 0}, {"label": "F7", "matrix": [9, 3], "x": 9.25, "y": 0}, {"label": "F8", "matrix": [10, 3], "x": 10.25, "y": 0}, {"label": "F9", "matrix": [0, 4], "x": 11.5, "y": 0}, {"label": "F10", "matrix": [0, 5], "x": 12.5, "y": 0}, {"label": "F11", "matrix": [0, 6], "x": 13.75, "y": 0}, {"label": "F12", "matrix": [1, 6], "x": 14.75, "y": 0}, {"label": "M1", "matrix": [10, 6], "x": 16, "y": 0}, {"label": "M2", "matrix": [0, 7], "x": 17, "y": 0}, {"label": "Num Lock", "matrix": [1, 7], "x": 17.75, "y": 1.25}, {"label": "/", "matrix": [0, 8], "x": 18.75, "y": 1.25}, {"label": "*", "matrix": [10, 8], "x": 19.75, "y": 1.25}, {"label": "-", "matrix": [1, 9], "x": 20.75, "y": 1.25}, {"label": "~", "matrix": [1, 0], "x": 0, "y": 1.25}, {"label": "!", "matrix": [10, 0], "x": 1, "y": 1.25}, {"label": "@", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "#", "matrix": [10, 1], "x": 3, "y": 1.25}, {"label": "$", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "%", "matrix": [10, 2], "x": 5, "y": 1.25}, {"label": "^", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "&", "matrix": [8, 3], "x": 9.25, "y": 1.25}, {"label": "*", "matrix": [1, 4], "x": 10.25, "y": 1.25}, {"label": "(", "matrix": [10, 4], "x": 11.25, "y": 1.25}, {"label": ")", "matrix": [1, 5], "x": 12.25, "y": 1.25}, {"label": "_", "matrix": [10, 5], "x": 13.25, "y": 1.25}, {"label": "+", "matrix": [2, 6], "x": 14.25, "y": 1.25}, {"label": "Backspace", "matrix": [9, 6], "w": 2, "x": 15.25, "y": 1.25}, {"label": "7", "matrix": [2, 7], "x": 17.75, "y": 2.25}, {"label": "8", "matrix": [1, 8], "x": 18.75, "y": 2.25}, {"label": "9", "matrix": [9, 8], "x": 19.75, "y": 2.25}, {"h": 2, "label": "+", "matrix": [8, 8], "x": 20.75, "y": 2.25}, {"label": "Tab", "matrix": [2, 0], "w": 1.5, "x": 0, "y": 2.25}, {"label": "Q", "matrix": [9, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [3, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [9, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [3, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [9, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [7, 3], "x": 8.75, "y": 2.25}, {"label": "U", "matrix": [2, 4], "x": 9.75, "y": 2.25}, {"label": "I", "matrix": [9, 4], "x": 10.75, "y": 2.25}, {"label": "O", "matrix": [2, 5], "x": 11.75, "y": 2.25}, {"label": "P", "matrix": [9, 5], "x": 12.75, "y": 2.25}, {"label": "{", "matrix": [3, 6], "x": 13.75, "y": 2.25}, {"label": "}", "matrix": [8, 6], "x": 14.75, "y": 2.25}, {"label": "|", "matrix": [7, 7], "w": 1.5, "x": 15.75, "y": 2.25}, {"label": "4", "matrix": [3, 7], "x": 17.75, "y": 3.25}, {"label": "5", "matrix": [2, 8], "x": 18.75, "y": 3.25}, {"label": "6", "matrix": [7, 8], "x": 19.75, "y": 3.25}, {"label": "Caps Lock", "matrix": [3, 0], "w": 1.75, "x": 0, "y": 3.25}, {"label": "A", "matrix": [8, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [4, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [8, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [4, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [8, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 9, "y": 3.25}, {"label": "J", "matrix": [3, 4], "x": 10, "y": 3.25}, {"label": "K", "matrix": [8, 4], "x": 11, "y": 3.25}, {"label": "L", "matrix": [3, 5], "x": 12, "y": 3.25}, {"label": ":", "matrix": [8, 5], "x": 13, "y": 3.25}, {"label": "\\", "matrix": [4, 6], "x": 14, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "w": 2.25, "x": 15, "y": 3.25}, {"label": "1", "matrix": [4, 7], "x": 17.75, "y": 4.25}, {"label": "2", "matrix": [3, 8], "x": 18.75, "y": 4.25}, {"label": "3", "matrix": [6, 8], "x": 19.75, "y": 4.25}, {"h": 2, "label": "Enter", "matrix": [5, 8], "x": 20.75, "y": 4.25}, {"label": "Shift", "matrix": [4, 0], "w": 2.25, "x": 0, "y": 4.25}, {"label": "Z", "matrix": [5, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [7, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [5, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [7, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [3, 3], "x": 6.25, "y": 4.25}, {"label": "B", "matrix": [5, 3], "x": 8.5, "y": 4.25}, {"label": "N", "matrix": [4, 4], "x": 9.5, "y": 4.25}, {"label": "M", "matrix": [5, 4], "x": 10.5, "y": 4.25}, {"label": "<", "matrix": [7, 4], "x": 11.5, "y": 4.25}, {"label": ">", "matrix": [4, 5], "x": 12.5, "y": 4.25}, {"label": "?", "matrix": [7, 5], "x": 13.5, "y": 4.25}, {"label": "Shift", "matrix": [5, 6], "w": 2.75, "x": 14.5, "y": 4.25}, {"label": "Up", "matrix": [5, 7], "x": 17.5, "y": 5.5}, {"label": "0", "matrix": [10, 7], "x": 18.75, "y": 5.25}, {"label": ".", "matrix": [4, 8], "x": 19.75, "y": 5.25}, {"label": "LCtrl", "matrix": [5, 0], "w": 1.25, "x": 0, "y": 5.25}, {"label": "LWin", "matrix": [6, 0], "w": 1.25, "x": 1.25, "y": 5.25}, {"label": "LAlt", "matrix": [6, 1], "w": 1.25, "x": 3.25, "y": 5.25}, {"label": "LSpacebar", "matrix": [6, 2], "w": 2.75, "x": 4.5, "y": 5.25}, {"label": "RSpacebar", "matrix": [4, 3], "w": 2.25, "x": 8.5, "y": 5.25}, {"label": "RAlt", "matrix": [6, 4], "w": 1.25, "x": 10.75, "y": 5.25}, {"label": "RWin", "matrix": [5, 5], "w": 1.25, "x": 14.75, "y": 5.25}, {"label": "RCtrl", "matrix": [6, 5], "w": 1.25, "x": 16, "y": 5.25}, {"label": "Left", "matrix": [6, 7], "x": 16.5, "y": 6.5}, {"label": "Down", "matrix": [8, 7], "x": 17.5, "y": 6.5}, {"label": "Right", "matrix": [9, 7], "x": 18.5, "y": 6.5}]}, "LAYOUT_1800_alice_iso": {"layout": [{"label": "Enc", "matrix": [0, 9], "x": 20.75, "y": 0}, {"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [0, 1], "x": 1.25, "y": 0}, {"label": "F2", "matrix": [1, 1], "x": 2.25, "y": 0}, {"label": "F3", "matrix": [0, 2], "x": 3.5, "y": 0}, {"label": "F4", "matrix": [1, 2], "x": 4.5, "y": 0}, {"label": "F5", "matrix": [0, 3], "x": 5.75, "y": 0}, {"label": "F6", "matrix": [1, 3], "x": 6.75, "y": 0}, {"label": "F7", "matrix": [9, 3], "x": 9.25, "y": 0}, {"label": "F8", "matrix": [10, 3], "x": 10.25, "y": 0}, {"label": "F9", "matrix": [0, 4], "x": 11.5, "y": 0}, {"label": "F10", "matrix": [0, 5], "x": 12.5, "y": 0}, {"label": "F11", "matrix": [0, 6], "x": 13.75, "y": 0}, {"label": "F12", "matrix": [1, 6], "x": 14.75, "y": 0}, {"label": "M1", "matrix": [10, 6], "x": 16, "y": 0}, {"label": "M2", "matrix": [0, 7], "x": 17, "y": 0}, {"label": "Num Lock", "matrix": [1, 7], "x": 17.75, "y": 1.25}, {"label": "/", "matrix": [0, 8], "x": 18.75, "y": 1.25}, {"label": "*", "matrix": [10, 8], "x": 19.75, "y": 1.25}, {"label": "-", "matrix": [1, 9], "x": 20.75, "y": 1.25}, {"label": "~", "matrix": [1, 0], "x": 0, "y": 1.25}, {"label": "!", "matrix": [10, 0], "x": 1, "y": 1.25}, {"label": "@", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "#", "matrix": [10, 1], "x": 3, "y": 1.25}, {"label": "$", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "%", "matrix": [10, 2], "x": 5, "y": 1.25}, {"label": "^", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "&", "matrix": [8, 3], "x": 9.25, "y": 1.25}, {"label": "*", "matrix": [1, 4], "x": 10.25, "y": 1.25}, {"label": "(", "matrix": [10, 4], "x": 11.25, "y": 1.25}, {"label": ")", "matrix": [1, 5], "x": 12.25, "y": 1.25}, {"label": "_", "matrix": [10, 5], "x": 13.25, "y": 1.25}, {"label": "+", "matrix": [2, 6], "x": 14.25, "y": 1.25}, {"label": "Backspace", "matrix": [9, 6], "w": 2, "x": 15.25, "y": 1.25}, {"label": "7", "matrix": [2, 7], "x": 17.75, "y": 2.25}, {"label": "8", "matrix": [1, 8], "x": 18.75, "y": 2.25}, {"label": "9", "matrix": [9, 8], "x": 19.75, "y": 2.25}, {"h": 2, "label": "+", "matrix": [8, 8], "x": 20.75, "y": 2.25}, {"label": "Tab", "matrix": [2, 0], "w": 1.5, "x": 0, "y": 2.25}, {"label": "Q", "matrix": [9, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [3, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [9, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [3, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [9, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [7, 3], "x": 8.75, "y": 2.25}, {"label": "U", "matrix": [2, 4], "x": 9.75, "y": 2.25}, {"label": "I", "matrix": [9, 4], "x": 10.75, "y": 2.25}, {"label": "O", "matrix": [2, 5], "x": 11.75, "y": 2.25}, {"label": "P", "matrix": [9, 5], "x": 12.75, "y": 2.25}, {"label": "{", "matrix": [3, 6], "x": 13.75, "y": 2.25}, {"label": "}", "matrix": [8, 6], "x": 14.75, "y": 2.25}, {"label": "4", "matrix": [3, 7], "x": 17.75, "y": 3.25}, {"label": "5", "matrix": [2, 8], "x": 18.75, "y": 3.25}, {"label": "6", "matrix": [7, 8], "x": 19.75, "y": 3.25}, {"label": "Caps Lock", "matrix": [3, 0], "w": 1.75, "x": 0, "y": 3.25}, {"label": "A", "matrix": [8, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [4, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [8, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [4, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [8, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 9, "y": 3.25}, {"label": "J", "matrix": [3, 4], "x": 10, "y": 3.25}, {"label": "K", "matrix": [8, 4], "x": 11, "y": 3.25}, {"label": "L", "matrix": [3, 5], "x": 12, "y": 3.25}, {"label": ":", "matrix": [8, 5], "x": 13, "y": 3.25}, {"label": "\\", "matrix": [4, 6], "x": 14, "y": 3.25}, {"label": "#", "matrix": [6, 6], "x": 15, "y": 3.25}, {"h": 2, "label": "Enter", "matrix": [7, 6], "w": 1.25, "x": 16, "y": 2.25}, {"label": "1", "matrix": [4, 7], "x": 17.75, "y": 4.25}, {"label": "2", "matrix": [3, 8], "x": 18.75, "y": 4.25}, {"label": "3", "matrix": [6, 8], "x": 19.75, "y": 4.25}, {"h": 2, "label": "Enter", "matrix": [5, 8], "x": 20.75, "y": 4.25}, {"label": "Shift", "matrix": [4, 0], "w": 1.25, "x": 0, "y": 4.25}, {"label": "~", "matrix": [7, 0], "x": 1.25, "y": 4.25}, {"label": "Z", "matrix": [5, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [7, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [5, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [7, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [3, 3], "x": 6.25, "y": 4.25}, {"label": "B", "matrix": [5, 3], "x": 8.5, "y": 4.25}, {"label": "N", "matrix": [4, 4], "x": 9.5, "y": 4.25}, {"label": "M", "matrix": [5, 4], "x": 10.5, "y": 4.25}, {"label": "<", "matrix": [7, 4], "x": 11.5, "y": 4.25}, {"label": ">", "matrix": [4, 5], "x": 12.5, "y": 4.25}, {"label": "?", "matrix": [7, 5], "x": 13.5, "y": 4.25}, {"label": "Shift", "matrix": [5, 6], "w": 2.75, "x": 14.5, "y": 4.25}, {"label": "Up", "matrix": [5, 7], "x": 17.5, "y": 5.5}, {"label": "0", "matrix": [10, 7], "x": 18.75, "y": 5.25}, {"label": ".", "matrix": [4, 8], "x": 19.75, "y": 5.25}, {"label": "LCtrl", "matrix": [5, 0], "w": 1.25, "x": 0, "y": 5.25}, {"label": "LWin", "matrix": [6, 0], "w": 1.25, "x": 1.25, "y": 5.25}, {"label": "LAlt", "matrix": [6, 1], "w": 1.25, "x": 3.25, "y": 5.25}, {"label": "LSpacebar", "matrix": [6, 2], "w": 2.75, "x": 4.5, "y": 5.25}, {"label": "RSpacebar", "matrix": [4, 3], "w": 2.25, "x": 8.5, "y": 5.25}, {"label": "RAlt", "matrix": [6, 4], "w": 1.25, "x": 10.75, "y": 5.25}, {"label": "RWin", "matrix": [5, 5], "w": 1.25, "x": 14.75, "y": 5.25}, {"label": "RCtrl", "matrix": [6, 5], "w": 1.25, "x": 16, "y": 5.25}, {"label": "Left", "matrix": [6, 7], "x": 16.5, "y": 6.5}, {"label": "Down", "matrix": [8, 7], "x": 17.5, "y": 6.5}, {"label": "Right", "matrix": [9, 7], "x": 18.5, "y": 6.5}]}}}