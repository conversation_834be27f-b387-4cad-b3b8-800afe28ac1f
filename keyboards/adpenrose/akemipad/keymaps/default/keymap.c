// Copyright 2022 <PERSON> (@<PERSON><PERSON><PERSON><PERSON>)
// SPDX-License-Identifier: GPL-2.0-or-later

#include QMK_KEYBOARD_H

/* Keymap */
const uint16_t PROGMEM keymaps[][MATRIX_ROWS][MATRIX_COLS] = {
    [0] = LAYOUT_all(
                  <PERSON>O(1),    <PERSON><PERSON>(2),     <PERSON><PERSON>(3),     KC_<PERSON>L,
                  KC_NUM,   KC_<PERSON>LS,   KC_PAST,   KC_PMNS,
                  KC_P7,    KC_P8,     <PERSON>_<PERSON>9,     <PERSON>_<PERSON><PERSON>,
                  KC_P4,    KC_P5,     <PERSON>_P6,     <PERSON>_<PERSON><PERSON>,
                  KC_P1,    KC_P2,     KC_P3,     <PERSON>_PENT,
        KC_MUTE,  KC_P0,    KC_P0,     KC_PDOT,   KC_PENT 
    ),
    [1] = LAYOUT_all(
                  KC_TRNS,  CK_DOWN,   CK_UP,     CK_RST,
                  MU_TOGG,  <PERSON>U_NEXT,   <PERSON>_<PERSON><PERSON><PERSON>,   <PERSON>_<PERSON><PERSON><PERSON>,
                  <PERSON>_<PERSON><PERSON><PERSON>,  <PERSON>_<PERSON><PERSON><PERSON>,   <PERSON>_<PERSON><PERSON><PERSON>,   <PERSON><PERSON><PERSON><PERSON><PERSON>,
                  <PERSON>_<PERSON><PERSON><PERSON>,  <PERSON><PERSON><PERSON><PERSON><PERSON>,   <PERSON><PERSON><PERSON><PERSON><PERSON>,   <PERSON>_<PERSON><PERSON><PERSON>,
                  <PERSON><PERSON><PERSON><PERSON><PERSON>,  <PERSON><PERSON><PERSON><PERSON><PERSON>,   <PERSON><PERSON><PERSON><PERSON><PERSON>,   <PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON>_<PERSON>GG,  <PERSON><PERSON><PERSON><PERSON><PERSON>,  <PERSON>_<PERSON>RNS,   <PERSON>_TRNS,   KC_TRNS 
    ),
    [2] = LAYOUT_all(
                  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
                  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
                  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
                  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
                  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
        KC_TRNS,  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS 
    ),
    [3] = LAYOUT_all(
                  QK_BOOT,  KC_TRNS,   KC_TRNS,   KC_TRNS,
                  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
                  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
                  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
                  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
        KC_TRNS,  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS 
    )
};

#if defined(ENCODER_MAP_ENABLE)
const uint16_t PROGMEM encoder_map[][NUM_ENCODERS][NUM_DIRECTIONS] = {
    [0] = { ENCODER_CCW_CW(KC_VOLD, KC_VOLU) },
    [1] = { ENCODER_CCW_CW(RM_HUED, RM_HUEU) },
    [2] = { ENCODER_CCW_CW(RM_SATD, RM_SATU) },
    [3] = { ENCODER_CCW_CW(RM_VALD, RM_VALU) }
};
#endif
