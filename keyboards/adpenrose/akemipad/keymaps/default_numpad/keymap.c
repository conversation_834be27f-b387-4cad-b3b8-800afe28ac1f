// Copyright 2022 <PERSON> (@<PERSON><PERSON><PERSON><PERSON>)
// SPDX-License-Identifier: GPL-2.0-or-later

#include QMK_KEYBOARD_H

/* Keymap */
const uint16_t PROGMEM keymaps[][MATRIX_ROWS][MATRIX_COLS] = {
    [0] = LAYOUT_numpad(
                  MO(1),    <PERSON><PERSON>(2),     <PERSON><PERSON>(3),     KC_DEL,
                  KC_NUM,   KC_PSLS,   KC_PAST,   KC_PMNS,
                  KC_P7,    KC_P8,     KC_P9,     KC_PPLS,
                  KC_P4,    KC_P5,     KC_P6,
                  KC_P1,    KC_P2,     KC_P3,     KC_PENT,
        KC_MUTE,  KC_P0,               KC_PDOT
    ),
    [1] = LAYOUT_numpad(
                  KC_TRNS,  CK_DOWN,   CK_UP,     CK_RST,
                  MU_TOGG,  MU_NEXT,   KC_TRNS,   <PERSON>_<PERSON><PERSON><PERSON>,
                  <PERSON>_<PERSON><PERSON><PERSON>,  <PERSON>_<PERSON><PERSON><PERSON>,   <PERSON>_<PERSON><PERSON><PERSON>,   <PERSON>_<PERSON><PERSON><PERSON>,
                  <PERSON>_<PERSON><PERSON><PERSON>,  <PERSON>_<PERSON><PERSON><PERSON>,   <PERSON>_<PERSON><PERSON>NS,
                  <PERSON>_<PERSON><PERSON><PERSON>,  <PERSON><PERSON><PERSON><PERSON><PERSON>,   <PERSON>_<PERSON><PERSON><PERSON>,   <PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON>_TOGG,  <PERSON>_<PERSON><PERSON><PERSON>,             <PERSON>_<PERSON><PERSON><PERSON>
    ),
    [2] = LAYOUT_numpad(
                  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
                  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
                  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
                  KC_TRNS,  KC_TRNS,   KC_TRNS,
                  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
        KC_TRNS,  KC_TRNS,             KC_TRNS
    ),
    [3] = LAYOUT_numpad(
                  QK_BOOT,  KC_TRNS,   KC_TRNS,   KC_TRNS,
                  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
                  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
                  KC_TRNS,  KC_TRNS,   KC_TRNS,
                  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,
        KC_TRNS,  KC_TRNS,             KC_TRNS
    )
};

#if defined(ENCODER_MAP_ENABLE)
const uint16_t PROGMEM encoder_map[][NUM_ENCODERS][NUM_DIRECTIONS] = {
    [0] = { ENCODER_CCW_CW(KC_VOLD, KC_VOLU) },
    [1] = { ENCODER_CCW_CW(RM_HUED, RM_HUEU) },
    [2] = { ENCODER_CCW_CW(RM_SATD, RM_SATU) },
    [3] = { ENCODER_CCW_CW(RM_VALD, RM_VALU) }
};
#endif
