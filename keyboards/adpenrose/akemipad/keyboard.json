{"manufacturer": "ADPenrose", "keyboard_name": "AkemiPad", "maintainer": "<PERSON>", "usb": {"device_version": "1.0.0", "pid": "0x0004", "vid": "0x4450"}, "ws2812": {"pin": "F4"}, "rgb_matrix": {"animations": {"cycle_left_right": true, "cycle_up_down": true}, "center_point": [60, 77], "driver": "ws2812", "max_brightness": 175, "sleep": true}, "features": {"audio": true, "bootmagic": true, "encoder": true, "extrakey": true, "mousekey": false, "nkro": false, "rgb_matrix": true}, "matrix_pins": {"cols": ["D3", "D2", "F5", "F6", "B2"], "rows": ["D4", "D7", "E6", "B6", "B4", "B5"]}, "diode_direction": "COL2ROW", "encoder": {"rotary": [{"pin_a": "B1", "pin_b": "B3"}]}, "qmk": {"tap_keycode_delay": 10}, "processor": "atmega32u4", "bootloader": "caterina", "layouts": {"LAYOUT_all": {"layout": [{"label": "Fn", "matrix": [0, 0], "x": 1.25, "y": 0}, {"label": "Fn2", "matrix": [0, 1], "x": 2.25, "y": 0}, {"label": "Fn3", "matrix": [0, 2], "x": 3.25, "y": 0}, {"label": "Delete", "matrix": [0, 3], "x": 4.25, "y": 0}, {"label": "Num Lock", "matrix": [1, 0], "x": 1.25, "y": 1.25}, {"label": "/", "matrix": [1, 1], "x": 2.25, "y": 1.25}, {"label": "*", "matrix": [1, 2], "x": 3.25, "y": 1.25}, {"label": "-", "matrix": [1, 3], "x": 4.25, "y": 1.25}, {"label": "7", "matrix": [2, 0], "x": 1.25, "y": 2.25}, {"label": "8", "matrix": [2, 1], "x": 2.25, "y": 2.25}, {"label": "9", "matrix": [2, 2], "x": 3.25, "y": 2.25}, {"label": "+", "matrix": [2, 3], "x": 4.25, "y": 2.25}, {"label": "4", "matrix": [3, 0], "x": 1.25, "y": 3.25}, {"label": "5", "matrix": [3, 1], "x": 2.25, "y": 3.25}, {"label": "6", "matrix": [3, 2], "x": 3.25, "y": 3.25}, {"label": "+", "matrix": [3, 3], "x": 4.25, "y": 3.25}, {"label": "1", "matrix": [4, 0], "x": 1.25, "y": 4.25}, {"label": "2", "matrix": [4, 1], "x": 2.25, "y": 4.25}, {"label": "3", "matrix": [4, 2], "x": 3.25, "y": 4.25}, {"label": "Enter", "matrix": [4, 3], "x": 4.25, "y": 4.25}, {"label": "Mute", "matrix": [5, 4], "x": 0, "y": 5.25}, {"label": "0", "matrix": [5, 0], "x": 1.25, "y": 5.25}, {"label": "0", "matrix": [5, 1], "x": 2.25, "y": 5.25}, {"label": ".", "matrix": [5, 2], "x": 3.25, "y": 5.25}, {"label": "Enter", "matrix": [5, 3], "x": 4.25, "y": 5.25}]}, "LAYOUT_numpad": {"layout": [{"label": "Fn", "matrix": [0, 0], "x": 1.25, "y": 0}, {"label": "Fn2", "matrix": [0, 1], "x": 2.25, "y": 0}, {"label": "Fn3", "matrix": [0, 2], "x": 3.25, "y": 0}, {"label": "Delete", "matrix": [0, 3], "x": 4.25, "y": 0}, {"label": "Num Lock", "matrix": [1, 0], "x": 1.25, "y": 1.25}, {"label": "/", "matrix": [1, 1], "x": 2.25, "y": 1.25}, {"label": "*", "matrix": [1, 2], "x": 3.25, "y": 1.25}, {"label": "-", "matrix": [1, 3], "x": 4.25, "y": 1.25}, {"label": "7", "matrix": [2, 0], "x": 1.25, "y": 2.25}, {"label": "8", "matrix": [2, 1], "x": 2.25, "y": 2.25}, {"label": "9", "matrix": [2, 2], "x": 3.25, "y": 2.25}, {"label": "+", "matrix": [2, 3], "x": 4.25, "y": 2.25, "h": 2}, {"label": "4", "matrix": [3, 0], "x": 1.25, "y": 3.25}, {"label": "5", "matrix": [3, 1], "x": 2.25, "y": 3.25}, {"label": "6", "matrix": [3, 2], "x": 3.25, "y": 3.25}, {"label": "1", "matrix": [4, 0], "x": 1.25, "y": 4.25}, {"label": "2", "matrix": [4, 1], "x": 2.25, "y": 4.25}, {"label": "3", "matrix": [4, 2], "x": 3.25, "y": 4.25}, {"label": "Enter", "matrix": [4, 3], "x": 4.25, "y": 4.25, "h": 2}, {"label": "Mute", "matrix": [5, 4], "x": 0, "y": 5.25}, {"label": "0", "matrix": [5, 0], "x": 1.25, "y": 5.25, "w": 2}, {"label": ".", "matrix": [5, 2], "x": 3.25, "y": 5.25}]}}}