{"keyboard_name": "Kintsugi", "manufacturer": "ADPenrose", "url": "https://github.com/ADPenrose/kintsugi_keeb", "maintainer": "<PERSON>", "usb": {"vid": "0x4450", "pid": "0x0001", "device_version": "1.0.0"}, "rgblight": {"saturation_steps": 8, "brightness_steps": 8, "led_count": 16, "sleep": true, "animations": {"breathing": true, "rainbow_mood": true, "rainbow_swirl": true, "snake": true, "knight": true, "christmas": true, "static_gradient": true, "rgb_test": true, "alternating": true}}, "ws2812": {"pin": "F1"}, "features": {"bootmagic": true, "encoder": true, "extrakey": true, "mousekey": false, "nkro": false, "oled": true, "rgblight": true}, "matrix_pins": {"cols": ["F6", "F7", "B1", "B3", "B2", "B6", "F0"], "rows": ["B0", "E6", "D7", "C6", "D4", "D2", "F4", "F5", "B5", "B4"]}, "diode_direction": "COL2ROW", "encoder": {"rotary": [{"pin_a": "B7", "pin_b": "D5"}]}, "qmk": {"tap_keycode_delay": 10}, "processor": "atmega32u4", "bootloader": "atmel-dfu", "layouts": {"LAYOUT": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [5, 0], "x": 7, "y": 0}, {"matrix": [5, 1], "x": 8, "y": 0}, {"matrix": [5, 2], "x": 9, "y": 0}, {"matrix": [5, 3], "x": 10, "y": 0}, {"matrix": [5, 4], "x": 11, "y": 0}, {"matrix": [5, 5], "x": 12, "y": 0}, {"matrix": [5, 6], "x": 13, "y": 0, "w": 2}, {"matrix": [1, 0], "x": 0, "y": 1, "w": 1.5}, {"matrix": [1, 1], "x": 1.5, "y": 1}, {"matrix": [1, 2], "x": 2.5, "y": 1}, {"matrix": [1, 3], "x": 3.5, "y": 1}, {"matrix": [1, 4], "x": 4.5, "y": 1}, {"matrix": [1, 5], "x": 5.5, "y": 1}, {"matrix": [1, 6], "x": 6.5, "y": 1}, {"matrix": [6, 0], "x": 7.5, "y": 1}, {"matrix": [6, 1], "x": 8.5, "y": 1}, {"matrix": [6, 2], "x": 9.5, "y": 1}, {"matrix": [6, 3], "x": 10.5, "y": 1}, {"matrix": [6, 4], "x": 11.5, "y": 1}, {"matrix": [6, 5], "x": 12.5, "y": 1}, {"matrix": [6, 6], "x": 13.5, "y": 1, "w": 1.5}, {"matrix": [2, 0], "x": 0, "y": 2, "w": 1.75}, {"matrix": [2, 1], "x": 1.75, "y": 2}, {"matrix": [2, 2], "x": 2.75, "y": 2}, {"matrix": [2, 3], "x": 3.75, "y": 2}, {"matrix": [2, 4], "x": 4.75, "y": 2}, {"matrix": [2, 5], "x": 5.75, "y": 2}, {"matrix": [2, 6], "x": 6.75, "y": 2}, {"matrix": [7, 0], "x": 7.75, "y": 2}, {"matrix": [7, 1], "x": 8.75, "y": 2}, {"matrix": [7, 2], "x": 9.75, "y": 2}, {"matrix": [7, 3], "x": 10.75, "y": 2}, {"matrix": [7, 4], "x": 11.75, "y": 2}, {"matrix": [7, 5], "x": 12.75, "y": 2, "w": 2.25}, {"matrix": [7, 6], "x": 15, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3, "w": 2.25}, {"matrix": [3, 1], "x": 2.25, "y": 3}, {"matrix": [3, 2], "x": 3.25, "y": 3}, {"matrix": [3, 3], "x": 4.25, "y": 3}, {"matrix": [3, 4], "x": 5.25, "y": 3}, {"matrix": [3, 5], "x": 6.25, "y": 3}, {"matrix": [3, 6], "x": 7.25, "y": 3}, {"matrix": [8, 0], "x": 8.25, "y": 3}, {"matrix": [8, 1], "x": 9.25, "y": 3}, {"matrix": [8, 2], "x": 10.25, "y": 3}, {"matrix": [8, 3], "x": 11.25, "y": 3}, {"matrix": [8, 4], "x": 12.25, "y": 3, "w": 1.75}, {"matrix": [8, 5], "x": 14, "y": 3}, {"matrix": [8, 6], "x": 15, "y": 3}, {"matrix": [4, 0], "x": 0, "y": 4, "w": 1.25}, {"matrix": [4, 1], "x": 1.25, "y": 4, "w": 1.25}, {"matrix": [4, 2], "x": 2.5, "y": 4, "w": 1.25}, {"matrix": [4, 6], "x": 3.75, "y": 4, "w": 6.25}, {"matrix": [9, 2], "x": 10, "y": 4, "w": 1.5}, {"matrix": [9, 3], "x": 11.5, "y": 4, "w": 1.5}, {"matrix": [9, 4], "x": 13, "y": 4}, {"matrix": [9, 5], "x": 14, "y": 4}, {"matrix": [9, 6], "x": 15, "y": 4}]}}}