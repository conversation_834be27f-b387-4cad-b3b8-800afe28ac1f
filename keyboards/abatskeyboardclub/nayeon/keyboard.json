{"keyboard_name": "<PERSON><PERSON><PERSON>", "manufacturer": "Abats Keyboard Club", "maintainer": "<PERSON><PERSON><PERSON><PERSON>", "layout_aliases": {"LAYOUT_ansi": "LAYOUT_tkl_f13_ansi_tsangan", "LAYOUT_iso": "LAYOUT_tkl_f13_iso_tsangan"}, "features": {"bootmagic": true, "mousekey": true, "extrakey": true, "nkro": false, "rgb_matrix": true}, "usb": {"vid": "0xABA7", "pid": "0x0001", "device_version": "2.0.0"}, "ws2812": {"pin": "GP19", "driver": "vendor"}, "rgb_matrix": {"driver": "ws2812", "layout": [{"matrix": [3, 0], "x": 5, "y": 40, "flags": 8}, {"matrix": [0, 15], "x": 211, "y": 0, "flags": 8}]}, "processor": "RP2040", "bootloader": "rp2040", "diode_direction": "COL2ROW", "matrix_pins": {"cols": ["GP29", "GP28", "GP27", "GP26", "GP25", "GP24", "GP23", "GP22", "GP18", "GP17", "GP16", "GP15", "GP14", "GP13", "GP12", "GP11", "GP5"], "rows": ["GP0", "GP1", "GP2", "GP21", "GP3", "GP4"]}, "community_layouts": ["tkl_f13_an<PERSON>_tsangan", "tkl_f13_ansi_t<PERSON>an_split_bs_rshift", "tkl_f13_iso_tsangan", "tkl_f13_iso_tsangan_split_bs_rshift"], "layouts": {"LAYOUT_tkl_f13_ansi_tsangan": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1.25, "y": 0}, {"matrix": [0, 2], "x": 2.25, "y": 0}, {"matrix": [0, 3], "x": 3.25, "y": 0}, {"matrix": [0, 4], "x": 4.25, "y": 0}, {"matrix": [0, 5], "x": 5.5, "y": 0}, {"matrix": [0, 6], "x": 6.5, "y": 0}, {"matrix": [0, 7], "x": 7.5, "y": 0}, {"matrix": [0, 8], "x": 8.5, "y": 0}, {"matrix": [0, 9], "x": 9.75, "y": 0}, {"matrix": [0, 10], "x": 10.75, "y": 0}, {"matrix": [0, 11], "x": 11.75, "y": 0}, {"matrix": [0, 12], "x": 12.75, "y": 0}, {"matrix": [0, 13], "x": 14, "y": 0}, {"matrix": [0, 14], "x": 15.25, "y": 0}, {"matrix": [0, 15], "x": 16.25, "y": 0}, {"matrix": [0, 16], "x": 17.25, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1.25}, {"matrix": [1, 1], "x": 1, "y": 1.25}, {"matrix": [1, 2], "x": 2, "y": 1.25}, {"matrix": [1, 3], "x": 3, "y": 1.25}, {"matrix": [1, 4], "x": 4, "y": 1.25}, {"matrix": [1, 5], "x": 5, "y": 1.25}, {"matrix": [1, 6], "x": 6, "y": 1.25}, {"matrix": [1, 7], "x": 7, "y": 1.25}, {"matrix": [1, 8], "x": 8, "y": 1.25}, {"matrix": [1, 9], "x": 9, "y": 1.25}, {"matrix": [1, 10], "x": 10, "y": 1.25}, {"matrix": [1, 11], "x": 11, "y": 1.25}, {"matrix": [1, 12], "x": 12, "y": 1.25}, {"matrix": [1, 13], "x": 13, "y": 1.25, "w": 2}, {"matrix": [1, 14], "x": 15.25, "y": 1.25}, {"matrix": [1, 15], "x": 16.25, "y": 1.25}, {"matrix": [1, 16], "x": 17.25, "y": 1.25}, {"matrix": [2, 0], "x": 0, "y": 2.25, "w": 1.5}, {"matrix": [2, 1], "x": 1.5, "y": 2.25}, {"matrix": [2, 2], "x": 2.5, "y": 2.25}, {"matrix": [2, 3], "x": 3.5, "y": 2.25}, {"matrix": [2, 4], "x": 4.5, "y": 2.25}, {"matrix": [2, 5], "x": 5.5, "y": 2.25}, {"matrix": [2, 6], "x": 6.5, "y": 2.25}, {"matrix": [2, 7], "x": 7.5, "y": 2.25}, {"matrix": [2, 8], "x": 8.5, "y": 2.25}, {"matrix": [2, 9], "x": 9.5, "y": 2.25}, {"matrix": [2, 10], "x": 10.5, "y": 2.25}, {"matrix": [2, 11], "x": 11.5, "y": 2.25}, {"matrix": [2, 12], "x": 12.5, "y": 2.25}, {"matrix": [2, 13], "x": 13.5, "y": 2.25, "w": 1.5}, {"matrix": [2, 14], "x": 15.25, "y": 2.25}, {"matrix": [2, 15], "x": 16.25, "y": 2.25}, {"matrix": [2, 16], "x": 17.25, "y": 2.25}, {"matrix": [3, 0], "x": 0, "y": 3.25, "w": 1.75}, {"matrix": [3, 1], "x": 1.75, "y": 3.25}, {"matrix": [3, 2], "x": 2.75, "y": 3.25}, {"matrix": [3, 3], "x": 3.75, "y": 3.25}, {"matrix": [3, 4], "x": 4.75, "y": 3.25}, {"matrix": [3, 5], "x": 5.75, "y": 3.25}, {"matrix": [3, 6], "x": 6.75, "y": 3.25}, {"matrix": [3, 7], "x": 7.75, "y": 3.25}, {"matrix": [3, 8], "x": 8.75, "y": 3.25}, {"matrix": [3, 9], "x": 9.75, "y": 3.25}, {"matrix": [3, 10], "x": 10.75, "y": 3.25}, {"matrix": [3, 11], "x": 11.75, "y": 3.25}, {"matrix": [3, 12], "x": 12.75, "y": 3.25, "w": 2.25}, {"matrix": [4, 0], "x": 0, "y": 4.25, "w": 2.25}, {"matrix": [4, 2], "x": 2.25, "y": 4.25}, {"matrix": [4, 3], "x": 3.25, "y": 4.25}, {"matrix": [4, 4], "x": 4.25, "y": 4.25}, {"matrix": [4, 5], "x": 5.25, "y": 4.25}, {"matrix": [4, 6], "x": 6.25, "y": 4.25}, {"matrix": [4, 7], "x": 7.25, "y": 4.25}, {"matrix": [4, 8], "x": 8.25, "y": 4.25}, {"matrix": [4, 9], "x": 9.25, "y": 4.25}, {"matrix": [4, 10], "x": 10.25, "y": 4.25}, {"matrix": [4, 11], "x": 11.25, "y": 4.25}, {"matrix": [4, 12], "x": 12.25, "y": 4.25, "w": 2.75}, {"matrix": [4, 15], "x": 16.25, "y": 4.25}, {"matrix": [5, 0], "x": 0, "y": 5.25, "w": 1.5}, {"matrix": [5, 1], "x": 1.5, "y": 5.25}, {"matrix": [5, 2], "x": 2.5, "y": 5.25, "w": 1.5}, {"matrix": [5, 7], "x": 4, "y": 5.25, "w": 7}, {"matrix": [5, 11], "x": 11, "y": 5.25, "w": 1.5}, {"matrix": [5, 12], "x": 12.5, "y": 5.25}, {"matrix": [5, 13], "x": 13.5, "y": 5.25, "w": 1.5}, {"matrix": [5, 14], "x": 15.25, "y": 5.25}, {"matrix": [5, 15], "x": 16.25, "y": 5.25}, {"matrix": [5, 16], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_f13_ansi_tsangan_split_bs_rshift": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1.25, "y": 0}, {"matrix": [0, 2], "x": 2.25, "y": 0}, {"matrix": [0, 3], "x": 3.25, "y": 0}, {"matrix": [0, 4], "x": 4.25, "y": 0}, {"matrix": [0, 5], "x": 5.5, "y": 0}, {"matrix": [0, 6], "x": 6.5, "y": 0}, {"matrix": [0, 7], "x": 7.5, "y": 0}, {"matrix": [0, 8], "x": 8.5, "y": 0}, {"matrix": [0, 9], "x": 9.75, "y": 0}, {"matrix": [0, 10], "x": 10.75, "y": 0}, {"matrix": [0, 11], "x": 11.75, "y": 0}, {"matrix": [0, 12], "x": 12.75, "y": 0}, {"matrix": [0, 13], "x": 14, "y": 0}, {"matrix": [0, 14], "x": 15.25, "y": 0}, {"matrix": [0, 15], "x": 16.25, "y": 0}, {"matrix": [0, 16], "x": 17.25, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1.25}, {"matrix": [1, 1], "x": 1, "y": 1.25}, {"matrix": [1, 2], "x": 2, "y": 1.25}, {"matrix": [1, 3], "x": 3, "y": 1.25}, {"matrix": [1, 4], "x": 4, "y": 1.25}, {"matrix": [1, 5], "x": 5, "y": 1.25}, {"matrix": [1, 6], "x": 6, "y": 1.25}, {"matrix": [1, 7], "x": 7, "y": 1.25}, {"matrix": [1, 8], "x": 8, "y": 1.25}, {"matrix": [1, 9], "x": 9, "y": 1.25}, {"matrix": [1, 10], "x": 10, "y": 1.25}, {"matrix": [1, 11], "x": 11, "y": 1.25}, {"matrix": [1, 12], "x": 12, "y": 1.25}, {"matrix": [1, 13], "x": 13, "y": 1.25}, {"matrix": [3, 13], "x": 14, "y": 1.25}, {"matrix": [1, 14], "x": 15.25, "y": 1.25}, {"matrix": [1, 15], "x": 16.25, "y": 1.25}, {"matrix": [1, 16], "x": 17.25, "y": 1.25}, {"matrix": [2, 0], "x": 0, "y": 2.25, "w": 1.5}, {"matrix": [2, 1], "x": 1.5, "y": 2.25}, {"matrix": [2, 2], "x": 2.5, "y": 2.25}, {"matrix": [2, 3], "x": 3.5, "y": 2.25}, {"matrix": [2, 4], "x": 4.5, "y": 2.25}, {"matrix": [2, 5], "x": 5.5, "y": 2.25}, {"matrix": [2, 6], "x": 6.5, "y": 2.25}, {"matrix": [2, 7], "x": 7.5, "y": 2.25}, {"matrix": [2, 8], "x": 8.5, "y": 2.25}, {"matrix": [2, 9], "x": 9.5, "y": 2.25}, {"matrix": [2, 10], "x": 10.5, "y": 2.25}, {"matrix": [2, 11], "x": 11.5, "y": 2.25}, {"matrix": [2, 12], "x": 12.5, "y": 2.25}, {"matrix": [2, 13], "x": 13.5, "y": 2.25, "w": 1.5}, {"matrix": [2, 14], "x": 15.25, "y": 2.25}, {"matrix": [2, 15], "x": 16.25, "y": 2.25}, {"matrix": [2, 16], "x": 17.25, "y": 2.25}, {"matrix": [3, 0], "x": 0, "y": 3.25, "w": 1.75}, {"matrix": [3, 1], "x": 1.75, "y": 3.25}, {"matrix": [3, 2], "x": 2.75, "y": 3.25}, {"matrix": [3, 3], "x": 3.75, "y": 3.25}, {"matrix": [3, 4], "x": 4.75, "y": 3.25}, {"matrix": [3, 5], "x": 5.75, "y": 3.25}, {"matrix": [3, 6], "x": 6.75, "y": 3.25}, {"matrix": [3, 7], "x": 7.75, "y": 3.25}, {"matrix": [3, 8], "x": 8.75, "y": 3.25}, {"matrix": [3, 9], "x": 9.75, "y": 3.25}, {"matrix": [3, 10], "x": 10.75, "y": 3.25}, {"matrix": [3, 11], "x": 11.75, "y": 3.25}, {"matrix": [3, 12], "x": 12.75, "y": 3.25, "w": 2.25}, {"matrix": [4, 0], "x": 0, "y": 4.25, "w": 2.25}, {"matrix": [4, 2], "x": 2.25, "y": 4.25}, {"matrix": [4, 3], "x": 3.25, "y": 4.25}, {"matrix": [4, 4], "x": 4.25, "y": 4.25}, {"matrix": [4, 5], "x": 5.25, "y": 4.25}, {"matrix": [4, 6], "x": 6.25, "y": 4.25}, {"matrix": [4, 7], "x": 7.25, "y": 4.25}, {"matrix": [4, 8], "x": 8.25, "y": 4.25}, {"matrix": [4, 9], "x": 9.25, "y": 4.25}, {"matrix": [4, 10], "x": 10.25, "y": 4.25}, {"matrix": [4, 11], "x": 11.25, "y": 4.25}, {"matrix": [4, 12], "x": 12.25, "y": 4.25, "w": 1.75}, {"matrix": [4, 13], "x": 14, "y": 4.25}, {"matrix": [4, 15], "x": 16.25, "y": 4.25}, {"matrix": [5, 0], "x": 0, "y": 5.25, "w": 1.5}, {"matrix": [5, 1], "x": 1.5, "y": 5.25}, {"matrix": [5, 2], "x": 2.5, "y": 5.25, "w": 1.5}, {"matrix": [5, 7], "x": 4, "y": 5.25, "w": 7}, {"matrix": [5, 11], "x": 11, "y": 5.25, "w": 1.5}, {"matrix": [5, 12], "x": 12.5, "y": 5.25}, {"matrix": [5, 13], "x": 13.5, "y": 5.25, "w": 1.5}, {"matrix": [5, 14], "x": 15.25, "y": 5.25}, {"matrix": [5, 15], "x": 16.25, "y": 5.25}, {"matrix": [5, 16], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_f13_iso_tsangan": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1.25, "y": 0}, {"matrix": [0, 2], "x": 2.25, "y": 0}, {"matrix": [0, 3], "x": 3.25, "y": 0}, {"matrix": [0, 4], "x": 4.25, "y": 0}, {"matrix": [0, 5], "x": 5.5, "y": 0}, {"matrix": [0, 6], "x": 6.5, "y": 0}, {"matrix": [0, 7], "x": 7.5, "y": 0}, {"matrix": [0, 8], "x": 8.5, "y": 0}, {"matrix": [0, 9], "x": 9.75, "y": 0}, {"matrix": [0, 10], "x": 10.75, "y": 0}, {"matrix": [0, 11], "x": 11.75, "y": 0}, {"matrix": [0, 12], "x": 12.75, "y": 0}, {"matrix": [0, 13], "x": 14, "y": 0}, {"matrix": [0, 14], "x": 15.25, "y": 0}, {"matrix": [0, 15], "x": 16.25, "y": 0}, {"matrix": [0, 16], "x": 17.25, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1.25}, {"matrix": [1, 1], "x": 1, "y": 1.25}, {"matrix": [1, 2], "x": 2, "y": 1.25}, {"matrix": [1, 3], "x": 3, "y": 1.25}, {"matrix": [1, 4], "x": 4, "y": 1.25}, {"matrix": [1, 5], "x": 5, "y": 1.25}, {"matrix": [1, 6], "x": 6, "y": 1.25}, {"matrix": [1, 7], "x": 7, "y": 1.25}, {"matrix": [1, 8], "x": 8, "y": 1.25}, {"matrix": [1, 9], "x": 9, "y": 1.25}, {"matrix": [1, 10], "x": 10, "y": 1.25}, {"matrix": [1, 11], "x": 11, "y": 1.25}, {"matrix": [1, 12], "x": 12, "y": 1.25}, {"matrix": [1, 13], "x": 13, "y": 1.25, "w": 2}, {"matrix": [1, 14], "x": 15.25, "y": 1.25}, {"matrix": [1, 15], "x": 16.25, "y": 1.25}, {"matrix": [1, 16], "x": 17.25, "y": 1.25}, {"matrix": [2, 0], "x": 0, "y": 2.25, "w": 1.5}, {"matrix": [2, 1], "x": 1.5, "y": 2.25}, {"matrix": [2, 2], "x": 2.5, "y": 2.25}, {"matrix": [2, 3], "x": 3.5, "y": 2.25}, {"matrix": [2, 4], "x": 4.5, "y": 2.25}, {"matrix": [2, 5], "x": 5.5, "y": 2.25}, {"matrix": [2, 6], "x": 6.5, "y": 2.25}, {"matrix": [2, 7], "x": 7.5, "y": 2.25}, {"matrix": [2, 8], "x": 8.5, "y": 2.25}, {"matrix": [2, 9], "x": 9.5, "y": 2.25}, {"matrix": [2, 10], "x": 10.5, "y": 2.25}, {"matrix": [2, 11], "x": 11.5, "y": 2.25}, {"matrix": [2, 12], "x": 12.5, "y": 2.25}, {"matrix": [2, 14], "x": 15.25, "y": 2.25}, {"matrix": [2, 15], "x": 16.25, "y": 2.25}, {"matrix": [2, 16], "x": 17.25, "y": 2.25}, {"matrix": [3, 0], "x": 0, "y": 3.25, "w": 1.75}, {"matrix": [3, 1], "x": 1.75, "y": 3.25}, {"matrix": [3, 2], "x": 2.75, "y": 3.25}, {"matrix": [3, 3], "x": 3.75, "y": 3.25}, {"matrix": [3, 4], "x": 4.75, "y": 3.25}, {"matrix": [3, 5], "x": 5.75, "y": 3.25}, {"matrix": [3, 6], "x": 6.75, "y": 3.25}, {"matrix": [3, 7], "x": 7.75, "y": 3.25}, {"matrix": [3, 8], "x": 8.75, "y": 3.25}, {"matrix": [3, 9], "x": 9.75, "y": 3.25}, {"matrix": [3, 10], "x": 10.75, "y": 3.25}, {"matrix": [3, 11], "x": 11.75, "y": 3.25}, {"matrix": [3, 12], "x": 12.75, "y": 3.25}, {"matrix": [2, 13], "x": 13.75, "y": 2.25, "w": 1.25, "h": 2}, {"matrix": [4, 0], "x": 0, "y": 4.25, "w": 1.25}, {"matrix": [4, 1], "x": 1.25, "y": 4.25}, {"matrix": [4, 2], "x": 2.25, "y": 4.25}, {"matrix": [4, 3], "x": 3.25, "y": 4.25}, {"matrix": [4, 4], "x": 4.25, "y": 4.25}, {"matrix": [4, 5], "x": 5.25, "y": 4.25}, {"matrix": [4, 6], "x": 6.25, "y": 4.25}, {"matrix": [4, 7], "x": 7.25, "y": 4.25}, {"matrix": [4, 8], "x": 8.25, "y": 4.25}, {"matrix": [4, 9], "x": 9.25, "y": 4.25}, {"matrix": [4, 10], "x": 10.25, "y": 4.25}, {"matrix": [4, 11], "x": 11.25, "y": 4.25}, {"matrix": [4, 12], "x": 12.25, "y": 4.25, "w": 2.75}, {"matrix": [4, 15], "x": 16.25, "y": 4.25}, {"matrix": [5, 0], "x": 0, "y": 5.25, "w": 1.5}, {"matrix": [5, 1], "x": 1.5, "y": 5.25}, {"matrix": [5, 2], "x": 2.5, "y": 5.25, "w": 1.5}, {"matrix": [5, 7], "x": 4, "y": 5.25, "w": 7}, {"matrix": [5, 11], "x": 11, "y": 5.25, "w": 1.5}, {"matrix": [5, 12], "x": 12.5, "y": 5.25}, {"matrix": [5, 13], "x": 13.5, "y": 5.25, "w": 1.5}, {"matrix": [5, 14], "x": 15.25, "y": 5.25}, {"matrix": [5, 15], "x": 16.25, "y": 5.25}, {"matrix": [5, 16], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_f13_iso_tsangan_split_bs_rshift": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1.25, "y": 0}, {"matrix": [0, 2], "x": 2.25, "y": 0}, {"matrix": [0, 3], "x": 3.25, "y": 0}, {"matrix": [0, 4], "x": 4.25, "y": 0}, {"matrix": [0, 5], "x": 5.5, "y": 0}, {"matrix": [0, 6], "x": 6.5, "y": 0}, {"matrix": [0, 7], "x": 7.5, "y": 0}, {"matrix": [0, 8], "x": 8.5, "y": 0}, {"matrix": [0, 9], "x": 9.75, "y": 0}, {"matrix": [0, 10], "x": 10.75, "y": 0}, {"matrix": [0, 11], "x": 11.75, "y": 0}, {"matrix": [0, 12], "x": 12.75, "y": 0}, {"matrix": [0, 13], "x": 14, "y": 0}, {"matrix": [0, 14], "x": 15.25, "y": 0}, {"matrix": [0, 15], "x": 16.25, "y": 0}, {"matrix": [0, 16], "x": 17.25, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1.25}, {"matrix": [1, 1], "x": 1, "y": 1.25}, {"matrix": [1, 2], "x": 2, "y": 1.25}, {"matrix": [1, 3], "x": 3, "y": 1.25}, {"matrix": [1, 4], "x": 4, "y": 1.25}, {"matrix": [1, 5], "x": 5, "y": 1.25}, {"matrix": [1, 6], "x": 6, "y": 1.25}, {"matrix": [1, 7], "x": 7, "y": 1.25}, {"matrix": [1, 8], "x": 8, "y": 1.25}, {"matrix": [1, 9], "x": 9, "y": 1.25}, {"matrix": [1, 10], "x": 10, "y": 1.25}, {"matrix": [1, 11], "x": 11, "y": 1.25}, {"matrix": [1, 12], "x": 12, "y": 1.25}, {"matrix": [1, 13], "x": 13, "y": 1.25}, {"matrix": [3, 13], "x": 14, "y": 1.25}, {"matrix": [1, 14], "x": 15.25, "y": 1.25}, {"matrix": [1, 15], "x": 16.25, "y": 1.25}, {"matrix": [1, 16], "x": 17.25, "y": 1.25}, {"matrix": [2, 0], "x": 0, "y": 2.25, "w": 1.5}, {"matrix": [2, 1], "x": 1.5, "y": 2.25}, {"matrix": [2, 2], "x": 2.5, "y": 2.25}, {"matrix": [2, 3], "x": 3.5, "y": 2.25}, {"matrix": [2, 4], "x": 4.5, "y": 2.25}, {"matrix": [2, 5], "x": 5.5, "y": 2.25}, {"matrix": [2, 6], "x": 6.5, "y": 2.25}, {"matrix": [2, 7], "x": 7.5, "y": 2.25}, {"matrix": [2, 8], "x": 8.5, "y": 2.25}, {"matrix": [2, 9], "x": 9.5, "y": 2.25}, {"matrix": [2, 10], "x": 10.5, "y": 2.25}, {"matrix": [2, 11], "x": 11.5, "y": 2.25}, {"matrix": [2, 12], "x": 12.5, "y": 2.25}, {"matrix": [2, 14], "x": 15.25, "y": 2.25}, {"matrix": [2, 15], "x": 16.25, "y": 2.25}, {"matrix": [2, 16], "x": 17.25, "y": 2.25}, {"matrix": [3, 0], "x": 0, "y": 3.25, "w": 1.75}, {"matrix": [3, 1], "x": 1.75, "y": 3.25}, {"matrix": [3, 2], "x": 2.75, "y": 3.25}, {"matrix": [3, 3], "x": 3.75, "y": 3.25}, {"matrix": [3, 4], "x": 4.75, "y": 3.25}, {"matrix": [3, 5], "x": 5.75, "y": 3.25}, {"matrix": [3, 6], "x": 6.75, "y": 3.25}, {"matrix": [3, 7], "x": 7.75, "y": 3.25}, {"matrix": [3, 8], "x": 8.75, "y": 3.25}, {"matrix": [3, 9], "x": 9.75, "y": 3.25}, {"matrix": [3, 10], "x": 10.75, "y": 3.25}, {"matrix": [3, 11], "x": 11.75, "y": 3.25}, {"matrix": [3, 12], "x": 12.75, "y": 3.25}, {"matrix": [2, 13], "x": 13.75, "y": 2.25, "w": 1.25, "h": 2}, {"matrix": [4, 0], "x": 0, "y": 4.25, "w": 1.25}, {"matrix": [4, 1], "x": 1.25, "y": 4.25}, {"matrix": [4, 2], "x": 2.25, "y": 4.25}, {"matrix": [4, 3], "x": 3.25, "y": 4.25}, {"matrix": [4, 4], "x": 4.25, "y": 4.25}, {"matrix": [4, 5], "x": 5.25, "y": 4.25}, {"matrix": [4, 6], "x": 6.25, "y": 4.25}, {"matrix": [4, 7], "x": 7.25, "y": 4.25}, {"matrix": [4, 8], "x": 8.25, "y": 4.25}, {"matrix": [4, 9], "x": 9.25, "y": 4.25}, {"matrix": [4, 10], "x": 10.25, "y": 4.25}, {"matrix": [4, 11], "x": 11.25, "y": 4.25}, {"matrix": [4, 12], "x": 12.25, "y": 4.25, "w": 1.75}, {"matrix": [4, 13], "x": 14, "y": 4.25}, {"matrix": [4, 15], "x": 16.25, "y": 4.25}, {"matrix": [5, 0], "x": 0, "y": 5.25, "w": 1.5}, {"matrix": [5, 1], "x": 1.5, "y": 5.25}, {"matrix": [5, 2], "x": 2.5, "y": 5.25, "w": 1.5}, {"matrix": [5, 7], "x": 4, "y": 5.25, "w": 7}, {"matrix": [5, 11], "x": 11, "y": 5.25, "w": 1.5}, {"matrix": [5, 12], "x": 12.5, "y": 5.25}, {"matrix": [5, 13], "x": 13.5, "y": 5.25, "w": 1.5}, {"matrix": [5, 14], "x": 15.25, "y": 5.25}, {"matrix": [5, 15], "x": 16.25, "y": 5.25}, {"matrix": [5, 16], "x": 17.25, "y": 5.25}]}, "LAYOUT_all": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1.25, "y": 0}, {"matrix": [0, 2], "x": 2.25, "y": 0}, {"matrix": [0, 3], "x": 3.25, "y": 0}, {"matrix": [0, 4], "x": 4.25, "y": 0}, {"matrix": [0, 5], "x": 5.5, "y": 0}, {"matrix": [0, 6], "x": 6.5, "y": 0}, {"matrix": [0, 7], "x": 7.5, "y": 0}, {"matrix": [0, 8], "x": 8.5, "y": 0}, {"matrix": [0, 9], "x": 9.75, "y": 0}, {"matrix": [0, 10], "x": 10.75, "y": 0}, {"matrix": [0, 11], "x": 11.75, "y": 0}, {"matrix": [0, 12], "x": 12.75, "y": 0}, {"matrix": [0, 13], "x": 14, "y": 0}, {"matrix": [0, 14], "x": 15.25, "y": 0}, {"matrix": [0, 15], "x": 16.25, "y": 0}, {"matrix": [0, 16], "x": 17.25, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1.25}, {"matrix": [1, 1], "x": 1, "y": 1.25}, {"matrix": [1, 2], "x": 2, "y": 1.25}, {"matrix": [1, 3], "x": 3, "y": 1.25}, {"matrix": [1, 4], "x": 4, "y": 1.25}, {"matrix": [1, 5], "x": 5, "y": 1.25}, {"matrix": [1, 6], "x": 6, "y": 1.25}, {"matrix": [1, 7], "x": 7, "y": 1.25}, {"matrix": [1, 8], "x": 8, "y": 1.25}, {"matrix": [1, 9], "x": 9, "y": 1.25}, {"matrix": [1, 10], "x": 10, "y": 1.25}, {"matrix": [1, 11], "x": 11, "y": 1.25}, {"matrix": [1, 12], "x": 12, "y": 1.25}, {"matrix": [1, 13], "x": 13, "y": 1.25}, {"matrix": [3, 13], "x": 14, "y": 1.25}, {"matrix": [1, 14], "x": 15.25, "y": 1.25}, {"matrix": [1, 15], "x": 16.25, "y": 1.25}, {"matrix": [1, 16], "x": 17.25, "y": 1.25}, {"matrix": [2, 0], "x": 0, "y": 2.25, "w": 1.5}, {"matrix": [2, 1], "x": 1.5, "y": 2.25}, {"matrix": [2, 2], "x": 2.5, "y": 2.25}, {"matrix": [2, 3], "x": 3.5, "y": 2.25}, {"matrix": [2, 4], "x": 4.5, "y": 2.25}, {"matrix": [2, 5], "x": 5.5, "y": 2.25}, {"matrix": [2, 6], "x": 6.5, "y": 2.25}, {"matrix": [2, 7], "x": 7.5, "y": 2.25}, {"matrix": [2, 8], "x": 8.5, "y": 2.25}, {"matrix": [2, 9], "x": 9.5, "y": 2.25}, {"matrix": [2, 10], "x": 10.5, "y": 2.25}, {"matrix": [2, 11], "x": 11.5, "y": 2.25}, {"matrix": [2, 12], "x": 12.5, "y": 2.25}, {"matrix": [2, 13], "x": 13.5, "y": 2.25, "w": 1.5}, {"matrix": [2, 14], "x": 15.25, "y": 2.25}, {"matrix": [2, 15], "x": 16.25, "y": 2.25}, {"matrix": [2, 16], "x": 17.25, "y": 2.25}, {"matrix": [3, 0], "x": 0, "y": 3.25, "w": 1.75}, {"matrix": [3, 1], "x": 1.75, "y": 3.25}, {"matrix": [3, 2], "x": 2.75, "y": 3.25}, {"matrix": [3, 3], "x": 3.75, "y": 3.25}, {"matrix": [3, 4], "x": 4.75, "y": 3.25}, {"matrix": [3, 5], "x": 5.75, "y": 3.25}, {"matrix": [3, 6], "x": 6.75, "y": 3.25}, {"matrix": [3, 7], "x": 7.75, "y": 3.25}, {"matrix": [3, 8], "x": 8.75, "y": 3.25}, {"matrix": [3, 9], "x": 9.75, "y": 3.25}, {"matrix": [3, 10], "x": 10.75, "y": 3.25}, {"matrix": [3, 11], "x": 11.75, "y": 3.25}, {"matrix": [3, 12], "x": 12.75, "y": 3.25, "w": 2.25}, {"matrix": [4, 0], "x": 0, "y": 4.25, "w": 1.25}, {"matrix": [4, 1], "x": 1.25, "y": 4.25}, {"matrix": [4, 2], "x": 2.25, "y": 4.25}, {"matrix": [4, 3], "x": 3.25, "y": 4.25}, {"matrix": [4, 4], "x": 4.25, "y": 4.25}, {"matrix": [4, 5], "x": 5.25, "y": 4.25}, {"matrix": [4, 6], "x": 6.25, "y": 4.25}, {"matrix": [4, 7], "x": 7.25, "y": 4.25}, {"matrix": [4, 8], "x": 8.25, "y": 4.25}, {"matrix": [4, 9], "x": 9.25, "y": 4.25}, {"matrix": [4, 10], "x": 10.25, "y": 4.25}, {"matrix": [4, 11], "x": 11.25, "y": 4.25}, {"matrix": [4, 12], "x": 12.25, "y": 4.25, "w": 1.75}, {"matrix": [4, 13], "x": 14, "y": 4.25}, {"matrix": [4, 15], "x": 16.25, "y": 4.25}, {"matrix": [5, 0], "x": 0, "y": 5.25, "w": 1.5}, {"matrix": [5, 1], "x": 1.5, "y": 5.25}, {"matrix": [5, 2], "x": 2.5, "y": 5.25, "w": 1.5}, {"matrix": [5, 7], "x": 4, "y": 5.25, "w": 7}, {"matrix": [5, 11], "x": 11, "y": 5.25, "w": 1.5}, {"matrix": [5, 12], "x": 12.5, "y": 5.25}, {"matrix": [5, 13], "x": 13.5, "y": 5.25, "w": 1.5}, {"matrix": [5, 14], "x": 15.25, "y": 5.25}, {"matrix": [5, 15], "x": 16.25, "y": 5.25}, {"matrix": [5, 16], "x": 17.25, "y": 5.25}]}}}