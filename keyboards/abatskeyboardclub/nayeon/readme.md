# Nayeon

![Nayeon](https://i.imgur.com/OfB5ikbl.jpg)

Nayeon is a screwless TKL keyboard with an extra key, inspired by a K-pop artist named <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.

* Keyboard Maintainer: [<PERSON>](https://github.com/ramonimbao)
* Hardware Supported: RP2040

Make example for this keyboard (after setting up your build environment):

    make abatskeyboardclub/nayeon:via
    
To get to the bootloader, simply press the reset button located at the back of the PCB. You can then flash new firmware onto it.

Flashing example for this keyboard:

    make abatskeyboardclub/nayeon:via:flash

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
