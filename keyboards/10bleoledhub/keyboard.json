{"keyboard_name": "10<PERSON><PERSON><PERSON><PERSON>", "manufacturer": "haierwangwei2005", "url": "https://github.com/haierwangwei2005/10BLE-OLED-HUB", "maintainer": "haierwangwei2005", "usb": {"vid": "0x7C88", "pid": "0x7C99", "device_version": "0.0.1"}, "features": {"bluetooth": true, "bootmagic": true, "encoder": true, "extrakey": true, "mousekey": true, "nkro": false, "oled": true}, "bluetooth": {"driver": "bluefruit_le"}, "rgblight": {"led_count": 4, "animations": {"breathing": true, "rainbow_mood": true, "rainbow_swirl": true, "snake": true, "knight": true, "christmas": true, "static_gradient": true, "rgb_test": true, "alternating": true, "twinkle": true}}, "ws2812": {"pin": "B7"}, "matrix_pins": {"cols": ["D6", "D7", "B5"], "rows": ["F0", "F5", "F4", "F6"]}, "diode_direction": "ROW2COL", "encoder": {"rotary": [{"pin_a": "C7", "pin_b": "F7"}]}, "processor": "atmega32u4", "bootloader": "caterina", "layouts": {"LAYOUT": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0, "w": 0.8, "h": 0.8}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3}, {"matrix": [3, 1], "x": 1, "y": 3}, {"matrix": [3, 2], "x": 2, "y": 3}]}}}