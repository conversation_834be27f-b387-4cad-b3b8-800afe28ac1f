# 2Key2CrawlPad

![2Key2CrawlPad](https://i.imgur.com/ON7m7RI.jpg)

A 2x5 macropad/numpad with a rotary encoder, exclusively available at Austin Keycrawl 2018 (12-01-2018).

Keyboard Maintainer: QMK Community  
Keyboard Designer: [<PERSON>](https://github.com/colemark<PERSON>)  
Hardware Supported: Crawlpad  
Hardware Availability: Exclusive to Keycrawl events, contact [awwwwwwyeaahhhhhh](https://www.reddit.com/user/awwwwwwyeaahhhhhh) for more details.

Make example for this keyboard (after setting up your build environment):

    make 2key2crawl:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
