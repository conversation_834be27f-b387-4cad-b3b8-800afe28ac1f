{"keyboard_name": "2Key2Crawl", "manufacturer": "WoodKeys.click", "maintainer": "qmk", "usb": {"vid": "0xFEED", "pid": "0x6090", "device_version": "0.0.2"}, "features": {"bootmagic": true, "console": true, "encoder": true, "extrakey": false, "mousekey": false, "nkro": false}, "qmk": {"locking": {"enabled": true, "resync": true}}, "matrix_pins": {"cols": ["B3", "B4", "B5", "B6", "B7", "C7", "B2"], "rows": ["C4", "C5"]}, "diode_direction": "COL2ROW", "encoder": {"rotary": [{"pin_a": "D0", "pin_b": "D1", "resolution": 1}]}, "rgblight": {"led_count": 3, "animations": {"breathing": true, "rainbow_mood": true, "rainbow_swirl": true, "snake": true, "knight": true, "christmas": true, "static_gradient": true, "rgb_test": true, "alternating": true, "twinkle": true}}, "ws2812": {"pin": "C6"}, "processor": "atmega32u2", "bootloader": "atmel-dfu", "layouts": {"LAYOUT": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [1, 5], "x": 4, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [1, 4], "x": 4, "y": 1}, {"matrix": [1, 6], "x": 5.5, "y": 0.5}]}}}