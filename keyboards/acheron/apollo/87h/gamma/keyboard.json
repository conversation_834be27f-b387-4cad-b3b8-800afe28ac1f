{"keyboard_name": "Apollo87H rev. <PERSON>", "usb": {"pid": "0x8774", "device_version": "0.0.3", "shared_endpoint": {"keyboard": true}}, "rgb_matrix": {"animations": {"alphas_mods": true, "gradient_up_down": true, "gradient_left_right": true, "breathing": true, "band_sat": true, "band_val": true, "band_pinwheel_sat": true, "band_pinwheel_val": true, "band_spiral_sat": true, "band_spiral_val": true, "cycle_all": true, "cycle_left_right": true, "cycle_up_down": true, "rainbow_moving_chevron": true, "cycle_out_in": true, "cycle_out_in_dual": true, "cycle_pinwheel": true, "cycle_spiral": true, "dual_beacon": true, "rainbow_beacon": true, "rainbow_pinwheels": true, "raindrops": true, "jellybean_raindrops": true, "hue_breathing": true, "hue_pendulum": true, "hue_wave": true, "fractal": true, "pixel_rain": true, "pixel_flow": true, "typing_heatmap": true, "digital_rain": true, "solid_reactive_simple": true, "solid_reactive": true, "solid_reactive_wide": true, "solid_reactive_multiwide": true, "solid_reactive_cross": true, "solid_reactive_multicross": true, "solid_reactive_nexus": true, "solid_reactive_multinexus": true, "splash": true, "multisplash": true, "solid_splash": true, "solid_multisplash": true}, "default": {"animation": "hue_wave", "val": 80}, "driver": "is31fl3741", "sleep": true}, "features": {"bootmagic": true, "mousekey": true, "extrakey": true, "rgb_matrix": true}, "qmk": {"locking": {"enabled": true, "resync": true}}, "matrix_pins": {"cols": ["B3", "A15", "A10", "A8", "B14", "B12", "B10", "B1", "B0", "A7", "A4", "A5", "A6", "C15", "A0", "A1"], "rows": ["C14", "C13", "B9", "B4", "A3", "A2"]}, "diode_direction": "COL2ROW", "processor": "STM32F411", "bootloader": "stm32-dfu", "layouts": {"LAYOUT_tkl_ansi": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 2, "y": 0}, {"matrix": [0, 2], "x": 3, "y": 0}, {"matrix": [0, 3], "x": 4, "y": 0}, {"matrix": [0, 4], "x": 5, "y": 0}, {"matrix": [0, 5], "x": 6.5, "y": 0}, {"matrix": [0, 6], "x": 7.5, "y": 0}, {"matrix": [0, 7], "x": 8.5, "y": 0}, {"matrix": [0, 8], "x": 9.5, "y": 0}, {"matrix": [0, 9], "x": 11, "y": 0}, {"matrix": [0, 10], "x": 12, "y": 0}, {"matrix": [0, 11], "x": 13, "y": 0}, {"matrix": [0, 12], "x": 14, "y": 0}, {"matrix": [0, 13], "x": 15.25, "y": 0}, {"matrix": [0, 14], "x": 16.25, "y": 0}, {"matrix": [0, 15], "x": 17.25, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1.25}, {"matrix": [1, 1], "x": 1, "y": 1.25}, {"matrix": [1, 2], "x": 2, "y": 1.25}, {"matrix": [1, 3], "x": 3, "y": 1.25}, {"matrix": [1, 4], "x": 4, "y": 1.25}, {"matrix": [1, 5], "x": 5, "y": 1.25}, {"matrix": [1, 6], "x": 6, "y": 1.25}, {"matrix": [1, 7], "x": 7, "y": 1.25}, {"matrix": [1, 8], "x": 8, "y": 1.25}, {"matrix": [1, 9], "x": 9, "y": 1.25}, {"matrix": [1, 10], "x": 10, "y": 1.25}, {"matrix": [1, 11], "x": 11, "y": 1.25}, {"matrix": [1, 12], "x": 12, "y": 1.25}, {"matrix": [1, 13], "x": 13, "y": 1.25, "w": 2}, {"matrix": [1, 14], "x": 15.25, "y": 1.25}, {"matrix": [1, 15], "x": 16.25, "y": 1.25}, {"matrix": [4, 15], "x": 17.25, "y": 1.25}, {"matrix": [2, 0], "x": 0, "y": 2.25, "w": 1.5}, {"matrix": [2, 1], "x": 1.5, "y": 2.25}, {"matrix": [2, 2], "x": 2.5, "y": 2.25}, {"matrix": [2, 3], "x": 3.5, "y": 2.25}, {"matrix": [2, 4], "x": 4.5, "y": 2.25}, {"matrix": [2, 5], "x": 5.5, "y": 2.25}, {"matrix": [2, 6], "x": 6.5, "y": 2.25}, {"matrix": [2, 7], "x": 7.5, "y": 2.25}, {"matrix": [2, 8], "x": 8.5, "y": 2.25}, {"matrix": [2, 9], "x": 9.5, "y": 2.25}, {"matrix": [2, 10], "x": 10.5, "y": 2.25}, {"matrix": [2, 11], "x": 11.5, "y": 2.25}, {"matrix": [2, 12], "x": 12.5, "y": 2.25}, {"matrix": [2, 13], "x": 13.5, "y": 2.25, "w": 1.5}, {"matrix": [2, 14], "x": 15.25, "y": 2.25}, {"matrix": [2, 15], "x": 16.25, "y": 2.25}, {"matrix": [4, 14], "x": 17.25, "y": 2.25}, {"matrix": [3, 0], "x": 0, "y": 3.25, "w": 1.75}, {"matrix": [3, 1], "x": 1.75, "y": 3.25}, {"matrix": [3, 2], "x": 2.75, "y": 3.25}, {"matrix": [3, 3], "x": 3.75, "y": 3.25}, {"matrix": [3, 4], "x": 4.75, "y": 3.25}, {"matrix": [3, 5], "x": 5.75, "y": 3.25}, {"matrix": [3, 6], "x": 6.75, "y": 3.25}, {"matrix": [3, 7], "x": 7.75, "y": 3.25}, {"matrix": [3, 8], "x": 8.75, "y": 3.25}, {"matrix": [3, 9], "x": 9.75, "y": 3.25}, {"matrix": [3, 10], "x": 10.75, "y": 3.25}, {"matrix": [3, 11], "x": 11.75, "y": 3.25}, {"matrix": [3, 12], "x": 12.75, "y": 3.25, "w": 2.25}, {"matrix": [4, 0], "x": 0, "y": 4.25, "w": 2.25}, {"matrix": [4, 2], "x": 2.25, "y": 4.25}, {"matrix": [4, 3], "x": 3.25, "y": 4.25}, {"matrix": [4, 4], "x": 4.25, "y": 4.25}, {"matrix": [4, 5], "x": 5.25, "y": 4.25}, {"matrix": [4, 6], "x": 6.25, "y": 4.25}, {"matrix": [4, 7], "x": 7.25, "y": 4.25}, {"matrix": [4, 8], "x": 8.25, "y": 4.25}, {"matrix": [4, 9], "x": 9.25, "y": 4.25}, {"matrix": [4, 10], "x": 10.25, "y": 4.25}, {"matrix": [4, 11], "x": 11.25, "y": 4.25}, {"matrix": [4, 12], "x": 12.25, "y": 4.25, "w": 2.75}, {"matrix": [4, 13], "x": 16.25, "y": 4.25}, {"matrix": [5, 0], "x": 0, "y": 5.25, "w": 1.25}, {"matrix": [5, 1], "x": 1.25, "y": 5.25, "w": 1.25}, {"matrix": [5, 2], "x": 2.5, "y": 5.25, "w": 1.25}, {"matrix": [5, 6], "x": 3.75, "y": 5.25, "w": 6.25}, {"matrix": [5, 9], "x": 10, "y": 5.25, "w": 1.25}, {"matrix": [5, 10], "x": 11.25, "y": 5.25, "w": 1.25}, {"matrix": [5, 11], "x": 12.5, "y": 5.25, "w": 1.25}, {"matrix": [5, 12], "x": 13.75, "y": 5.25, "w": 1.25}, {"matrix": [5, 13], "x": 15.25, "y": 5.25}, {"matrix": [5, 14], "x": 16.25, "y": 5.25}, {"matrix": [5, 15], "x": 17.25, "y": 5.25}]}}}