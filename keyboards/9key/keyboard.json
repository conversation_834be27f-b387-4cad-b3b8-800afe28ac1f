{"keyboard_name": "9K<PERSON>", "manufacturer": "<PERSON>", "maintainer": "qmk", "usb": {"vid": "0xFEED", "pid": "0x0007", "device_version": "0.0.1"}, "rgblight": {"led_count": 1}, "ws2812": {"pin": "F7"}, "features": {"bootmagic": false, "extrakey": true, "mousekey": true, "nkro": true, "unicode": true}, "matrix_pins": {"cols": ["F4", "F5", "F6"], "rows": ["D1", "D0", "D4"]}, "diode_direction": "COL2ROW", "processor": "atmega32u4", "bootloader": "caterina", "layouts": {"LAYOUT": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}]}}}