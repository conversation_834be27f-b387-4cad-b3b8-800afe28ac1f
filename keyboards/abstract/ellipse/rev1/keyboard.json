{"keyboard_name": "Ellipse", "manufacturer": "AbstractKB", "url": "https://abstractkb.tk/product/ellipse-rev1", "maintainer": "AbstractKB", "usb": {"vid": "0xFEED", "pid": "0x0001", "device_version": "0.0.1"}, "features": {"bootmagic": false, "encoder": true, "extrakey": true, "mousekey": false, "nkro": false, "rgblight": true}, "qmk": {"locking": {"enabled": true, "resync": true}}, "matrix_pins": {"cols": ["F0", "B6", "B5"], "rows": ["D3", "C7"]}, "diode_direction": "COL2ROW", "encoder": {"rotary": [{"pin_a": "B2", "pin_b": "B1", "resolution": 2}, {"pin_a": "B3", "pin_b": "B7", "resolution": 2}, {"pin_a": "D5", "pin_b": "B4", "resolution": 2}]}, "backlight": {"pin": "C6", "levels": 15}, "rgblight": {"saturation_steps": 8, "brightness_steps": 8, "led_count": 3, "sleep": true}, "ws2812": {"pin": "E6"}, "processor": "atmega32u4", "bootloader": "atmel-dfu", "layouts": {"LAYOUT": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1.25}, {"matrix": [1, 1], "x": 1, "y": 1.25}, {"matrix": [1, 2], "x": 2, "y": 1.25}]}}}