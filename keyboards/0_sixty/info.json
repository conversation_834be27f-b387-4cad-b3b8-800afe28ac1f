{"manufacturer": "ven0mtr0n", "maintainer": "vinamarora8", "usb": {"vid": "0x7654", "device_version": "0.0.1"}, "matrix_pins": {"cols": ["D3", "D2", "D1", "D0", "D4", "C6", "D7", "E6", "B4", "B5", "B3", "B2"], "rows": ["B1", "F7", "F6", "F5", "F4"]}, "diode_direction": "COL2ROW", "features": {"extrakey": true, "console": true, "command": true, "nkro": true}, "build": {"lto": true}, "qmk": {"locking": {"enabled": true, "resync": true}}, "development_board": "promicro", "layouts": {"LAYOUT_1x2uC": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [1, 4], "x": 4, "y": 1}, {"matrix": [1, 5], "x": 5, "y": 1}, {"matrix": [1, 6], "x": 6, "y": 1}, {"matrix": [1, 7], "x": 7, "y": 1}, {"matrix": [1, 8], "x": 8, "y": 1}, {"matrix": [1, 9], "x": 9, "y": 1}, {"matrix": [1, 10], "x": 10, "y": 1}, {"matrix": [1, 11], "x": 11, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2}, {"matrix": [2, 4], "x": 4, "y": 2}, {"matrix": [2, 5], "x": 5, "y": 2}, {"matrix": [2, 6], "x": 6, "y": 2}, {"matrix": [2, 7], "x": 7, "y": 2}, {"matrix": [2, 8], "x": 8, "y": 2}, {"matrix": [2, 9], "x": 9, "y": 2}, {"matrix": [2, 10], "x": 10, "y": 2}, {"matrix": [2, 11], "x": 11, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3}, {"matrix": [3, 1], "x": 1, "y": 3}, {"matrix": [3, 2], "x": 2, "y": 3}, {"matrix": [3, 3], "x": 3, "y": 3}, {"matrix": [3, 4], "x": 4, "y": 3}, {"matrix": [3, 5], "x": 5, "y": 3}, {"matrix": [3, 6], "x": 6, "y": 3}, {"matrix": [3, 7], "x": 7, "y": 3}, {"matrix": [3, 8], "x": 8, "y": 3}, {"matrix": [3, 9], "x": 9, "y": 3}, {"matrix": [3, 10], "x": 10, "y": 3}, {"matrix": [3, 11], "x": 11, "y": 3}, {"matrix": [4, 0], "x": 0, "y": 4}, {"matrix": [4, 1], "x": 1, "y": 4}, {"matrix": [4, 2], "x": 2, "y": 4}, {"matrix": [4, 3], "x": 3, "y": 4}, {"matrix": [4, 4], "x": 4, "y": 4}, {"matrix": [4, 6], "x": 5, "y": 4, "w": 2}, {"matrix": [4, 7], "x": 7, "y": 4}, {"matrix": [4, 8], "x": 8, "y": 4}, {"matrix": [4, 9], "x": 9, "y": 4}, {"matrix": [4, 10], "x": 10, "y": 4}, {"matrix": [4, 11], "x": 11, "y": 4}]}, "LAYOUT_2x2uC": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [1, 4], "x": 4, "y": 1}, {"matrix": [1, 5], "x": 5, "y": 1}, {"matrix": [1, 6], "x": 6, "y": 1}, {"matrix": [1, 7], "x": 7, "y": 1}, {"matrix": [1, 8], "x": 8, "y": 1}, {"matrix": [1, 9], "x": 9, "y": 1}, {"matrix": [1, 10], "x": 10, "y": 1}, {"matrix": [1, 11], "x": 11, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2}, {"matrix": [2, 4], "x": 4, "y": 2}, {"matrix": [2, 5], "x": 5, "y": 2}, {"matrix": [2, 6], "x": 6, "y": 2}, {"matrix": [2, 7], "x": 7, "y": 2}, {"matrix": [2, 8], "x": 8, "y": 2}, {"matrix": [2, 9], "x": 9, "y": 2}, {"matrix": [2, 10], "x": 10, "y": 2}, {"matrix": [2, 11], "x": 11, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3}, {"matrix": [3, 1], "x": 1, "y": 3}, {"matrix": [3, 2], "x": 2, "y": 3}, {"matrix": [3, 3], "x": 3, "y": 3}, {"matrix": [3, 4], "x": 4, "y": 3}, {"matrix": [3, 5], "x": 5, "y": 3}, {"matrix": [3, 6], "x": 6, "y": 3}, {"matrix": [3, 7], "x": 7, "y": 3}, {"matrix": [3, 8], "x": 8, "y": 3}, {"matrix": [3, 9], "x": 9, "y": 3}, {"matrix": [3, 10], "x": 10, "y": 3}, {"matrix": [3, 11], "x": 11, "y": 3}, {"matrix": [4, 0], "x": 0, "y": 4}, {"matrix": [4, 1], "x": 1, "y": 4}, {"matrix": [4, 2], "x": 2, "y": 4}, {"matrix": [4, 3], "x": 3, "y": 4}, {"matrix": [4, 5], "x": 4, "y": 4, "w": 2}, {"matrix": [4, 6], "x": 6, "y": 4, "w": 2}, {"matrix": [4, 8], "x": 8, "y": 4}, {"matrix": [4, 9], "x": 9, "y": 4}, {"matrix": [4, 10], "x": 10, "y": 4}, {"matrix": [4, 11], "x": 11, "y": 4}]}, "LAYOUT_ortho_5x12": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [1, 4], "x": 4, "y": 1}, {"matrix": [1, 5], "x": 5, "y": 1}, {"matrix": [1, 6], "x": 6, "y": 1}, {"matrix": [1, 7], "x": 7, "y": 1}, {"matrix": [1, 8], "x": 8, "y": 1}, {"matrix": [1, 9], "x": 9, "y": 1}, {"matrix": [1, 10], "x": 10, "y": 1}, {"matrix": [1, 11], "x": 11, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2}, {"matrix": [2, 4], "x": 4, "y": 2}, {"matrix": [2, 5], "x": 5, "y": 2}, {"matrix": [2, 6], "x": 6, "y": 2}, {"matrix": [2, 7], "x": 7, "y": 2}, {"matrix": [2, 8], "x": 8, "y": 2}, {"matrix": [2, 9], "x": 9, "y": 2}, {"matrix": [2, 10], "x": 10, "y": 2}, {"matrix": [2, 11], "x": 11, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3}, {"matrix": [3, 1], "x": 1, "y": 3}, {"matrix": [3, 2], "x": 2, "y": 3}, {"matrix": [3, 3], "x": 3, "y": 3}, {"matrix": [3, 4], "x": 4, "y": 3}, {"matrix": [3, 5], "x": 5, "y": 3}, {"matrix": [3, 6], "x": 6, "y": 3}, {"matrix": [3, 7], "x": 7, "y": 3}, {"matrix": [3, 8], "x": 8, "y": 3}, {"matrix": [3, 9], "x": 9, "y": 3}, {"matrix": [3, 10], "x": 10, "y": 3}, {"matrix": [3, 11], "x": 11, "y": 3}, {"matrix": [4, 0], "x": 0, "y": 4}, {"matrix": [4, 1], "x": 1, "y": 4}, {"matrix": [4, 2], "x": 2, "y": 4}, {"matrix": [4, 3], "x": 3, "y": 4}, {"matrix": [4, 4], "x": 4, "y": 4}, {"matrix": [4, 5], "x": 5, "y": 4}, {"matrix": [4, 6], "x": 6, "y": 4}, {"matrix": [4, 7], "x": 7, "y": 4}, {"matrix": [4, 8], "x": 8, "y": 4}, {"matrix": [4, 9], "x": 9, "y": 4}, {"matrix": [4, 10], "x": 10, "y": 4}, {"matrix": [4, 11], "x": 11, "y": 4}]}, "LAYOUT_1x2uR": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [1, 4], "x": 4, "y": 1}, {"matrix": [1, 5], "x": 5, "y": 1}, {"matrix": [1, 6], "x": 6, "y": 1}, {"matrix": [1, 7], "x": 7, "y": 1}, {"matrix": [1, 8], "x": 8, "y": 1}, {"matrix": [1, 9], "x": 9, "y": 1}, {"matrix": [1, 10], "x": 10, "y": 1}, {"matrix": [1, 11], "x": 11, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2}, {"matrix": [2, 4], "x": 4, "y": 2}, {"matrix": [2, 5], "x": 5, "y": 2}, {"matrix": [2, 6], "x": 6, "y": 2}, {"matrix": [2, 7], "x": 7, "y": 2}, {"matrix": [2, 8], "x": 8, "y": 2}, {"matrix": [2, 9], "x": 9, "y": 2}, {"matrix": [2, 10], "x": 10, "y": 2}, {"matrix": [2, 11], "x": 11, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3}, {"matrix": [3, 1], "x": 1, "y": 3}, {"matrix": [3, 2], "x": 2, "y": 3}, {"matrix": [3, 3], "x": 3, "y": 3}, {"matrix": [3, 4], "x": 4, "y": 3}, {"matrix": [3, 5], "x": 5, "y": 3}, {"matrix": [3, 6], "x": 6, "y": 3}, {"matrix": [3, 7], "x": 7, "y": 3}, {"matrix": [3, 8], "x": 8, "y": 3}, {"matrix": [3, 9], "x": 9, "y": 3}, {"matrix": [3, 10], "x": 10, "y": 3}, {"matrix": [3, 11], "x": 11, "y": 3}, {"matrix": [4, 0], "x": 0, "y": 4}, {"matrix": [4, 1], "x": 1, "y": 4}, {"matrix": [4, 2], "x": 2, "y": 4}, {"matrix": [4, 3], "x": 3, "y": 4}, {"matrix": [4, 4], "x": 4, "y": 4}, {"matrix": [4, 5], "x": 5, "y": 4}, {"matrix": [4, 6], "x": 6, "y": 4, "w": 2}, {"matrix": [4, 8], "x": 8, "y": 4}, {"matrix": [4, 9], "x": 9, "y": 4}, {"matrix": [4, 10], "x": 10, "y": 4}, {"matrix": [4, 11], "x": 11, "y": 4}]}, "LAYOUT_1x2uL": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [1, 4], "x": 4, "y": 1}, {"matrix": [1, 5], "x": 5, "y": 1}, {"matrix": [1, 6], "x": 6, "y": 1}, {"matrix": [1, 7], "x": 7, "y": 1}, {"matrix": [1, 8], "x": 8, "y": 1}, {"matrix": [1, 9], "x": 9, "y": 1}, {"matrix": [1, 10], "x": 10, "y": 1}, {"matrix": [1, 11], "x": 11, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2}, {"matrix": [2, 4], "x": 4, "y": 2}, {"matrix": [2, 5], "x": 5, "y": 2}, {"matrix": [2, 6], "x": 6, "y": 2}, {"matrix": [2, 7], "x": 7, "y": 2}, {"matrix": [2, 8], "x": 8, "y": 2}, {"matrix": [2, 9], "x": 9, "y": 2}, {"matrix": [2, 10], "x": 10, "y": 2}, {"matrix": [2, 11], "x": 11, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3}, {"matrix": [3, 1], "x": 1, "y": 3}, {"matrix": [3, 2], "x": 2, "y": 3}, {"matrix": [3, 3], "x": 3, "y": 3}, {"matrix": [3, 4], "x": 4, "y": 3}, {"matrix": [3, 5], "x": 5, "y": 3}, {"matrix": [3, 6], "x": 6, "y": 3}, {"matrix": [3, 7], "x": 7, "y": 3}, {"matrix": [3, 8], "x": 8, "y": 3}, {"matrix": [3, 9], "x": 9, "y": 3}, {"matrix": [3, 10], "x": 10, "y": 3}, {"matrix": [3, 11], "x": 11, "y": 3}, {"matrix": [4, 0], "x": 0, "y": 4}, {"matrix": [4, 1], "x": 1, "y": 4}, {"matrix": [4, 2], "x": 2, "y": 4}, {"matrix": [4, 3], "x": 3, "y": 4}, {"matrix": [4, 5], "x": 4, "y": 4, "w": 2}, {"matrix": [4, 6], "x": 6, "y": 4}, {"matrix": [4, 7], "x": 7, "y": 4}, {"matrix": [4, 8], "x": 8, "y": 4}, {"matrix": [4, 9], "x": 9, "y": 4}, {"matrix": [4, 10], "x": 10, "y": 4}, {"matrix": [4, 11], "x": 11, "y": 4}]}}}