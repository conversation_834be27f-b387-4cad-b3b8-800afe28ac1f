# waffling60

A 60% PCB for MX switches, one hot swap and one solder-pcb version with decent layout support.

More info: https://geekhack.org/index.php?topic=103531.0

* Keyboard Maintainer: [4pplet](https://github.com/4pplet)
* Hardware Supported: [waffling60](https://github.com/4pplet/waffling60)

Make example for this keyboard (after setting up your build environment):

    make 4pplet/waffling60/rev_a:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
