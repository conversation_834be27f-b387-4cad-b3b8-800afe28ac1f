{"manufacturer": "4pplet", "maintainer": "4pplet", "usb": {"vid": "0x4444"}, "community_layouts": ["tkl_ansi", "tkl_ansi_split_bs_rshift", "tkl_ansi_tsangan", "tkl_ansi_tsangan_split_bs_rshift", "tkl_f13_ansi", "tkl_f13_ansi_split_bs_rshift", "tkl_f13_an<PERSON>_tsangan", "tkl_f13_ansi_t<PERSON>an_split_bs_rshift", "tkl_iso", "tkl_iso_split_bs_rshift", "tkl_iso_tsangan", "tkl_iso_tsangan_split_bs_rshift", "tkl_f13_iso", "tkl_f13_iso_split_bs_rshift", "tkl_f13_iso_tsangan", "tkl_f13_iso_tsangan_split_bs_rshift"], "layouts": {"LAYOUT_all": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [1, 0], "x": 1.25, "y": 0}, {"label": "F2", "matrix": [0, 1], "x": 2.25, "y": 0}, {"label": "F3", "matrix": [1, 1], "x": 3.25, "y": 0}, {"label": "F4", "matrix": [0, 2], "x": 4.25, "y": 0}, {"label": "F5", "matrix": [1, 2], "x": 5.5, "y": 0}, {"label": "F6", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F7", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F8", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F9", "matrix": [1, 4], "x": 9.75, "y": 0}, {"label": "F10", "matrix": [0, 5], "x": 10.75, "y": 0}, {"label": "F11", "matrix": [1, 5], "x": 11.75, "y": 0}, {"label": "F12", "matrix": [0, 6], "x": 12.75, "y": 0}, {"label": "F13", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "`", "matrix": [3, 6], "x": 13, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 14, "y": 1.25}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "\\", "matrix": [5, 6], "x": 13.5, "y": 2.25, "w": 1.5}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "#", "matrix": [6, 6], "x": 12.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 13.75, "y": 3.25, "w": 1.25}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 1.25}, {"label": "\\", "matrix": [9, 0], "x": 1.25, "y": 4.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 1.75}, {"label": "Fn", "matrix": [9, 6], "x": 14, "y": 4.25}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 1], "x": 1.25, "y": 5.25, "w": 1.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.25}, {"label": "Space", "matrix": [11, 2], "x": 3.75, "y": 5.25, "w": 2.25}, {"label": "Space", "matrix": [10, 3], "x": 6, "y": 5.25, "w": 1.25}, {"label": "Space", "matrix": [11, 3], "x": 7.25, "y": 5.25, "w": 2.75}, {"label": "Fn", "matrix": [10, 4], "x": 10, "y": 5.25, "w": 1.25}, {"label": "Alt", "matrix": [11, 4], "x": 11.25, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [10, 5], "x": 12.5, "y": 5.25, "w": 1.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.75, "y": 5.25, "w": 1.25}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}, {"label": "Fn", "matrix": [11, 0], "x": 15.25, "y": 3.25}, {"label": "Fn", "matrix": [10, 2], "x": 17.25, "y": 3.25}]}, "LAYOUT_tkl_ansi": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "F2", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "F3", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "F4", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "F5", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F6", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F7", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F8", "matrix": [1, 4], "x": 9.5, "y": 0}, {"label": "F9", "matrix": [0, 5], "x": 11, "y": 0}, {"label": "F10", "matrix": [1, 5], "x": 12, "y": 0}, {"label": "F11", "matrix": [0, 6], "x": 13, "y": 0}, {"label": "F12", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 13, "y": 1.25, "w": 2}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "\\", "matrix": [5, 6], "x": 13.5, "y": 2.25, "w": 1.5}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 12.75, "y": 3.25, "w": 2.25}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 2.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 2.75}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 1], "x": 1.25, "y": 5.25, "w": 1.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.25}, {"label": "Space", "matrix": [10, 3], "x": 3.75, "y": 5.25, "w": 6.25}, {"label": "Alt", "matrix": [10, 4], "x": 10, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 4], "x": 11.25, "y": 5.25, "w": 1.25}, {"label": "<PERSON><PERSON>", "matrix": [10, 5], "x": 12.5, "y": 5.25, "w": 1.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.75, "y": 5.25, "w": 1.25}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_ansi_split_bs_rshift": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "F2", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "F3", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "F4", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "F5", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F6", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F7", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F8", "matrix": [1, 4], "x": 9.5, "y": 0}, {"label": "F9", "matrix": [0, 5], "x": 11, "y": 0}, {"label": "F10", "matrix": [1, 5], "x": 12, "y": 0}, {"label": "F11", "matrix": [0, 6], "x": 13, "y": 0}, {"label": "F12", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "`", "matrix": [3, 6], "x": 13, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 14, "y": 1.25}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "\\", "matrix": [5, 6], "x": 13.5, "y": 2.25, "w": 1.5}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 12.75, "y": 3.25, "w": 2.25}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 2.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 1.75}, {"label": "Fn", "matrix": [9, 6], "x": 14, "y": 4.25}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 1], "x": 1.25, "y": 5.25, "w": 1.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.25}, {"label": "Space", "matrix": [10, 3], "x": 3.75, "y": 5.25, "w": 6.25}, {"label": "Alt", "matrix": [10, 4], "x": 10, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 4], "x": 11.25, "y": 5.25, "w": 1.25}, {"label": "<PERSON><PERSON>", "matrix": [10, 5], "x": 12.5, "y": 5.25, "w": 1.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.75, "y": 5.25, "w": 1.25}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_ansi_tsangan": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "F2", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "F3", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "F4", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "F5", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F6", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F7", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F8", "matrix": [1, 4], "x": 9.5, "y": 0}, {"label": "F9", "matrix": [0, 5], "x": 11, "y": 0}, {"label": "F10", "matrix": [1, 5], "x": 12, "y": 0}, {"label": "F11", "matrix": [0, 6], "x": 13, "y": 0}, {"label": "F12", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 13, "y": 1.25, "w": 2}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "\\", "matrix": [5, 6], "x": 13.5, "y": 2.25, "w": 1.5}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 12.75, "y": 3.25, "w": 2.25}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 2.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 2.75}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.5}, {"label": "GUI", "matrix": [11, 1], "x": 1.5, "y": 5.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.5}, {"label": "Space", "matrix": [10, 3], "x": 4, "y": 5.25, "w": 7}, {"label": "Alt", "matrix": [11, 4], "x": 11, "y": 5.25, "w": 1.5}, {"label": "<PERSON><PERSON>", "matrix": [10, 5], "x": 12.5, "y": 5.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.5, "y": 5.25, "w": 1.5}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_ansi_tsangan_split_bs_rshift": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "F2", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "F3", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "F4", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "F5", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F6", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F7", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F8", "matrix": [1, 4], "x": 9.5, "y": 0}, {"label": "F9", "matrix": [0, 5], "x": 11, "y": 0}, {"label": "F10", "matrix": [1, 5], "x": 12, "y": 0}, {"label": "F11", "matrix": [0, 6], "x": 13, "y": 0}, {"label": "F12", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "`", "matrix": [3, 6], "x": 13, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 14, "y": 1.25}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "\\", "matrix": [5, 6], "x": 13.5, "y": 2.25, "w": 1.5}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 12.75, "y": 3.25, "w": 2.25}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 2.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 1.75}, {"label": "Fn", "matrix": [9, 6], "x": 14, "y": 4.25}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.5}, {"label": "GUI", "matrix": [11, 1], "x": 1.5, "y": 5.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.5}, {"label": "Space", "matrix": [10, 3], "x": 4, "y": 5.25, "w": 7}, {"label": "Alt", "matrix": [11, 4], "x": 11, "y": 5.25, "w": 1.5}, {"label": "<PERSON><PERSON>", "matrix": [10, 5], "x": 12.5, "y": 5.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.5, "y": 5.25, "w": 1.5}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_f13_ansi_split_space_split_bs_rshift": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [1, 0], "x": 1.25, "y": 0}, {"label": "F2", "matrix": [0, 1], "x": 2.25, "y": 0}, {"label": "F3", "matrix": [1, 1], "x": 3.25, "y": 0}, {"label": "F4", "matrix": [0, 2], "x": 4.25, "y": 0}, {"label": "F5", "matrix": [1, 2], "x": 5.5, "y": 0}, {"label": "F6", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F7", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F8", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F9", "matrix": [1, 4], "x": 9.75, "y": 0}, {"label": "F10", "matrix": [0, 5], "x": 10.75, "y": 0}, {"label": "F11", "matrix": [1, 5], "x": 11.75, "y": 0}, {"label": "F12", "matrix": [0, 6], "x": 12.75, "y": 0}, {"label": "F13", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "`", "matrix": [3, 6], "x": 13, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 14, "y": 1.25}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "\\", "matrix": [5, 6], "x": 13.5, "y": 2.25, "w": 1.5}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 12.75, "y": 3.25, "w": 2.25}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 2.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 1.75}, {"label": "Fn", "matrix": [9, 6], "x": 14, "y": 4.25}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 1], "x": 1.25, "y": 5.25, "w": 1.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.25}, {"label": "Space", "matrix": [11, 2], "x": 3.75, "y": 5.25, "w": 2.25}, {"label": "Space", "matrix": [10, 3], "x": 6, "y": 5.25, "w": 1.25}, {"label": "Space", "matrix": [11, 3], "x": 7.25, "y": 5.25, "w": 2.75}, {"label": "Alt", "matrix": [10, 4], "x": 10, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 4], "x": 11.25, "y": 5.25, "w": 1.25}, {"label": "<PERSON><PERSON>", "matrix": [10, 5], "x": 12.5, "y": 5.25, "w": 1.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.75, "y": 5.25, "w": 1.25}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_f13_ansi": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [1, 0], "x": 1.25, "y": 0}, {"label": "F2", "matrix": [0, 1], "x": 2.25, "y": 0}, {"label": "F3", "matrix": [1, 1], "x": 3.25, "y": 0}, {"label": "F4", "matrix": [0, 2], "x": 4.25, "y": 0}, {"label": "F5", "matrix": [1, 2], "x": 5.5, "y": 0}, {"label": "F6", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F7", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F8", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F9", "matrix": [1, 4], "x": 9.75, "y": 0}, {"label": "F10", "matrix": [0, 5], "x": 10.75, "y": 0}, {"label": "F11", "matrix": [1, 5], "x": 11.75, "y": 0}, {"label": "F12", "matrix": [0, 6], "x": 12.75, "y": 0}, {"label": "F13", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 13, "y": 1.25, "w": 2}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "\\", "matrix": [5, 6], "x": 13.5, "y": 2.25, "w": 1.5}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 12.75, "y": 3.25, "w": 2.25}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 2.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 2.75}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 1], "x": 1.25, "y": 5.25, "w": 1.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.25}, {"label": "Space", "matrix": [10, 3], "x": 3.75, "y": 5.25, "w": 6.25}, {"label": "Alt", "matrix": [10, 4], "x": 10, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 4], "x": 11.25, "y": 5.25, "w": 1.25}, {"label": "<PERSON><PERSON>", "matrix": [10, 5], "x": 12.5, "y": 5.25, "w": 1.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.75, "y": 5.25, "w": 1.25}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_f13_ansi_split_bs_rshift": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [1, 0], "x": 1.25, "y": 0}, {"label": "F2", "matrix": [0, 1], "x": 2.25, "y": 0}, {"label": "F3", "matrix": [1, 1], "x": 3.25, "y": 0}, {"label": "F4", "matrix": [0, 2], "x": 4.25, "y": 0}, {"label": "F5", "matrix": [1, 2], "x": 5.5, "y": 0}, {"label": "F6", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F7", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F8", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F9", "matrix": [1, 4], "x": 9.75, "y": 0}, {"label": "F10", "matrix": [0, 5], "x": 10.75, "y": 0}, {"label": "F11", "matrix": [1, 5], "x": 11.75, "y": 0}, {"label": "F12", "matrix": [0, 6], "x": 12.75, "y": 0}, {"label": "F13", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "`", "matrix": [3, 6], "x": 13, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 14, "y": 1.25}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "\\", "matrix": [5, 6], "x": 13.5, "y": 2.25, "w": 1.5}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 12.75, "y": 3.25, "w": 2.25}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 2.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 1.75}, {"label": "Fn", "matrix": [9, 6], "x": 14, "y": 4.25}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 1], "x": 1.25, "y": 5.25, "w": 1.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.25}, {"label": "Space", "matrix": [10, 3], "x": 3.75, "y": 5.25, "w": 6.25}, {"label": "Alt", "matrix": [10, 4], "x": 10, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 4], "x": 11.25, "y": 5.25, "w": 1.25}, {"label": "<PERSON><PERSON>", "matrix": [10, 5], "x": 12.5, "y": 5.25, "w": 1.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.75, "y": 5.25, "w": 1.25}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_f13_ansi_tsangan": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [1, 0], "x": 1.25, "y": 0}, {"label": "F2", "matrix": [0, 1], "x": 2.25, "y": 0}, {"label": "F3", "matrix": [1, 1], "x": 3.25, "y": 0}, {"label": "F4", "matrix": [0, 2], "x": 4.25, "y": 0}, {"label": "F5", "matrix": [1, 2], "x": 5.5, "y": 0}, {"label": "F6", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F7", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F8", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F9", "matrix": [1, 4], "x": 9.75, "y": 0}, {"label": "F10", "matrix": [0, 5], "x": 10.75, "y": 0}, {"label": "F11", "matrix": [1, 5], "x": 11.75, "y": 0}, {"label": "F12", "matrix": [0, 6], "x": 12.75, "y": 0}, {"label": "F13", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 13, "y": 1.25, "w": 2}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "\\", "matrix": [5, 6], "x": 13.5, "y": 2.25, "w": 1.5}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 12.75, "y": 3.25, "w": 2.25}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 2.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 2.75}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.5}, {"label": "GUI", "matrix": [11, 1], "x": 1.5, "y": 5.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.5}, {"label": "Space", "matrix": [10, 3], "x": 4, "y": 5.25, "w": 7}, {"label": "Alt", "matrix": [11, 4], "x": 11, "y": 5.25, "w": 1.5}, {"label": "<PERSON><PERSON>", "matrix": [10, 5], "x": 12.5, "y": 5.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.5, "y": 5.25, "w": 1.5}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_f13_ansi_tsangan_split_bs_rshift": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [1, 0], "x": 1.25, "y": 0}, {"label": "F2", "matrix": [0, 1], "x": 2.25, "y": 0}, {"label": "F3", "matrix": [1, 1], "x": 3.25, "y": 0}, {"label": "F4", "matrix": [0, 2], "x": 4.25, "y": 0}, {"label": "F5", "matrix": [1, 2], "x": 5.5, "y": 0}, {"label": "F6", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F7", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F8", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F9", "matrix": [1, 4], "x": 9.75, "y": 0}, {"label": "F10", "matrix": [0, 5], "x": 10.75, "y": 0}, {"label": "F11", "matrix": [1, 5], "x": 11.75, "y": 0}, {"label": "F12", "matrix": [0, 6], "x": 12.75, "y": 0}, {"label": "F13", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "`", "matrix": [3, 6], "x": 13, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 14, "y": 1.25}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "\\", "matrix": [5, 6], "x": 13.5, "y": 2.25, "w": 1.5}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 12.75, "y": 3.25, "w": 2.25}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 2.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 1.75}, {"label": "Fn", "matrix": [9, 6], "x": 14, "y": 4.25}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.5}, {"label": "GUI", "matrix": [11, 1], "x": 1.5, "y": 5.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.5}, {"label": "Space", "matrix": [10, 3], "x": 4, "y": 5.25, "w": 7}, {"label": "Alt", "matrix": [11, 4], "x": 11, "y": 5.25, "w": 1.5}, {"label": "<PERSON><PERSON>", "matrix": [10, 5], "x": 12.5, "y": 5.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.5, "y": 5.25, "w": 1.5}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_iso": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "F2", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "F3", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "F4", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "F5", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F6", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F7", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F8", "matrix": [1, 4], "x": 9.5, "y": 0}, {"label": "F9", "matrix": [0, 5], "x": 11, "y": 0}, {"label": "F10", "matrix": [1, 5], "x": 12, "y": 0}, {"label": "F11", "matrix": [0, 6], "x": 13, "y": 0}, {"label": "F12", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 13, "y": 1.25, "w": 2}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "#", "matrix": [6, 6], "x": 12.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 13.75, "y": 2.25, "w": 1.25, "h": 2}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 1.25}, {"label": "\\", "matrix": [9, 0], "x": 1.25, "y": 4.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 2.75}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 1], "x": 1.25, "y": 5.25, "w": 1.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.25}, {"label": "Space", "matrix": [10, 3], "x": 3.75, "y": 5.25, "w": 6.25}, {"label": "Alt", "matrix": [10, 4], "x": 10, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 4], "x": 11.25, "y": 5.25, "w": 1.25}, {"label": "<PERSON><PERSON>", "matrix": [10, 5], "x": 12.5, "y": 5.25, "w": 1.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.75, "y": 5.25, "w": 1.25}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_iso_split_bs_rshift": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "F2", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "F3", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "F4", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "F5", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F6", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F7", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F8", "matrix": [1, 4], "x": 9.5, "y": 0}, {"label": "F9", "matrix": [0, 5], "x": 11, "y": 0}, {"label": "F10", "matrix": [1, 5], "x": 12, "y": 0}, {"label": "F11", "matrix": [0, 6], "x": 13, "y": 0}, {"label": "F12", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "`", "matrix": [3, 6], "x": 13, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 14, "y": 1.25}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "#", "matrix": [6, 6], "x": 12.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 13.75, "y": 2.25, "w": 1.25, "h": 2}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 1.25}, {"label": "\\", "matrix": [9, 0], "x": 1.25, "y": 4.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 1.75}, {"label": "Fn", "matrix": [9, 6], "x": 14, "y": 4.25}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 1], "x": 1.25, "y": 5.25, "w": 1.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.25}, {"label": "Space", "matrix": [10, 3], "x": 3.75, "y": 5.25, "w": 6.25}, {"label": "Alt", "matrix": [10, 4], "x": 10, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 4], "x": 11.25, "y": 5.25, "w": 1.25}, {"label": "<PERSON><PERSON>", "matrix": [10, 5], "x": 12.5, "y": 5.25, "w": 1.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.75, "y": 5.25, "w": 1.25}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_iso_tsangan": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "F2", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "F3", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "F4", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "F5", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F6", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F7", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F8", "matrix": [1, 4], "x": 9.5, "y": 0}, {"label": "F9", "matrix": [0, 5], "x": 11, "y": 0}, {"label": "F10", "matrix": [1, 5], "x": 12, "y": 0}, {"label": "F11", "matrix": [0, 6], "x": 13, "y": 0}, {"label": "F12", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 13, "y": 1.25, "w": 2}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "#", "matrix": [6, 6], "x": 12.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 13.75, "y": 2.25, "w": 1.25, "h": 2}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 1.25}, {"label": "\\", "matrix": [9, 0], "x": 1.25, "y": 4.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 2.75}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.5}, {"label": "GUI", "matrix": [11, 1], "x": 1.5, "y": 5.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.5}, {"label": "Space", "matrix": [10, 3], "x": 4, "y": 5.25, "w": 7}, {"label": "Alt", "matrix": [11, 4], "x": 11, "y": 5.25, "w": 1.5}, {"label": "<PERSON><PERSON>", "matrix": [10, 5], "x": 12.5, "y": 5.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.5, "y": 5.25, "w": 1.5}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_iso_tsangan_split_bs_rshift": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "F2", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "F3", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "F4", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "F5", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F6", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F7", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F8", "matrix": [1, 4], "x": 9.5, "y": 0}, {"label": "F9", "matrix": [0, 5], "x": 11, "y": 0}, {"label": "F10", "matrix": [1, 5], "x": 12, "y": 0}, {"label": "F11", "matrix": [0, 6], "x": 13, "y": 0}, {"label": "F12", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "`", "matrix": [3, 6], "x": 13, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 14, "y": 1.25}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "#", "matrix": [6, 6], "x": 12.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 13.75, "y": 2.25, "w": 1.25, "h": 2}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 1.25}, {"label": "\\", "matrix": [9, 0], "x": 1.25, "y": 4.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 1.75}, {"label": "Fn", "matrix": [9, 6], "x": 14, "y": 4.25}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.5}, {"label": "GUI", "matrix": [11, 1], "x": 1.5, "y": 5.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.5}, {"label": "Space", "matrix": [10, 3], "x": 4, "y": 5.25, "w": 7}, {"label": "Alt", "matrix": [11, 4], "x": 11, "y": 5.25, "w": 1.5}, {"label": "<PERSON><PERSON>", "matrix": [10, 5], "x": 12.5, "y": 5.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.5, "y": 5.25, "w": 1.5}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_f13_iso_split_space_split_bs_rshift": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [1, 0], "x": 1.25, "y": 0}, {"label": "F2", "matrix": [0, 1], "x": 2.25, "y": 0}, {"label": "F3", "matrix": [1, 1], "x": 3.25, "y": 0}, {"label": "F4", "matrix": [0, 2], "x": 4.25, "y": 0}, {"label": "F5", "matrix": [1, 2], "x": 5.5, "y": 0}, {"label": "F6", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F7", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F8", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F9", "matrix": [1, 4], "x": 9.75, "y": 0}, {"label": "F10", "matrix": [0, 5], "x": 10.75, "y": 0}, {"label": "F11", "matrix": [1, 5], "x": 11.75, "y": 0}, {"label": "F12", "matrix": [0, 6], "x": 12.75, "y": 0}, {"label": "F13", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "`", "matrix": [3, 6], "x": 13, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 14, "y": 1.25}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "#", "matrix": [6, 6], "x": 12.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 13.75, "y": 2.25, "w": 1.25, "h": 2}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 1.25}, {"label": "\\", "matrix": [9, 0], "x": 1.25, "y": 4.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 1.75}, {"label": "Fn", "matrix": [9, 6], "x": 14, "y": 4.25}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 1], "x": 1.25, "y": 5.25, "w": 1.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.25}, {"label": "Space", "matrix": [11, 2], "x": 3.75, "y": 5.25, "w": 2.25}, {"label": "Space", "matrix": [10, 3], "x": 6, "y": 5.25, "w": 1.25}, {"label": "Space", "matrix": [11, 3], "x": 7.25, "y": 5.25, "w": 2.75}, {"label": "Fn", "matrix": [10, 4], "x": 10, "y": 5.25, "w": 1.25}, {"label": "Alt", "matrix": [11, 4], "x": 11.25, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [10, 5], "x": 12.5, "y": 5.25, "w": 1.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.75, "y": 5.25, "w": 1.25}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_f13_iso": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [1, 0], "x": 1.25, "y": 0}, {"label": "F2", "matrix": [0, 1], "x": 2.25, "y": 0}, {"label": "F3", "matrix": [1, 1], "x": 3.25, "y": 0}, {"label": "F4", "matrix": [0, 2], "x": 4.25, "y": 0}, {"label": "F5", "matrix": [1, 2], "x": 5.5, "y": 0}, {"label": "F6", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F7", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F8", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F9", "matrix": [1, 4], "x": 9.75, "y": 0}, {"label": "F10", "matrix": [0, 5], "x": 10.75, "y": 0}, {"label": "F11", "matrix": [1, 5], "x": 11.75, "y": 0}, {"label": "F12", "matrix": [0, 6], "x": 12.75, "y": 0}, {"label": "F13", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 13, "y": 1.25, "w": 2}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "#", "matrix": [6, 6], "x": 12.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 13.75, "y": 2.25, "w": 1.25, "h": 2}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 1.25}, {"label": "\\", "matrix": [9, 0], "x": 1.25, "y": 4.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 2.75}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 1], "x": 1.25, "y": 5.25, "w": 1.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.25}, {"label": "Space", "matrix": [10, 3], "x": 3.75, "y": 5.25, "w": 6.25}, {"label": "Alt", "matrix": [10, 4], "x": 10, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 4], "x": 11.25, "y": 5.25, "w": 1.25}, {"label": "<PERSON><PERSON>", "matrix": [10, 5], "x": 12.5, "y": 5.25, "w": 1.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.75, "y": 5.25, "w": 1.25}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_f13_iso_split_bs_rshift": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [1, 0], "x": 1.25, "y": 0}, {"label": "F2", "matrix": [0, 1], "x": 2.25, "y": 0}, {"label": "F3", "matrix": [1, 1], "x": 3.25, "y": 0}, {"label": "F4", "matrix": [0, 2], "x": 4.25, "y": 0}, {"label": "F5", "matrix": [1, 2], "x": 5.5, "y": 0}, {"label": "F6", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F7", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F8", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F9", "matrix": [1, 4], "x": 9.75, "y": 0}, {"label": "F10", "matrix": [0, 5], "x": 10.75, "y": 0}, {"label": "F11", "matrix": [1, 5], "x": 11.75, "y": 0}, {"label": "F12", "matrix": [0, 6], "x": 12.75, "y": 0}, {"label": "F13", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "`", "matrix": [3, 6], "x": 13, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 14, "y": 1.25}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "#", "matrix": [6, 6], "x": 12.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 13.75, "y": 2.25, "w": 1.25, "h": 2}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 1.25}, {"label": "\\", "matrix": [9, 0], "x": 1.25, "y": 4.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 1.75}, {"label": "Fn", "matrix": [9, 6], "x": 14, "y": 4.25}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 1], "x": 1.25, "y": 5.25, "w": 1.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.25}, {"label": "Space", "matrix": [10, 3], "x": 3.75, "y": 5.25, "w": 6.25}, {"label": "Alt", "matrix": [10, 4], "x": 10, "y": 5.25, "w": 1.25}, {"label": "GUI", "matrix": [11, 4], "x": 11.25, "y": 5.25, "w": 1.25}, {"label": "<PERSON><PERSON>", "matrix": [10, 5], "x": 12.5, "y": 5.25, "w": 1.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.75, "y": 5.25, "w": 1.25}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_f13_iso_tsangan": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [1, 0], "x": 1.25, "y": 0}, {"label": "F2", "matrix": [0, 1], "x": 2.25, "y": 0}, {"label": "F3", "matrix": [1, 1], "x": 3.25, "y": 0}, {"label": "F4", "matrix": [0, 2], "x": 4.25, "y": 0}, {"label": "F5", "matrix": [1, 2], "x": 5.5, "y": 0}, {"label": "F6", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F7", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F8", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F9", "matrix": [1, 4], "x": 9.75, "y": 0}, {"label": "F10", "matrix": [0, 5], "x": 10.75, "y": 0}, {"label": "F11", "matrix": [1, 5], "x": 11.75, "y": 0}, {"label": "F12", "matrix": [0, 6], "x": 12.75, "y": 0}, {"label": "F13", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 13, "y": 1.25, "w": 2}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "#", "matrix": [6, 6], "x": 12.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 13.75, "y": 2.25, "w": 1.25, "h": 2}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 1.25}, {"label": "\\", "matrix": [9, 0], "x": 1.25, "y": 4.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 2.75}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.5}, {"label": "GUI", "matrix": [11, 1], "x": 1.5, "y": 5.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.5}, {"label": "Space", "matrix": [10, 3], "x": 4, "y": 5.25, "w": 7}, {"label": "Alt", "matrix": [11, 4], "x": 11, "y": 5.25, "w": 1.5}, {"label": "<PERSON><PERSON>", "matrix": [10, 5], "x": 12.5, "y": 5.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.5, "y": 5.25, "w": 1.5}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}, "LAYOUT_tkl_f13_iso_tsangan_split_bs_rshift": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "F1", "matrix": [1, 0], "x": 1.25, "y": 0}, {"label": "F2", "matrix": [0, 1], "x": 2.25, "y": 0}, {"label": "F3", "matrix": [1, 1], "x": 3.25, "y": 0}, {"label": "F4", "matrix": [0, 2], "x": 4.25, "y": 0}, {"label": "F5", "matrix": [1, 2], "x": 5.5, "y": 0}, {"label": "F6", "matrix": [0, 3], "x": 6.5, "y": 0}, {"label": "F7", "matrix": [1, 3], "x": 7.5, "y": 0}, {"label": "F8", "matrix": [0, 4], "x": 8.5, "y": 0}, {"label": "F9", "matrix": [1, 4], "x": 9.75, "y": 0}, {"label": "F10", "matrix": [0, 5], "x": 10.75, "y": 0}, {"label": "F11", "matrix": [1, 5], "x": 11.75, "y": 0}, {"label": "F12", "matrix": [0, 6], "x": 12.75, "y": 0}, {"label": "F13", "matrix": [1, 6], "x": 14, "y": 0}, {"label": "Print Screen", "matrix": [0, 7], "x": 15.25, "y": 0}, {"label": "<PERSON><PERSON> Lock", "matrix": [1, 7], "x": 16.25, "y": 0}, {"label": "Pause", "matrix": [3, 7], "x": 17.25, "y": 0}, {"label": "`", "matrix": [2, 0], "x": 0, "y": 1.25}, {"label": "1", "matrix": [3, 0], "x": 1, "y": 1.25}, {"label": "2", "matrix": [2, 1], "x": 2, "y": 1.25}, {"label": "3", "matrix": [3, 1], "x": 3, "y": 1.25}, {"label": "4", "matrix": [2, 2], "x": 4, "y": 1.25}, {"label": "5", "matrix": [3, 2], "x": 5, "y": 1.25}, {"label": "6", "matrix": [2, 3], "x": 6, "y": 1.25}, {"label": "7", "matrix": [3, 3], "x": 7, "y": 1.25}, {"label": "8", "matrix": [2, 4], "x": 8, "y": 1.25}, {"label": "9", "matrix": [3, 4], "x": 9, "y": 1.25}, {"label": "0", "matrix": [2, 5], "x": 10, "y": 1.25}, {"label": "-", "matrix": [3, 5], "x": 11, "y": 1.25}, {"label": "=", "matrix": [2, 6], "x": 12, "y": 1.25}, {"label": "`", "matrix": [3, 6], "x": 13, "y": 1.25}, {"label": "Backspace", "matrix": [6, 7], "x": 14, "y": 1.25}, {"label": "Insert", "matrix": [2, 7], "x": 15.25, "y": 1.25}, {"label": "Home", "matrix": [5, 7], "x": 16.25, "y": 1.25}, {"label": "Page Up", "matrix": [9, 7], "x": 17.25, "y": 1.25}, {"label": "Tab", "matrix": [4, 0], "x": 0, "y": 2.25, "w": 1.5}, {"label": "Q", "matrix": [5, 0], "x": 1.5, "y": 2.25}, {"label": "W", "matrix": [4, 1], "x": 2.5, "y": 2.25}, {"label": "E", "matrix": [5, 1], "x": 3.5, "y": 2.25}, {"label": "R", "matrix": [4, 2], "x": 4.5, "y": 2.25}, {"label": "T", "matrix": [5, 2], "x": 5.5, "y": 2.25}, {"label": "Y", "matrix": [4, 3], "x": 6.5, "y": 2.25}, {"label": "U", "matrix": [5, 3], "x": 7.5, "y": 2.25}, {"label": "I", "matrix": [4, 4], "x": 8.5, "y": 2.25}, {"label": "O", "matrix": [5, 4], "x": 9.5, "y": 2.25}, {"label": "P", "matrix": [4, 5], "x": 10.5, "y": 2.25}, {"label": "[", "matrix": [5, 5], "x": 11.5, "y": 2.25}, {"label": "]", "matrix": [4, 6], "x": 12.5, "y": 2.25}, {"label": "Delete", "matrix": [4, 7], "x": 15.25, "y": 2.25}, {"label": "End", "matrix": [7, 7], "x": 16.25, "y": 2.25}, {"label": "Page Down", "matrix": [11, 7], "x": 17.25, "y": 2.25}, {"label": "Caps Lock", "matrix": [6, 0], "x": 0, "y": 3.25, "w": 1.75}, {"label": "A", "matrix": [7, 0], "x": 1.75, "y": 3.25}, {"label": "S", "matrix": [6, 1], "x": 2.75, "y": 3.25}, {"label": "D", "matrix": [7, 1], "x": 3.75, "y": 3.25}, {"label": "F", "matrix": [6, 2], "x": 4.75, "y": 3.25}, {"label": "G", "matrix": [7, 2], "x": 5.75, "y": 3.25}, {"label": "H", "matrix": [6, 3], "x": 6.75, "y": 3.25}, {"label": "J", "matrix": [7, 3], "x": 7.75, "y": 3.25}, {"label": "K", "matrix": [6, 4], "x": 8.75, "y": 3.25}, {"label": "L", "matrix": [7, 4], "x": 9.75, "y": 3.25}, {"label": ";", "matrix": [6, 5], "x": 10.75, "y": 3.25}, {"label": "'", "matrix": [7, 5], "x": 11.75, "y": 3.25}, {"label": "#", "matrix": [6, 6], "x": 12.75, "y": 3.25}, {"label": "Enter", "matrix": [7, 6], "x": 13.75, "y": 2.25, "w": 1.25, "h": 2}, {"label": "Shift", "matrix": [8, 0], "x": 0, "y": 4.25, "w": 1.25}, {"label": "\\", "matrix": [9, 0], "x": 1.25, "y": 4.25}, {"label": "Z", "matrix": [8, 1], "x": 2.25, "y": 4.25}, {"label": "X", "matrix": [9, 1], "x": 3.25, "y": 4.25}, {"label": "C", "matrix": [8, 2], "x": 4.25, "y": 4.25}, {"label": "V", "matrix": [9, 2], "x": 5.25, "y": 4.25}, {"label": "B", "matrix": [8, 3], "x": 6.25, "y": 4.25}, {"label": "N", "matrix": [9, 3], "x": 7.25, "y": 4.25}, {"label": "M", "matrix": [8, 4], "x": 8.25, "y": 4.25}, {"label": ",", "matrix": [9, 4], "x": 9.25, "y": 4.25}, {"label": ".", "matrix": [8, 5], "x": 10.25, "y": 4.25}, {"label": "/", "matrix": [9, 5], "x": 11.25, "y": 4.25}, {"label": "Shift", "matrix": [8, 6], "x": 12.25, "y": 4.25, "w": 1.75}, {"label": "Fn", "matrix": [9, 6], "x": 14, "y": 4.25}, {"label": "↑", "matrix": [8, 7], "x": 16.25, "y": 4.25}, {"label": "Ctrl", "matrix": [10, 0], "x": 0, "y": 5.25, "w": 1.5}, {"label": "GUI", "matrix": [11, 1], "x": 1.5, "y": 5.25}, {"label": "Alt", "matrix": [10, 1], "x": 2.5, "y": 5.25, "w": 1.5}, {"label": "Space", "matrix": [10, 3], "x": 4, "y": 5.25, "w": 7}, {"label": "Alt", "matrix": [11, 4], "x": 11, "y": 5.25, "w": 1.5}, {"label": "<PERSON><PERSON>", "matrix": [10, 5], "x": 12.5, "y": 5.25}, {"label": "Ctrl", "matrix": [11, 5], "x": 13.5, "y": 5.25, "w": 1.5}, {"label": "←", "matrix": [10, 6], "x": 15.25, "y": 5.25}, {"label": "↓", "matrix": [11, 6], "x": 16.25, "y": 5.25}, {"label": "→", "matrix": [10, 7], "x": 17.25, "y": 5.25}]}}}