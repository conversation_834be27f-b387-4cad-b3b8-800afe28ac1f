{"keyboard_name": "Eagle Viper REP Rev B", "manufacturer": "4pplet", "url": "https://github.com/4pplet/eagle_viper_rep", "maintainer": "4pplet", "usb": {"vid": "0x4444", "pid": "0x0010", "device_version": "0.0.2"}, "matrix_pins": {"cols": ["A0", "A3", "A4", "A7", "B5", "B4", "B3"], "rows": ["A2", "A1", "B8", "A10", "C15", "A15", "B7", "B6", "C14", "C13"]}, "diode_direction": "COL2ROW", "features": {"bootmagic": true, "mousekey": true, "extrakey": true, "command": true, "nkro": true, "backlight": true, "rgblight": true}, "qmk": {"locking": {"enabled": true, "resync": true}}, "dynamic_keymap": {"layer_count": 5}, "backlight": {"pin": "A6", "levels": 6, "breathing": true}, "indicators": {"caps_lock": "B1", "num_lock": "B12", "scroll_lock": "B13"}, "rgblight": {"saturation_steps": 8, "brightness_steps": 8, "led_count": 16, "animations": {"breathing": true, "rainbow_mood": true, "rainbow_swirl": true, "snake": true, "knight": true, "christmas": true, "static_gradient": true, "rgb_test": true, "alternating": true, "twinkle": true}}, "ws2812": {"pin": "A8"}, "processor": "STM32F072", "bootloader": "stm32-dfu", "layout_aliases": {"LAYOUT": "LAYOUT_all", "LAYOUT_60_tsangan_hhkb": "LAYOUT_60_ansi_tsangan_split_bs_rshift"}, "community_layouts": ["60_ansi", "60_ansi_split_bs_rshift", "60_an<PERSON>_t<PERSON>an", "60_ansi_t<PERSON>an_split_bs_rshift", "60_hhkb", "60_iso", "60_iso_split_bs_rshift", "60_iso_tsangan", "60_iso_tsangan_split_bs_rshift"], "layouts": {"LAYOUT_all": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "1", "matrix": [1, 0], "x": 1, "y": 0}, {"label": "2", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "3", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "4", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "5", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "6", "matrix": [0, 3], "x": 6, "y": 0}, {"label": "7", "matrix": [1, 3], "x": 7, "y": 0}, {"label": "8", "matrix": [0, 4], "x": 8, "y": 0}, {"label": "9", "matrix": [1, 4], "x": 9, "y": 0}, {"label": "0", "matrix": [0, 5], "x": 10, "y": 0}, {"label": "-", "matrix": [1, 5], "x": 11, "y": 0}, {"label": "=", "matrix": [0, 6], "x": 12, "y": 0}, {"label": "\\", "matrix": [1, 6], "x": 13, "y": 0}, {"label": "`", "matrix": [3, 6], "x": 14, "y": 0}, {"label": "Tab", "matrix": [2, 0], "x": 0, "y": 1, "w": 1.5}, {"label": "Q", "matrix": [3, 0], "x": 1.5, "y": 1}, {"label": "W", "matrix": [2, 1], "x": 2.5, "y": 1}, {"label": "E", "matrix": [3, 1], "x": 3.5, "y": 1}, {"label": "R", "matrix": [2, 2], "x": 4.5, "y": 1}, {"label": "T", "matrix": [3, 2], "x": 5.5, "y": 1}, {"label": "Y", "matrix": [2, 3], "x": 6.5, "y": 1}, {"label": "U", "matrix": [3, 3], "x": 7.5, "y": 1}, {"label": "I", "matrix": [2, 4], "x": 8.5, "y": 1}, {"label": "O", "matrix": [3, 4], "x": 9.5, "y": 1}, {"label": "P", "matrix": [2, 5], "x": 10.5, "y": 1}, {"label": "[", "matrix": [3, 5], "x": 11.5, "y": 1}, {"label": "]", "matrix": [2, 6], "x": 12.5, "y": 1}, {"label": "Backspace", "matrix": [5, 6], "x": 13.5, "y": 1, "w": 1.5}, {"label": "Caps Lock", "matrix": [4, 0], "x": 0, "y": 2, "w": 1.75}, {"label": "A", "matrix": [5, 0], "x": 1.75, "y": 2}, {"label": "S", "matrix": [4, 1], "x": 2.75, "y": 2}, {"label": "D", "matrix": [5, 1], "x": 3.75, "y": 2}, {"label": "F", "matrix": [4, 2], "x": 4.75, "y": 2}, {"label": "G", "matrix": [5, 2], "x": 5.75, "y": 2}, {"label": "H", "matrix": [4, 3], "x": 6.75, "y": 2}, {"label": "J", "matrix": [5, 3], "x": 7.75, "y": 2}, {"label": "K", "matrix": [4, 4], "x": 8.75, "y": 2}, {"label": "L", "matrix": [5, 4], "x": 9.75, "y": 2}, {"label": ";", "matrix": [4, 5], "x": 10.75, "y": 2}, {"label": "'", "matrix": [5, 5], "x": 11.75, "y": 2}, {"label": "#", "matrix": [4, 6], "x": 12.75, "y": 2}, {"label": "Enter", "matrix": [7, 6], "x": 13.75, "y": 2, "w": 1.25}, {"label": "Shift", "matrix": [6, 0], "x": 0, "y": 3, "w": 1.25}, {"label": "\\", "matrix": [7, 0], "x": 1.25, "y": 3}, {"label": "Z", "matrix": [6, 1], "x": 2.25, "y": 3}, {"label": "X", "matrix": [7, 1], "x": 3.25, "y": 3}, {"label": "C", "matrix": [6, 2], "x": 4.25, "y": 3}, {"label": "V", "matrix": [7, 2], "x": 5.25, "y": 3}, {"label": "B", "matrix": [6, 3], "x": 6.25, "y": 3}, {"label": "N", "matrix": [7, 3], "x": 7.25, "y": 3}, {"label": "M", "matrix": [6, 4], "x": 8.25, "y": 3}, {"label": ",", "matrix": [7, 4], "x": 9.25, "y": 3}, {"label": ".", "matrix": [6, 5], "x": 10.25, "y": 3}, {"label": "/", "matrix": [7, 5], "x": 11.25, "y": 3}, {"label": "Shift", "matrix": [6, 6], "x": 12.25, "y": 3, "w": 1.75}, {"label": "Fn", "matrix": [9, 6], "x": 14, "y": 3}, {"label": "Ctrl", "matrix": [8, 0], "x": 0, "y": 4, "w": 1.25}, {"label": "GUI", "matrix": [9, 0], "x": 1.25, "y": 4, "w": 1.25}, {"label": "Alt", "matrix": [9, 1], "x": 2.5, "y": 4, "w": 1.25}, {"label": "Space", "matrix": [9, 2], "x": 3.75, "y": 4, "w": 2.25}, {"label": "Space", "matrix": [9, 3], "x": 6, "y": 4, "w": 1.25}, {"label": "Space", "matrix": [8, 4], "x": 7.25, "y": 4, "w": 2.75}, {"label": "Alt", "matrix": [9, 4], "x": 10, "y": 4, "w": 1.25}, {"label": "Alt", "matrix": [8, 5], "x": 11.25, "y": 4, "w": 1.25}, {"label": "GUI", "matrix": [9, 5], "x": 12.5, "y": 4, "w": 1.25}, {"label": "Fn", "matrix": [8, 6], "x": 13.75, "y": 4, "w": 1.25}]}, "LAYOUT_60_ansi": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "1", "matrix": [1, 0], "x": 1, "y": 0}, {"label": "2", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "3", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "4", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "5", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "6", "matrix": [0, 3], "x": 6, "y": 0}, {"label": "7", "matrix": [1, 3], "x": 7, "y": 0}, {"label": "8", "matrix": [0, 4], "x": 8, "y": 0}, {"label": "9", "matrix": [1, 4], "x": 9, "y": 0}, {"label": "0", "matrix": [0, 5], "x": 10, "y": 0}, {"label": "-", "matrix": [1, 5], "x": 11, "y": 0}, {"label": "=", "matrix": [0, 6], "x": 12, "y": 0}, {"label": "Backspace", "matrix": [3, 6], "x": 13, "y": 0, "w": 2}, {"label": "Tab", "matrix": [2, 0], "x": 0, "y": 1, "w": 1.5}, {"label": "Q", "matrix": [3, 0], "x": 1.5, "y": 1}, {"label": "W", "matrix": [2, 1], "x": 2.5, "y": 1}, {"label": "E", "matrix": [3, 1], "x": 3.5, "y": 1}, {"label": "R", "matrix": [2, 2], "x": 4.5, "y": 1}, {"label": "T", "matrix": [3, 2], "x": 5.5, "y": 1}, {"label": "Y", "matrix": [2, 3], "x": 6.5, "y": 1}, {"label": "U", "matrix": [3, 3], "x": 7.5, "y": 1}, {"label": "I", "matrix": [2, 4], "x": 8.5, "y": 1}, {"label": "O", "matrix": [3, 4], "x": 9.5, "y": 1}, {"label": "P", "matrix": [2, 5], "x": 10.5, "y": 1}, {"label": "[", "matrix": [3, 5], "x": 11.5, "y": 1}, {"label": "]", "matrix": [2, 6], "x": 12.5, "y": 1}, {"label": "\\", "matrix": [5, 6], "x": 13.5, "y": 1, "w": 1.5}, {"label": "Caps Lock", "matrix": [4, 0], "x": 0, "y": 2, "w": 1.75}, {"label": "A", "matrix": [5, 0], "x": 1.75, "y": 2}, {"label": "S", "matrix": [4, 1], "x": 2.75, "y": 2}, {"label": "D", "matrix": [5, 1], "x": 3.75, "y": 2}, {"label": "F", "matrix": [4, 2], "x": 4.75, "y": 2}, {"label": "G", "matrix": [5, 2], "x": 5.75, "y": 2}, {"label": "H", "matrix": [4, 3], "x": 6.75, "y": 2}, {"label": "J", "matrix": [5, 3], "x": 7.75, "y": 2}, {"label": "K", "matrix": [4, 4], "x": 8.75, "y": 2}, {"label": "L", "matrix": [5, 4], "x": 9.75, "y": 2}, {"label": ";", "matrix": [4, 5], "x": 10.75, "y": 2}, {"label": "'", "matrix": [5, 5], "x": 11.75, "y": 2}, {"label": "Enter", "matrix": [7, 6], "x": 12.75, "y": 2, "w": 2.25}, {"label": "Shift", "matrix": [6, 0], "x": 0, "y": 3, "w": 2.25}, {"label": "Z", "matrix": [6, 1], "x": 2.25, "y": 3}, {"label": "X", "matrix": [7, 1], "x": 3.25, "y": 3}, {"label": "C", "matrix": [6, 2], "x": 4.25, "y": 3}, {"label": "V", "matrix": [7, 2], "x": 5.25, "y": 3}, {"label": "B", "matrix": [6, 3], "x": 6.25, "y": 3}, {"label": "N", "matrix": [7, 3], "x": 7.25, "y": 3}, {"label": "M", "matrix": [6, 4], "x": 8.25, "y": 3}, {"label": ",", "matrix": [7, 4], "x": 9.25, "y": 3}, {"label": ".", "matrix": [6, 5], "x": 10.25, "y": 3}, {"label": "/", "matrix": [7, 5], "x": 11.25, "y": 3}, {"label": "Shift", "matrix": [6, 6], "x": 12.25, "y": 3, "w": 2.75}, {"label": "Ctrl", "matrix": [8, 0], "x": 0, "y": 4, "w": 1.25}, {"label": "GUI", "matrix": [9, 0], "x": 1.25, "y": 4, "w": 1.25}, {"label": "Alt", "matrix": [9, 1], "x": 2.5, "y": 4, "w": 1.25}, {"label": "Space", "matrix": [9, 3], "x": 3.75, "y": 4, "w": 6.25}, {"label": "Alt", "matrix": [9, 4], "x": 10, "y": 4, "w": 1.25}, {"label": "GUI", "matrix": [8, 5], "x": 11.25, "y": 4, "w": 1.25}, {"label": "<PERSON><PERSON>", "matrix": [9, 5], "x": 12.5, "y": 4, "w": 1.25}, {"label": "Ctrl", "matrix": [8, 6], "x": 13.75, "y": 4, "w": 1.25}]}, "LAYOUT_60_ansi_split_bs_rshift": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "1", "matrix": [1, 0], "x": 1, "y": 0}, {"label": "2", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "3", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "4", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "5", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "6", "matrix": [0, 3], "x": 6, "y": 0}, {"label": "7", "matrix": [1, 3], "x": 7, "y": 0}, {"label": "8", "matrix": [0, 4], "x": 8, "y": 0}, {"label": "9", "matrix": [1, 4], "x": 9, "y": 0}, {"label": "0", "matrix": [0, 5], "x": 10, "y": 0}, {"label": "-", "matrix": [1, 5], "x": 11, "y": 0}, {"label": "=", "matrix": [0, 6], "x": 12, "y": 0}, {"label": "\\", "matrix": [1, 6], "x": 13, "y": 0}, {"label": "`", "matrix": [3, 6], "x": 14, "y": 0}, {"label": "Tab", "matrix": [2, 0], "x": 0, "y": 1, "w": 1.5}, {"label": "Q", "matrix": [3, 0], "x": 1.5, "y": 1}, {"label": "W", "matrix": [2, 1], "x": 2.5, "y": 1}, {"label": "E", "matrix": [3, 1], "x": 3.5, "y": 1}, {"label": "R", "matrix": [2, 2], "x": 4.5, "y": 1}, {"label": "T", "matrix": [3, 2], "x": 5.5, "y": 1}, {"label": "Y", "matrix": [2, 3], "x": 6.5, "y": 1}, {"label": "U", "matrix": [3, 3], "x": 7.5, "y": 1}, {"label": "I", "matrix": [2, 4], "x": 8.5, "y": 1}, {"label": "O", "matrix": [3, 4], "x": 9.5, "y": 1}, {"label": "P", "matrix": [2, 5], "x": 10.5, "y": 1}, {"label": "[", "matrix": [3, 5], "x": 11.5, "y": 1}, {"label": "]", "matrix": [2, 6], "x": 12.5, "y": 1}, {"label": "Backspace", "matrix": [5, 6], "x": 13.5, "y": 1, "w": 1.5}, {"label": "Caps Lock", "matrix": [4, 0], "x": 0, "y": 2, "w": 1.75}, {"label": "A", "matrix": [5, 0], "x": 1.75, "y": 2}, {"label": "S", "matrix": [4, 1], "x": 2.75, "y": 2}, {"label": "D", "matrix": [5, 1], "x": 3.75, "y": 2}, {"label": "F", "matrix": [4, 2], "x": 4.75, "y": 2}, {"label": "G", "matrix": [5, 2], "x": 5.75, "y": 2}, {"label": "H", "matrix": [4, 3], "x": 6.75, "y": 2}, {"label": "J", "matrix": [5, 3], "x": 7.75, "y": 2}, {"label": "K", "matrix": [4, 4], "x": 8.75, "y": 2}, {"label": "L", "matrix": [5, 4], "x": 9.75, "y": 2}, {"label": ";", "matrix": [4, 5], "x": 10.75, "y": 2}, {"label": "'", "matrix": [5, 5], "x": 11.75, "y": 2}, {"label": "Enter", "matrix": [7, 6], "x": 12.75, "y": 2, "w": 2.25}, {"label": "Shift", "matrix": [6, 0], "x": 0, "y": 3, "w": 2.25}, {"label": "Z", "matrix": [6, 1], "x": 2.25, "y": 3}, {"label": "X", "matrix": [7, 1], "x": 3.25, "y": 3}, {"label": "C", "matrix": [6, 2], "x": 4.25, "y": 3}, {"label": "V", "matrix": [7, 2], "x": 5.25, "y": 3}, {"label": "B", "matrix": [6, 3], "x": 6.25, "y": 3}, {"label": "N", "matrix": [7, 3], "x": 7.25, "y": 3}, {"label": "M", "matrix": [6, 4], "x": 8.25, "y": 3}, {"label": ",", "matrix": [7, 4], "x": 9.25, "y": 3}, {"label": ".", "matrix": [6, 5], "x": 10.25, "y": 3}, {"label": "/", "matrix": [7, 5], "x": 11.25, "y": 3}, {"label": "Shift", "matrix": [6, 6], "x": 12.25, "y": 3, "w": 1.75}, {"label": "Fn", "matrix": [9, 6], "x": 14, "y": 3}, {"label": "Ctrl", "matrix": [8, 0], "x": 0, "y": 4, "w": 1.25}, {"label": "GUI", "matrix": [9, 0], "x": 1.25, "y": 4, "w": 1.25}, {"label": "Alt", "matrix": [9, 1], "x": 2.5, "y": 4, "w": 1.25}, {"label": "Space", "matrix": [9, 3], "x": 3.75, "y": 4, "w": 6.25}, {"label": "Alt", "matrix": [9, 4], "x": 10, "y": 4, "w": 1.25}, {"label": "GUI", "matrix": [8, 5], "x": 11.25, "y": 4, "w": 1.25}, {"label": "<PERSON><PERSON>", "matrix": [9, 5], "x": 12.5, "y": 4, "w": 1.25}, {"label": "Ctrl", "matrix": [8, 6], "x": 13.75, "y": 4, "w": 1.25}]}, "LAYOUT_60_ansi_tsangan": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "1", "matrix": [1, 0], "x": 1, "y": 0}, {"label": "2", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "3", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "4", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "5", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "6", "matrix": [0, 3], "x": 6, "y": 0}, {"label": "7", "matrix": [1, 3], "x": 7, "y": 0}, {"label": "8", "matrix": [0, 4], "x": 8, "y": 0}, {"label": "9", "matrix": [1, 4], "x": 9, "y": 0}, {"label": "0", "matrix": [0, 5], "x": 10, "y": 0}, {"label": "-", "matrix": [1, 5], "x": 11, "y": 0}, {"label": "=", "matrix": [0, 6], "x": 12, "y": 0}, {"label": "Backspace", "matrix": [3, 6], "x": 13, "y": 0, "w": 2}, {"label": "Tab", "matrix": [2, 0], "x": 0, "y": 1, "w": 1.5}, {"label": "Q", "matrix": [3, 0], "x": 1.5, "y": 1}, {"label": "W", "matrix": [2, 1], "x": 2.5, "y": 1}, {"label": "E", "matrix": [3, 1], "x": 3.5, "y": 1}, {"label": "R", "matrix": [2, 2], "x": 4.5, "y": 1}, {"label": "T", "matrix": [3, 2], "x": 5.5, "y": 1}, {"label": "Y", "matrix": [2, 3], "x": 6.5, "y": 1}, {"label": "U", "matrix": [3, 3], "x": 7.5, "y": 1}, {"label": "I", "matrix": [2, 4], "x": 8.5, "y": 1}, {"label": "O", "matrix": [3, 4], "x": 9.5, "y": 1}, {"label": "P", "matrix": [2, 5], "x": 10.5, "y": 1}, {"label": "[", "matrix": [3, 5], "x": 11.5, "y": 1}, {"label": "]", "matrix": [2, 6], "x": 12.5, "y": 1}, {"label": "\\", "matrix": [5, 6], "x": 13.5, "y": 1, "w": 1.5}, {"label": "Caps Lock", "matrix": [4, 0], "x": 0, "y": 2, "w": 1.75}, {"label": "A", "matrix": [5, 0], "x": 1.75, "y": 2}, {"label": "S", "matrix": [4, 1], "x": 2.75, "y": 2}, {"label": "D", "matrix": [5, 1], "x": 3.75, "y": 2}, {"label": "F", "matrix": [4, 2], "x": 4.75, "y": 2}, {"label": "G", "matrix": [5, 2], "x": 5.75, "y": 2}, {"label": "H", "matrix": [4, 3], "x": 6.75, "y": 2}, {"label": "J", "matrix": [5, 3], "x": 7.75, "y": 2}, {"label": "K", "matrix": [4, 4], "x": 8.75, "y": 2}, {"label": "L", "matrix": [5, 4], "x": 9.75, "y": 2}, {"label": ";", "matrix": [4, 5], "x": 10.75, "y": 2}, {"label": "'", "matrix": [5, 5], "x": 11.75, "y": 2}, {"label": "Enter", "matrix": [7, 6], "x": 12.75, "y": 2, "w": 2.25}, {"label": "Shift", "matrix": [6, 0], "x": 0, "y": 3, "w": 2.25}, {"label": "Z", "matrix": [6, 1], "x": 2.25, "y": 3}, {"label": "X", "matrix": [7, 1], "x": 3.25, "y": 3}, {"label": "C", "matrix": [6, 2], "x": 4.25, "y": 3}, {"label": "V", "matrix": [7, 2], "x": 5.25, "y": 3}, {"label": "B", "matrix": [6, 3], "x": 6.25, "y": 3}, {"label": "N", "matrix": [7, 3], "x": 7.25, "y": 3}, {"label": "M", "matrix": [6, 4], "x": 8.25, "y": 3}, {"label": ",", "matrix": [7, 4], "x": 9.25, "y": 3}, {"label": ".", "matrix": [6, 5], "x": 10.25, "y": 3}, {"label": "/", "matrix": [7, 5], "x": 11.25, "y": 3}, {"label": "Shift", "matrix": [6, 6], "x": 12.25, "y": 3, "w": 2.75}, {"label": "Ctrl", "matrix": [8, 0], "x": 0, "y": 4, "w": 1.5}, {"label": "GUI", "matrix": [9, 0], "x": 1.5, "y": 4}, {"label": "Alt", "matrix": [9, 1], "x": 2.5, "y": 4, "w": 1.5}, {"label": "Space", "matrix": [9, 3], "x": 4, "y": 4, "w": 7}, {"label": "Alt", "matrix": [8, 5], "x": 11, "y": 4, "w": 1.5}, {"label": "<PERSON><PERSON>", "matrix": [9, 5], "x": 12.5, "y": 4}, {"label": "Ctrl", "matrix": [8, 6], "x": 13.5, "y": 4, "w": 1.5}]}, "LAYOUT_60_ansi_tsangan_split_bs_rshift": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "1", "matrix": [1, 0], "x": 1, "y": 0}, {"label": "2", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "3", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "4", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "5", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "6", "matrix": [0, 3], "x": 6, "y": 0}, {"label": "7", "matrix": [1, 3], "x": 7, "y": 0}, {"label": "8", "matrix": [0, 4], "x": 8, "y": 0}, {"label": "9", "matrix": [1, 4], "x": 9, "y": 0}, {"label": "0", "matrix": [0, 5], "x": 10, "y": 0}, {"label": "-", "matrix": [1, 5], "x": 11, "y": 0}, {"label": "=", "matrix": [0, 6], "x": 12, "y": 0}, {"label": "\\", "matrix": [1, 6], "x": 13, "y": 0}, {"label": "`", "matrix": [3, 6], "x": 14, "y": 0}, {"label": "Tab", "matrix": [2, 0], "x": 0, "y": 1, "w": 1.5}, {"label": "Q", "matrix": [3, 0], "x": 1.5, "y": 1}, {"label": "W", "matrix": [2, 1], "x": 2.5, "y": 1}, {"label": "E", "matrix": [3, 1], "x": 3.5, "y": 1}, {"label": "R", "matrix": [2, 2], "x": 4.5, "y": 1}, {"label": "T", "matrix": [3, 2], "x": 5.5, "y": 1}, {"label": "Y", "matrix": [2, 3], "x": 6.5, "y": 1}, {"label": "U", "matrix": [3, 3], "x": 7.5, "y": 1}, {"label": "I", "matrix": [2, 4], "x": 8.5, "y": 1}, {"label": "O", "matrix": [3, 4], "x": 9.5, "y": 1}, {"label": "P", "matrix": [2, 5], "x": 10.5, "y": 1}, {"label": "[", "matrix": [3, 5], "x": 11.5, "y": 1}, {"label": "]", "matrix": [2, 6], "x": 12.5, "y": 1}, {"label": "Backspace", "matrix": [5, 6], "x": 13.5, "y": 1, "w": 1.5}, {"label": "Caps Lock", "matrix": [4, 0], "x": 0, "y": 2, "w": 1.75}, {"label": "A", "matrix": [5, 0], "x": 1.75, "y": 2}, {"label": "S", "matrix": [4, 1], "x": 2.75, "y": 2}, {"label": "D", "matrix": [5, 1], "x": 3.75, "y": 2}, {"label": "F", "matrix": [4, 2], "x": 4.75, "y": 2}, {"label": "G", "matrix": [5, 2], "x": 5.75, "y": 2}, {"label": "H", "matrix": [4, 3], "x": 6.75, "y": 2}, {"label": "J", "matrix": [5, 3], "x": 7.75, "y": 2}, {"label": "K", "matrix": [4, 4], "x": 8.75, "y": 2}, {"label": "L", "matrix": [5, 4], "x": 9.75, "y": 2}, {"label": ";", "matrix": [4, 5], "x": 10.75, "y": 2}, {"label": "'", "matrix": [5, 5], "x": 11.75, "y": 2}, {"label": "Enter", "matrix": [7, 6], "x": 12.75, "y": 2, "w": 2.25}, {"label": "Shift", "matrix": [6, 0], "x": 0, "y": 3, "w": 2.25}, {"label": "Z", "matrix": [6, 1], "x": 2.25, "y": 3}, {"label": "X", "matrix": [7, 1], "x": 3.25, "y": 3}, {"label": "C", "matrix": [6, 2], "x": 4.25, "y": 3}, {"label": "V", "matrix": [7, 2], "x": 5.25, "y": 3}, {"label": "B", "matrix": [6, 3], "x": 6.25, "y": 3}, {"label": "N", "matrix": [7, 3], "x": 7.25, "y": 3}, {"label": "M", "matrix": [6, 4], "x": 8.25, "y": 3}, {"label": ",", "matrix": [7, 4], "x": 9.25, "y": 3}, {"label": ".", "matrix": [6, 5], "x": 10.25, "y": 3}, {"label": "/", "matrix": [7, 5], "x": 11.25, "y": 3}, {"label": "Shift", "matrix": [6, 6], "x": 12.25, "y": 3, "w": 1.75}, {"label": "Fn", "matrix": [9, 6], "x": 14, "y": 3}, {"label": "Ctrl", "matrix": [8, 0], "x": 0, "y": 4, "w": 1.5}, {"label": "GUI", "matrix": [9, 0], "x": 1.5, "y": 4}, {"label": "Alt", "matrix": [9, 1], "x": 2.5, "y": 4, "w": 1.5}, {"label": "Space", "matrix": [9, 3], "x": 4, "y": 4, "w": 7}, {"label": "Alt", "matrix": [8, 5], "x": 11, "y": 4, "w": 1.5}, {"label": "<PERSON><PERSON>", "matrix": [9, 5], "x": 12.5, "y": 4}, {"label": "Ctrl", "matrix": [8, 6], "x": 13.5, "y": 4, "w": 1.5}]}, "LAYOUT_60_hhkb": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "1", "matrix": [1, 0], "x": 1, "y": 0}, {"label": "2", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "3", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "4", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "5", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "6", "matrix": [0, 3], "x": 6, "y": 0}, {"label": "7", "matrix": [1, 3], "x": 7, "y": 0}, {"label": "8", "matrix": [0, 4], "x": 8, "y": 0}, {"label": "9", "matrix": [1, 4], "x": 9, "y": 0}, {"label": "0", "matrix": [0, 5], "x": 10, "y": 0}, {"label": "-", "matrix": [1, 5], "x": 11, "y": 0}, {"label": "=", "matrix": [0, 6], "x": 12, "y": 0}, {"label": "\\", "matrix": [1, 6], "x": 13, "y": 0}, {"label": "`", "matrix": [3, 6], "x": 14, "y": 0}, {"label": "Tab", "matrix": [2, 0], "x": 0, "y": 1, "w": 1.5}, {"label": "Q", "matrix": [3, 0], "x": 1.5, "y": 1}, {"label": "W", "matrix": [2, 1], "x": 2.5, "y": 1}, {"label": "E", "matrix": [3, 1], "x": 3.5, "y": 1}, {"label": "R", "matrix": [2, 2], "x": 4.5, "y": 1}, {"label": "T", "matrix": [3, 2], "x": 5.5, "y": 1}, {"label": "Y", "matrix": [2, 3], "x": 6.5, "y": 1}, {"label": "U", "matrix": [3, 3], "x": 7.5, "y": 1}, {"label": "I", "matrix": [2, 4], "x": 8.5, "y": 1}, {"label": "O", "matrix": [3, 4], "x": 9.5, "y": 1}, {"label": "P", "matrix": [2, 5], "x": 10.5, "y": 1}, {"label": "[", "matrix": [3, 5], "x": 11.5, "y": 1}, {"label": "]", "matrix": [2, 6], "x": 12.5, "y": 1}, {"label": "Backspace", "matrix": [5, 6], "x": 13.5, "y": 1, "w": 1.5}, {"label": "Caps Lock", "matrix": [4, 0], "x": 0, "y": 2, "w": 1.75}, {"label": "A", "matrix": [5, 0], "x": 1.75, "y": 2}, {"label": "S", "matrix": [4, 1], "x": 2.75, "y": 2}, {"label": "D", "matrix": [5, 1], "x": 3.75, "y": 2}, {"label": "F", "matrix": [4, 2], "x": 4.75, "y": 2}, {"label": "G", "matrix": [5, 2], "x": 5.75, "y": 2}, {"label": "H", "matrix": [4, 3], "x": 6.75, "y": 2}, {"label": "J", "matrix": [5, 3], "x": 7.75, "y": 2}, {"label": "K", "matrix": [4, 4], "x": 8.75, "y": 2}, {"label": "L", "matrix": [5, 4], "x": 9.75, "y": 2}, {"label": ";", "matrix": [4, 5], "x": 10.75, "y": 2}, {"label": "'", "matrix": [5, 5], "x": 11.75, "y": 2}, {"label": "Enter", "matrix": [7, 6], "x": 12.75, "y": 2, "w": 2.25}, {"label": "Shift", "matrix": [6, 0], "x": 0, "y": 3, "w": 2.25}, {"label": "Z", "matrix": [6, 1], "x": 2.25, "y": 3}, {"label": "X", "matrix": [7, 1], "x": 3.25, "y": 3}, {"label": "C", "matrix": [6, 2], "x": 4.25, "y": 3}, {"label": "V", "matrix": [7, 2], "x": 5.25, "y": 3}, {"label": "B", "matrix": [6, 3], "x": 6.25, "y": 3}, {"label": "N", "matrix": [7, 3], "x": 7.25, "y": 3}, {"label": "M", "matrix": [6, 4], "x": 8.25, "y": 3}, {"label": ",", "matrix": [7, 4], "x": 9.25, "y": 3}, {"label": ".", "matrix": [6, 5], "x": 10.25, "y": 3}, {"label": "/", "matrix": [7, 5], "x": 11.25, "y": 3}, {"label": "Shift", "matrix": [6, 6], "x": 12.25, "y": 3, "w": 1.75}, {"label": "Fn", "matrix": [9, 6], "x": 14, "y": 3}, {"label": "Alt", "matrix": [9, 0], "x": 1.5, "y": 4}, {"label": "GUI", "matrix": [9, 1], "x": 2.5, "y": 4, "w": 1.5}, {"label": "Space", "matrix": [9, 3], "x": 4, "y": 4, "w": 7}, {"label": "GUI", "matrix": [8, 5], "x": 11, "y": 4, "w": 1.5}, {"label": "Alt", "matrix": [9, 5], "x": 12.5, "y": 4}]}, "LAYOUT_60_iso": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "1", "matrix": [1, 0], "x": 1, "y": 0}, {"label": "2", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "3", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "4", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "5", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "6", "matrix": [0, 3], "x": 6, "y": 0}, {"label": "7", "matrix": [1, 3], "x": 7, "y": 0}, {"label": "8", "matrix": [0, 4], "x": 8, "y": 0}, {"label": "9", "matrix": [1, 4], "x": 9, "y": 0}, {"label": "0", "matrix": [0, 5], "x": 10, "y": 0}, {"label": "-", "matrix": [1, 5], "x": 11, "y": 0}, {"label": "=", "matrix": [0, 6], "x": 12, "y": 0}, {"label": "Backspace", "matrix": [3, 6], "x": 13, "y": 0, "w": 2}, {"label": "Tab", "matrix": [2, 0], "x": 0, "y": 1, "w": 1.5}, {"label": "Q", "matrix": [3, 0], "x": 1.5, "y": 1}, {"label": "W", "matrix": [2, 1], "x": 2.5, "y": 1}, {"label": "E", "matrix": [3, 1], "x": 3.5, "y": 1}, {"label": "R", "matrix": [2, 2], "x": 4.5, "y": 1}, {"label": "T", "matrix": [3, 2], "x": 5.5, "y": 1}, {"label": "Y", "matrix": [2, 3], "x": 6.5, "y": 1}, {"label": "U", "matrix": [3, 3], "x": 7.5, "y": 1}, {"label": "I", "matrix": [2, 4], "x": 8.5, "y": 1}, {"label": "O", "matrix": [3, 4], "x": 9.5, "y": 1}, {"label": "P", "matrix": [2, 5], "x": 10.5, "y": 1}, {"label": "[", "matrix": [3, 5], "x": 11.5, "y": 1}, {"label": "]", "matrix": [2, 6], "x": 12.5, "y": 1}, {"label": "Caps Lock", "matrix": [4, 0], "x": 0, "y": 2, "w": 1.75}, {"label": "A", "matrix": [5, 0], "x": 1.75, "y": 2}, {"label": "S", "matrix": [4, 1], "x": 2.75, "y": 2}, {"label": "D", "matrix": [5, 1], "x": 3.75, "y": 2}, {"label": "F", "matrix": [4, 2], "x": 4.75, "y": 2}, {"label": "G", "matrix": [5, 2], "x": 5.75, "y": 2}, {"label": "H", "matrix": [4, 3], "x": 6.75, "y": 2}, {"label": "J", "matrix": [5, 3], "x": 7.75, "y": 2}, {"label": "K", "matrix": [4, 4], "x": 8.75, "y": 2}, {"label": "L", "matrix": [5, 4], "x": 9.75, "y": 2}, {"label": ";", "matrix": [4, 5], "x": 10.75, "y": 2}, {"label": "'", "matrix": [5, 5], "x": 11.75, "y": 2}, {"label": "#", "matrix": [4, 6], "x": 12.75, "y": 2}, {"label": "Enter", "matrix": [7, 6], "x": 13.75, "y": 1, "w": 1.25, "h": 2}, {"label": "Shift", "matrix": [6, 0], "x": 0, "y": 3, "w": 1.25}, {"label": "\\", "matrix": [7, 0], "x": 1.25, "y": 3}, {"label": "Z", "matrix": [6, 1], "x": 2.25, "y": 3}, {"label": "X", "matrix": [7, 1], "x": 3.25, "y": 3}, {"label": "C", "matrix": [6, 2], "x": 4.25, "y": 3}, {"label": "V", "matrix": [7, 2], "x": 5.25, "y": 3}, {"label": "B", "matrix": [6, 3], "x": 6.25, "y": 3}, {"label": "N", "matrix": [7, 3], "x": 7.25, "y": 3}, {"label": "M", "matrix": [6, 4], "x": 8.25, "y": 3}, {"label": ",", "matrix": [7, 4], "x": 9.25, "y": 3}, {"label": ".", "matrix": [6, 5], "x": 10.25, "y": 3}, {"label": "/", "matrix": [7, 5], "x": 11.25, "y": 3}, {"label": "Shift", "matrix": [6, 6], "x": 12.25, "y": 3, "w": 2.75}, {"label": "Ctrl", "matrix": [8, 0], "x": 0, "y": 4, "w": 1.25}, {"label": "GUI", "matrix": [9, 0], "x": 1.25, "y": 4, "w": 1.25}, {"label": "Alt", "matrix": [9, 1], "x": 2.5, "y": 4, "w": 1.25}, {"label": "Space", "matrix": [9, 3], "x": 3.75, "y": 4, "w": 6.25}, {"label": "Alt", "matrix": [9, 4], "x": 10, "y": 4, "w": 1.25}, {"label": "GUI", "matrix": [8, 5], "x": 11.25, "y": 4, "w": 1.25}, {"label": "<PERSON><PERSON>", "matrix": [9, 5], "x": 12.5, "y": 4, "w": 1.25}, {"label": "Ctrl", "matrix": [8, 6], "x": 13.75, "y": 4, "w": 1.25}]}, "LAYOUT_60_iso_split_bs_rshift": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "1", "matrix": [1, 0], "x": 1, "y": 0}, {"label": "2", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "3", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "4", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "5", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "6", "matrix": [0, 3], "x": 6, "y": 0}, {"label": "7", "matrix": [1, 3], "x": 7, "y": 0}, {"label": "8", "matrix": [0, 4], "x": 8, "y": 0}, {"label": "9", "matrix": [1, 4], "x": 9, "y": 0}, {"label": "0", "matrix": [0, 5], "x": 10, "y": 0}, {"label": "-", "matrix": [1, 5], "x": 11, "y": 0}, {"label": "=", "matrix": [0, 6], "x": 12, "y": 0}, {"label": "\\", "matrix": [1, 6], "x": 13, "y": 0}, {"label": "`", "matrix": [3, 6], "x": 14, "y": 0}, {"label": "Tab", "matrix": [2, 0], "x": 0, "y": 1, "w": 1.5}, {"label": "Q", "matrix": [3, 0], "x": 1.5, "y": 1}, {"label": "W", "matrix": [2, 1], "x": 2.5, "y": 1}, {"label": "E", "matrix": [3, 1], "x": 3.5, "y": 1}, {"label": "R", "matrix": [2, 2], "x": 4.5, "y": 1}, {"label": "T", "matrix": [3, 2], "x": 5.5, "y": 1}, {"label": "Y", "matrix": [2, 3], "x": 6.5, "y": 1}, {"label": "U", "matrix": [3, 3], "x": 7.5, "y": 1}, {"label": "I", "matrix": [2, 4], "x": 8.5, "y": 1}, {"label": "O", "matrix": [3, 4], "x": 9.5, "y": 1}, {"label": "P", "matrix": [2, 5], "x": 10.5, "y": 1}, {"label": "[", "matrix": [3, 5], "x": 11.5, "y": 1}, {"label": "]", "matrix": [2, 6], "x": 12.5, "y": 1}, {"label": "Caps Lock", "matrix": [4, 0], "x": 0, "y": 2, "w": 1.75}, {"label": "A", "matrix": [5, 0], "x": 1.75, "y": 2}, {"label": "S", "matrix": [4, 1], "x": 2.75, "y": 2}, {"label": "D", "matrix": [5, 1], "x": 3.75, "y": 2}, {"label": "F", "matrix": [4, 2], "x": 4.75, "y": 2}, {"label": "G", "matrix": [5, 2], "x": 5.75, "y": 2}, {"label": "H", "matrix": [4, 3], "x": 6.75, "y": 2}, {"label": "J", "matrix": [5, 3], "x": 7.75, "y": 2}, {"label": "K", "matrix": [4, 4], "x": 8.75, "y": 2}, {"label": "L", "matrix": [5, 4], "x": 9.75, "y": 2}, {"label": ";", "matrix": [4, 5], "x": 10.75, "y": 2}, {"label": "'", "matrix": [5, 5], "x": 11.75, "y": 2}, {"label": "#", "matrix": [4, 6], "x": 12.75, "y": 2}, {"label": "Enter", "matrix": [7, 6], "x": 13.75, "y": 1, "w": 1.25, "h": 2}, {"label": "Shift", "matrix": [6, 0], "x": 0, "y": 3, "w": 1.25}, {"label": "\\", "matrix": [7, 0], "x": 1.25, "y": 3}, {"label": "Z", "matrix": [6, 1], "x": 2.25, "y": 3}, {"label": "X", "matrix": [7, 1], "x": 3.25, "y": 3}, {"label": "C", "matrix": [6, 2], "x": 4.25, "y": 3}, {"label": "V", "matrix": [7, 2], "x": 5.25, "y": 3}, {"label": "B", "matrix": [6, 3], "x": 6.25, "y": 3}, {"label": "N", "matrix": [7, 3], "x": 7.25, "y": 3}, {"label": "M", "matrix": [6, 4], "x": 8.25, "y": 3}, {"label": ",", "matrix": [7, 4], "x": 9.25, "y": 3}, {"label": ".", "matrix": [6, 5], "x": 10.25, "y": 3}, {"label": "/", "matrix": [7, 5], "x": 11.25, "y": 3}, {"label": "Shift", "matrix": [6, 6], "x": 12.25, "y": 3, "w": 1.75}, {"label": "Fn", "matrix": [9, 6], "x": 14, "y": 3}, {"label": "Ctrl", "matrix": [8, 0], "x": 0, "y": 4, "w": 1.25}, {"label": "GUI", "matrix": [9, 0], "x": 1.25, "y": 4, "w": 1.25}, {"label": "Alt", "matrix": [9, 1], "x": 2.5, "y": 4, "w": 1.25}, {"label": "Space", "matrix": [9, 3], "x": 3.75, "y": 4, "w": 6.25}, {"label": "Alt", "matrix": [9, 4], "x": 10, "y": 4, "w": 1.25}, {"label": "GUI", "matrix": [8, 5], "x": 11.25, "y": 4, "w": 1.25}, {"label": "<PERSON><PERSON>", "matrix": [9, 5], "x": 12.5, "y": 4, "w": 1.25}, {"label": "Ctrl", "matrix": [8, 6], "x": 13.75, "y": 4, "w": 1.25}]}, "LAYOUT_60_iso_tsangan": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "1", "matrix": [1, 0], "x": 1, "y": 0}, {"label": "2", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "3", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "4", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "5", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "6", "matrix": [0, 3], "x": 6, "y": 0}, {"label": "7", "matrix": [1, 3], "x": 7, "y": 0}, {"label": "8", "matrix": [0, 4], "x": 8, "y": 0}, {"label": "9", "matrix": [1, 4], "x": 9, "y": 0}, {"label": "0", "matrix": [0, 5], "x": 10, "y": 0}, {"label": "-", "matrix": [1, 5], "x": 11, "y": 0}, {"label": "=", "matrix": [0, 6], "x": 12, "y": 0}, {"label": "Backspace", "matrix": [3, 6], "x": 13, "y": 0, "w": 2}, {"label": "Tab", "matrix": [2, 0], "x": 0, "y": 1, "w": 1.5}, {"label": "Q", "matrix": [3, 0], "x": 1.5, "y": 1}, {"label": "W", "matrix": [2, 1], "x": 2.5, "y": 1}, {"label": "E", "matrix": [3, 1], "x": 3.5, "y": 1}, {"label": "R", "matrix": [2, 2], "x": 4.5, "y": 1}, {"label": "T", "matrix": [3, 2], "x": 5.5, "y": 1}, {"label": "Y", "matrix": [2, 3], "x": 6.5, "y": 1}, {"label": "U", "matrix": [3, 3], "x": 7.5, "y": 1}, {"label": "I", "matrix": [2, 4], "x": 8.5, "y": 1}, {"label": "O", "matrix": [3, 4], "x": 9.5, "y": 1}, {"label": "P", "matrix": [2, 5], "x": 10.5, "y": 1}, {"label": "[", "matrix": [3, 5], "x": 11.5, "y": 1}, {"label": "]", "matrix": [2, 6], "x": 12.5, "y": 1}, {"label": "Caps Lock", "matrix": [4, 0], "x": 0, "y": 2, "w": 1.75}, {"label": "A", "matrix": [5, 0], "x": 1.75, "y": 2}, {"label": "S", "matrix": [4, 1], "x": 2.75, "y": 2}, {"label": "D", "matrix": [5, 1], "x": 3.75, "y": 2}, {"label": "F", "matrix": [4, 2], "x": 4.75, "y": 2}, {"label": "G", "matrix": [5, 2], "x": 5.75, "y": 2}, {"label": "H", "matrix": [4, 3], "x": 6.75, "y": 2}, {"label": "J", "matrix": [5, 3], "x": 7.75, "y": 2}, {"label": "K", "matrix": [4, 4], "x": 8.75, "y": 2}, {"label": "L", "matrix": [5, 4], "x": 9.75, "y": 2}, {"label": ";", "matrix": [4, 5], "x": 10.75, "y": 2}, {"label": "'", "matrix": [5, 5], "x": 11.75, "y": 2}, {"label": "#", "matrix": [4, 6], "x": 12.75, "y": 2}, {"label": "Enter", "matrix": [7, 6], "x": 13.75, "y": 1, "w": 1.25, "h": 2}, {"label": "Shift", "matrix": [6, 0], "x": 0, "y": 3, "w": 1.25}, {"label": "\\", "matrix": [7, 0], "x": 1.25, "y": 3}, {"label": "Z", "matrix": [6, 1], "x": 2.25, "y": 3}, {"label": "X", "matrix": [7, 1], "x": 3.25, "y": 3}, {"label": "C", "matrix": [6, 2], "x": 4.25, "y": 3}, {"label": "V", "matrix": [7, 2], "x": 5.25, "y": 3}, {"label": "B", "matrix": [6, 3], "x": 6.25, "y": 3}, {"label": "N", "matrix": [7, 3], "x": 7.25, "y": 3}, {"label": "M", "matrix": [6, 4], "x": 8.25, "y": 3}, {"label": ",", "matrix": [7, 4], "x": 9.25, "y": 3}, {"label": ".", "matrix": [6, 5], "x": 10.25, "y": 3}, {"label": "/", "matrix": [7, 5], "x": 11.25, "y": 3}, {"label": "Shift", "matrix": [6, 6], "x": 12.25, "y": 3, "w": 2.75}, {"label": "Ctrl", "matrix": [8, 0], "x": 0, "y": 4, "w": 1.5}, {"label": "GUI", "matrix": [9, 0], "x": 1.5, "y": 4}, {"label": "Alt", "matrix": [9, 1], "x": 2.5, "y": 4, "w": 1.5}, {"label": "Space", "matrix": [9, 3], "x": 4, "y": 4, "w": 7}, {"label": "Alt", "matrix": [8, 5], "x": 11, "y": 4, "w": 1.5}, {"label": "<PERSON><PERSON>", "matrix": [9, 5], "x": 12.5, "y": 4}, {"label": "Ctrl", "matrix": [8, 6], "x": 13.5, "y": 4, "w": 1.5}]}, "LAYOUT_60_iso_tsangan_split_bs_rshift": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "1", "matrix": [1, 0], "x": 1, "y": 0}, {"label": "2", "matrix": [0, 1], "x": 2, "y": 0}, {"label": "3", "matrix": [1, 1], "x": 3, "y": 0}, {"label": "4", "matrix": [0, 2], "x": 4, "y": 0}, {"label": "5", "matrix": [1, 2], "x": 5, "y": 0}, {"label": "6", "matrix": [0, 3], "x": 6, "y": 0}, {"label": "7", "matrix": [1, 3], "x": 7, "y": 0}, {"label": "8", "matrix": [0, 4], "x": 8, "y": 0}, {"label": "9", "matrix": [1, 4], "x": 9, "y": 0}, {"label": "0", "matrix": [0, 5], "x": 10, "y": 0}, {"label": "-", "matrix": [1, 5], "x": 11, "y": 0}, {"label": "=", "matrix": [0, 6], "x": 12, "y": 0}, {"label": "\\", "matrix": [1, 6], "x": 13, "y": 0}, {"label": "`", "matrix": [3, 6], "x": 14, "y": 0}, {"label": "Tab", "matrix": [2, 0], "x": 0, "y": 1, "w": 1.5}, {"label": "Q", "matrix": [3, 0], "x": 1.5, "y": 1}, {"label": "W", "matrix": [2, 1], "x": 2.5, "y": 1}, {"label": "E", "matrix": [3, 1], "x": 3.5, "y": 1}, {"label": "R", "matrix": [2, 2], "x": 4.5, "y": 1}, {"label": "T", "matrix": [3, 2], "x": 5.5, "y": 1}, {"label": "Y", "matrix": [2, 3], "x": 6.5, "y": 1}, {"label": "U", "matrix": [3, 3], "x": 7.5, "y": 1}, {"label": "I", "matrix": [2, 4], "x": 8.5, "y": 1}, {"label": "O", "matrix": [3, 4], "x": 9.5, "y": 1}, {"label": "P", "matrix": [2, 5], "x": 10.5, "y": 1}, {"label": "[", "matrix": [3, 5], "x": 11.5, "y": 1}, {"label": "]", "matrix": [2, 6], "x": 12.5, "y": 1}, {"label": "Caps Lock", "matrix": [4, 0], "x": 0, "y": 2, "w": 1.75}, {"label": "A", "matrix": [5, 0], "x": 1.75, "y": 2}, {"label": "S", "matrix": [4, 1], "x": 2.75, "y": 2}, {"label": "D", "matrix": [5, 1], "x": 3.75, "y": 2}, {"label": "F", "matrix": [4, 2], "x": 4.75, "y": 2}, {"label": "G", "matrix": [5, 2], "x": 5.75, "y": 2}, {"label": "H", "matrix": [4, 3], "x": 6.75, "y": 2}, {"label": "J", "matrix": [5, 3], "x": 7.75, "y": 2}, {"label": "K", "matrix": [4, 4], "x": 8.75, "y": 2}, {"label": "L", "matrix": [5, 4], "x": 9.75, "y": 2}, {"label": ";", "matrix": [4, 5], "x": 10.75, "y": 2}, {"label": "'", "matrix": [5, 5], "x": 11.75, "y": 2}, {"label": "#", "matrix": [4, 6], "x": 12.75, "y": 2}, {"label": "Enter", "matrix": [7, 6], "x": 13.75, "y": 1, "w": 1.25, "h": 2}, {"label": "Shift", "matrix": [6, 0], "x": 0, "y": 3, "w": 1.25}, {"label": "\\", "matrix": [7, 0], "x": 1.25, "y": 3}, {"label": "Z", "matrix": [6, 1], "x": 2.25, "y": 3}, {"label": "X", "matrix": [7, 1], "x": 3.25, "y": 3}, {"label": "C", "matrix": [6, 2], "x": 4.25, "y": 3}, {"label": "V", "matrix": [7, 2], "x": 5.25, "y": 3}, {"label": "B", "matrix": [6, 3], "x": 6.25, "y": 3}, {"label": "N", "matrix": [7, 3], "x": 7.25, "y": 3}, {"label": "M", "matrix": [6, 4], "x": 8.25, "y": 3}, {"label": ",", "matrix": [7, 4], "x": 9.25, "y": 3}, {"label": ".", "matrix": [6, 5], "x": 10.25, "y": 3}, {"label": "/", "matrix": [7, 5], "x": 11.25, "y": 3}, {"label": "Shift", "matrix": [6, 6], "x": 12.25, "y": 3, "w": 1.75}, {"label": "Fn", "matrix": [9, 6], "x": 14, "y": 3}, {"label": "Ctrl", "matrix": [8, 0], "x": 0, "y": 4, "w": 1.5}, {"label": "GUI", "matrix": [9, 0], "x": 1.5, "y": 4}, {"label": "Alt", "matrix": [9, 1], "x": 2.5, "y": 4, "w": 1.5}, {"label": "Space", "matrix": [9, 3], "x": 4, "y": 4, "w": 7}, {"label": "Alt", "matrix": [8, 5], "x": 11, "y": 4, "w": 1.5}, {"label": "<PERSON><PERSON>", "matrix": [9, 5], "x": 12.5, "y": 4}, {"label": "Ctrl", "matrix": [8, 6], "x": 13.5, "y": 4, "w": 1.5}]}}}