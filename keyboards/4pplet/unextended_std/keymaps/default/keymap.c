/*
Copyright 2023 <PERSON> "4pplet" <<EMAIL>>

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 2 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/
#include QMK_KEYBOARD_H

const uint16_t PROGMEM keymaps[][MATRIX_ROWS][MATRIX_COLS] = {
// main layer
[0] = LAYOUT_all(
  KC_ESC,   KC_1,     KC_2,     KC_3,     KC_4,     KC_5,       KC_6,      KC_7,      KC_8,      KC_9,       KC_<PERSON>,    KC_MINS, KC_EQL,  KC_BSPC,
  KC_TAB,   <PERSON><PERSON><PERSON>,     <PERSON><PERSON><PERSON>,     <PERSON><PERSON><PERSON>,     <PERSON><PERSON><PERSON>,     <PERSON><PERSON><PERSON>,       <PERSON><PERSON><PERSON>,      <PERSON><PERSON><PERSON>,      <PERSON><PERSON><PERSON>,      <PERSON><PERSON><PERSON>,       <PERSON><PERSON><PERSON>,    <PERSON>_LBRC, KC_RBRC, <PERSON>_<PERSON><PERSON>,
  KC_LCTL,  KC_A,     KC_S,     KC_D,     KC_F,     KC_G,       KC_H,      KC_J,      KC_K,      KC_L,       KC_SCLN, KC_QUOT, KC_NUHS, KC_ENT,
  KC_LSFT,  KC_NUBS,  KC_Z,     KC_X,     KC_C,     KC_V,       KC_B,      KC_N,      KC_M,      KC_COMM,    KC_DOT,  KC_SLSH, KC_RSFT, KC_UP,
  MO(1),    KC_LALT,  KC_LGUI,  KC_GRV,             KC_SPC,                KC_BSLS,              KC_LEFT,    KC_RIGHT,KC_DOWN, KC_UP),
// basic function layer
[1] = LAYOUT_all(
  QK_BOOT,  KC_F1,    KC_F2,    KC_F3,    KC_F4,     KC_F5,     KC_F6,     KC_F7,     KC_F8,     KC_F9,      KC_F10,  KC_F11,   KC_F12,  KC_TRNS,
  KC_TRNS,  KC_TRNS,  KC_UP,    KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,   KC_TRNS,   KC_TRNS,   KC_TRNS,    KC_TRNS, KC_TRNS,  KC_TRNS, KC_TRNS,
  KC_CAPS,  KC_LEFT,  KC_DOWN,  KC_RIGHT, KC_TRNS,   KC_TRNS,   KC_TRNS,   KC_TRNS,   KC_TRNS,   KC_TRNS,    KC_TRNS, KC_TRNS,  KC_TRNS, KC_TRNS,
  KC_TRNS,  KC_TRNS,  KC_TRNS,  KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,   KC_TRNS,   KC_TRNS,   KC_TRNS,    KC_TRNS, KC_TRNS,  KC_TRNS, KC_TRNS,
  KC_TRNS,  KC_TRNS,  KC_TRNS,  KC_TRNS,             KC_TRNS,              KC_TRNS,   KC_TRNS,   KC_TRNS,    KC_TRNS, KC_TRNS)
};
