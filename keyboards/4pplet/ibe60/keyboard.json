{"manufacturer": "4pplet", "keyboard_name": "IBE60 Rev A", "maintainer": "4pplet", "bootloader": "stm32-dfu", "diode_direction": "COL2ROW", "features": {"bootmagic": true, "extrakey": true, "key_lock": true, "mousekey": true, "nkro": true}, "matrix_pins": {"cols": ["B2", "A5", "A4", "A3", "F1", "F0", "C15", "C14", "C13", "B9", "B8", "B7", "A15", "B3"], "rows": ["B14", "A9", "B6", "B5", "B4"]}, "processor": "STM32F072", "usb": {"device_version": "0.0.1", "pid": "0x0001", "vid": "0x4448"}, "indicators": {"caps_lock": "A8"}, "community_layouts": ["60_hhkb"], "layouts": {"LAYOUT_60_hhkb": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "!", "matrix": [0, 1], "x": 1, "y": 0}, {"label": "@", "matrix": [0, 2], "x": 2, "y": 0}, {"label": "#", "matrix": [0, 3], "x": 3, "y": 0}, {"label": "$", "matrix": [0, 4], "x": 4, "y": 0}, {"label": "%", "matrix": [0, 5], "x": 5, "y": 0}, {"label": "^", "matrix": [0, 6], "x": 6, "y": 0}, {"label": "&", "matrix": [0, 7], "x": 7, "y": 0}, {"label": "*", "matrix": [0, 8], "x": 8, "y": 0}, {"label": "(", "matrix": [0, 9], "x": 9, "y": 0}, {"label": ")", "matrix": [0, 10], "x": 10, "y": 0}, {"label": "_", "matrix": [0, 11], "x": 11, "y": 0}, {"label": "+", "matrix": [0, 12], "x": 12, "y": 0}, {"label": "|", "matrix": [0, 13], "x": 13, "y": 0}, {"label": "~", "matrix": [2, 13], "x": 14, "y": 0}, {"label": "Tab", "matrix": [1, 0], "x": 0, "y": 1, "w": 1.5}, {"label": "Q", "matrix": [1, 1], "x": 1.5, "y": 1}, {"label": "W", "matrix": [1, 2], "x": 2.5, "y": 1}, {"label": "E", "matrix": [1, 3], "x": 3.5, "y": 1}, {"label": "R", "matrix": [1, 4], "x": 4.5, "y": 1}, {"label": "T", "matrix": [1, 5], "x": 5.5, "y": 1}, {"label": "Y", "matrix": [1, 6], "x": 6.5, "y": 1}, {"label": "U", "matrix": [1, 7], "x": 7.5, "y": 1}, {"label": "I", "matrix": [1, 8], "x": 8.5, "y": 1}, {"label": "O", "matrix": [1, 9], "x": 9.5, "y": 1}, {"label": "P", "matrix": [1, 10], "x": 10.5, "y": 1}, {"label": "{", "matrix": [1, 11], "x": 11.5, "y": 1}, {"label": "}", "matrix": [1, 12], "x": 12.5, "y": 1}, {"label": "|", "matrix": [1, 13], "x": 13.5, "y": 1, "w": 1.5}, {"label": "Caps Lock", "matrix": [2, 0], "x": 0, "y": 2, "w": 1.75}, {"label": "A", "matrix": [2, 1], "x": 1.75, "y": 2}, {"label": "S", "matrix": [2, 2], "x": 2.75, "y": 2}, {"label": "D", "matrix": [2, 3], "x": 3.75, "y": 2}, {"label": "F", "matrix": [2, 4], "x": 4.75, "y": 2}, {"label": "G", "matrix": [2, 5], "x": 5.75, "y": 2}, {"label": "H", "matrix": [2, 6], "x": 6.75, "y": 2}, {"label": "J", "matrix": [2, 7], "x": 7.75, "y": 2}, {"label": "K", "matrix": [2, 8], "x": 8.75, "y": 2}, {"label": "L", "matrix": [2, 9], "x": 9.75, "y": 2}, {"label": ":", "matrix": [2, 10], "x": 10.75, "y": 2}, {"label": "\"", "matrix": [2, 11], "x": 11.75, "y": 2}, {"label": "Enter", "matrix": [3, 13], "x": 12.75, "y": 2, "w": 2.25}, {"label": "Shift", "matrix": [3, 0], "x": 0, "y": 3, "w": 2.25}, {"label": "Z", "matrix": [3, 2], "x": 2.25, "y": 3}, {"label": "X", "matrix": [3, 3], "x": 3.25, "y": 3}, {"label": "C", "matrix": [3, 4], "x": 4.25, "y": 3}, {"label": "V", "matrix": [3, 5], "x": 5.25, "y": 3}, {"label": "B", "matrix": [3, 6], "x": 6.25, "y": 3}, {"label": "N", "matrix": [3, 7], "x": 7.25, "y": 3}, {"label": "M", "matrix": [3, 8], "x": 8.25, "y": 3}, {"label": "<", "matrix": [3, 9], "x": 9.25, "y": 3}, {"label": ">", "matrix": [3, 10], "x": 10.25, "y": 3}, {"label": "?", "matrix": [3, 11], "x": 11.25, "y": 3}, {"label": "Shift", "matrix": [3, 12], "x": 12.25, "y": 3, "w": 1.75}, {"label": "Fn", "matrix": [4, 13], "x": 14, "y": 3}, {"label": "Win", "matrix": [4, 1], "x": 1.5, "y": 4}, {"label": "Alt", "matrix": [4, 2], "x": 2.5, "y": 4, "w": 1.5}, {"label": "Space", "matrix": [4, 6], "x": 4, "y": 4, "w": 7}, {"label": "Alt", "matrix": [4, 9], "x": 11, "y": 4, "w": 1.5}, {"label": "Win", "matrix": [4, 10], "x": 12.5, "y": 4}]}}}