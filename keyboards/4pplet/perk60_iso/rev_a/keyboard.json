{"keyboard_name": "Perk60 ISO Rev A", "manufacturer": "4pplet", "maintainer": "4pplet", "usb": {"vid": "0x4444", "pid": "0x0009", "device_version": "0.0.1"}, "rgb_matrix": {"animations": {"alphas_mods": true, "gradient_up_down": true, "gradient_left_right": true, "breathing": true, "cycle_all": true, "cycle_left_right": true, "cycle_up_down": true, "rainbow_moving_chevron": true, "cycle_out_in": true, "cycle_out_in_dual": true, "cycle_pinwheel": true, "cycle_spiral": true, "dual_beacon": true, "rainbow_beacon": true, "rainbow_pinwheels": true, "raindrops": true, "jellybean_raindrops": true, "solid_reactive_simple": true, "solid_reactive": true, "solid_reactive_wide": true, "solid_reactive_cross": true, "solid_reactive_nexus": true, "splash": true, "solid_splash": true}, "default": {"val": 80}, "driver": "is31fl3733"}, "features": {"bootmagic": true, "extrakey": true, "mousekey": false, "nkro": true, "rgb_matrix": true}, "qmk": {"locking": {"enabled": true, "resync": true}}, "matrix_pins": {"cols": ["A1", "B12", "B14", "A2", "A0", "A3", "A4"], "rows": ["C14", "C13", "B5", "B4", "B8", "A15", "B3", "B9", "A5", "A7"]}, "diode_direction": "COL2ROW", "processor": "STM32F411", "bootloader": "stm32-dfu", "community_layouts": ["60_iso"], "layouts": {"LAYOUT_60_iso": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [1, 0], "x": 1, "y": 0}, {"matrix": [0, 1], "x": 2, "y": 0}, {"matrix": [1, 1], "x": 3, "y": 0}, {"matrix": [0, 2], "x": 4, "y": 0}, {"matrix": [1, 2], "x": 5, "y": 0}, {"matrix": [0, 3], "x": 6, "y": 0}, {"matrix": [1, 3], "x": 7, "y": 0}, {"matrix": [0, 4], "x": 8, "y": 0}, {"matrix": [1, 4], "x": 9, "y": 0}, {"matrix": [0, 5], "x": 10, "y": 0}, {"matrix": [1, 5], "x": 11, "y": 0}, {"matrix": [0, 6], "x": 12, "y": 0}, {"matrix": [3, 6], "x": 13, "y": 0, "w": 2}, {"matrix": [2, 0], "x": 0, "y": 1, "w": 1.5}, {"matrix": [3, 0], "x": 1.5, "y": 1}, {"matrix": [2, 1], "x": 2.5, "y": 1}, {"matrix": [3, 1], "x": 3.5, "y": 1}, {"matrix": [2, 2], "x": 4.5, "y": 1}, {"matrix": [3, 2], "x": 5.5, "y": 1}, {"matrix": [2, 3], "x": 6.5, "y": 1}, {"matrix": [3, 3], "x": 7.5, "y": 1}, {"matrix": [2, 4], "x": 8.5, "y": 1}, {"matrix": [3, 4], "x": 9.5, "y": 1}, {"matrix": [2, 5], "x": 10.5, "y": 1}, {"matrix": [3, 5], "x": 11.5, "y": 1}, {"matrix": [2, 6], "x": 12.5, "y": 1}, {"matrix": [4, 0], "x": 0, "y": 2, "w": 1.75}, {"matrix": [5, 0], "x": 1.75, "y": 2}, {"matrix": [4, 1], "x": 2.75, "y": 2}, {"matrix": [5, 1], "x": 3.75, "y": 2}, {"matrix": [4, 2], "x": 4.75, "y": 2}, {"matrix": [5, 2], "x": 5.75, "y": 2}, {"matrix": [4, 3], "x": 6.75, "y": 2}, {"matrix": [5, 3], "x": 7.75, "y": 2}, {"matrix": [4, 4], "x": 8.75, "y": 2}, {"matrix": [5, 4], "x": 9.75, "y": 2}, {"matrix": [4, 5], "x": 10.75, "y": 2}, {"matrix": [5, 5], "x": 11.75, "y": 2}, {"matrix": [4, 6], "x": 12.75, "y": 2}, {"matrix": [7, 6], "x": 13.75, "y": 1, "w": 1.25, "h": 2}, {"matrix": [6, 0], "x": 0, "y": 3, "w": 1.25}, {"matrix": [7, 0], "x": 1.25, "y": 3}, {"matrix": [6, 1], "x": 2.25, "y": 3}, {"matrix": [7, 1], "x": 3.25, "y": 3}, {"matrix": [6, 2], "x": 4.25, "y": 3}, {"matrix": [7, 2], "x": 5.25, "y": 3}, {"matrix": [6, 3], "x": 6.25, "y": 3}, {"matrix": [7, 3], "x": 7.25, "y": 3}, {"matrix": [6, 4], "x": 8.25, "y": 3}, {"matrix": [7, 4], "x": 9.25, "y": 3}, {"matrix": [6, 5], "x": 10.25, "y": 3}, {"matrix": [7, 5], "x": 11.25, "y": 3}, {"matrix": [8, 6], "x": 12.25, "y": 3, "w": 2.75}, {"matrix": [8, 0], "x": 0, "y": 4, "w": 1.25}, {"matrix": [9, 0], "x": 1.25, "y": 4, "w": 1.25}, {"matrix": [8, 1], "x": 2.5, "y": 4, "w": 1.25}, {"matrix": [8, 3], "x": 3.75, "y": 4, "w": 6.25}, {"matrix": [9, 3], "x": 10, "y": 4, "w": 1.25}, {"matrix": [9, 4], "x": 11.25, "y": 4, "w": 1.25}, {"matrix": [8, 5], "x": 12.5, "y": 4, "w": 1.25}, {"matrix": [9, 5], "x": 13.75, "y": 4, "w": 1.25}]}}}