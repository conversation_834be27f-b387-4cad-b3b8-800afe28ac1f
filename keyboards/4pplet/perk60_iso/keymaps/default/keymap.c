/*
Copyright 2022 <PERSON> "4pplet" <<EMAIL>>

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 2 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/
#include QMK_KEYBOARD_H

const uint16_t PROGMEM keymaps[][MATRIX_ROWS][MATRIX_COLS] = {
// main layer
[0] = LAYOUT_60_iso(
  KC_ESC,   KC_1,     KC_2,     KC_3,     KC_4,     KC_5,       KC_6,      KC_7,      KC_8,      KC_9,      <PERSON>_<PERSON>,    KC_MINS, KC_EQL,  KC_BSPC,
  KC_<PERSON><PERSON>,   <PERSON><PERSON><PERSON>,     <PERSON><PERSON><PERSON>,     <PERSON><PERSON><PERSON>,     <PERSON><PERSON><PERSON>,     <PERSON><PERSON><PERSON>,       <PERSON><PERSON><PERSON>,      <PERSON><PERSON><PERSON>,      <PERSON><PERSON><PERSON>,      <PERSON><PERSON><PERSON>,      <PERSON><PERSON><PERSON>,    <PERSON>_LBR<PERSON>, <PERSON>_RBRC,
  KC_CAPS,  KC_A,     KC_S,     KC_D,     KC_F,     KC_G,       KC_H,      KC_J,      KC_K,      KC_L,      KC_SCLN, KC_QUOT, KC_NUHS, KC_ENT,
  KC_LSFT,  KC_NUBS,  KC_Z,     KC_X,     KC_C,     KC_V,       KC_B,      KC_N,      KC_M,      KC_COMM,   KC_DOT,  KC_SLSH, KC_RSFT,
  KC_LCTL,  KC_LGUI,  KC_LALT,                                  KC_SPC,                                     KC_RALT,   KC_RGUI, MO(1), KC_RCTL),
  // basic function layer
[1] = LAYOUT_60_iso(
  QK_BOOT,  KC_F1,    KC_F2,    KC_F3,    KC_F4,     KC_F5,     KC_F6,      KC_F7,          KC_F8,              KC_F9,      KC_F10,  KC_F11,   KC_F12,  KC_DEL,
  KC_TRNS,  KC_TRNS,  KC_UP,    KC_TRNS,  KC_TRNS,   KC_TRNS,   KC_TRNS,    KC_TRNS,        KC_TRNS,            KC_TRNS,    KC_TRNS, KC_TRNS,  KC_TRNS,
  KC_TRNS,  KC_LEFT,  KC_DOWN,  KC_RIGHT, KC_TRNS,   KC_TRNS,   KC_TRNS,    KC_TRNS,        KC_TRNS,            KC_TRNS,    KC_TRNS, KC_TRNS,  KC_TRNS, KC_TRNS,
  KC_TRNS,  RM_TOGG,  RM_NEXT,  RM_HUEU,  RM_SATU,   RM_VALU,   RM_SPDU,    RGB_M_P,        RGB_M_B,            RGB_M_R,    KC_TRNS, KC_TRNS,  KC_TRNS,
  KC_TRNS,  KC_TRNS,  KC_TRNS,                       KC_TRNS,                                                   KC_TRNS,  KC_TRNS,  KC_TRNS,    KC_TRNS)
};
