# bootleg

A 60% PCB for non tray-mount keyboards

* Keyboard Maintainer: [4pplet](https://github.com/4pplet)
* Hardware Supported: bootleg Rev A

Make example for this keyboard (after setting up your build environment):

    make 4pplet/bootleg/rev_a:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
