{"manufacturer": "4pplet", "maintainer": "4pplet", "usb": {"vid": "0x4444"}, "community_layouts": ["60_iso_tsangan"], "layouts": {"LAYOUT_all": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0, "w": 1.25}, {"label": "1", "matrix": [0, 1], "x": 1.25, "y": 0}, {"label": "2", "matrix": [0, 2], "x": 2.25, "y": 0}, {"label": "3", "matrix": [0, 3], "x": 3.25, "y": 0}, {"label": "4", "matrix": [0, 4], "x": 4.25, "y": 0}, {"label": "5", "matrix": [0, 5], "x": 5.25, "y": 0}, {"label": "6", "matrix": [0, 6], "x": 6.25, "y": 0}, {"label": "7", "matrix": [0, 7], "x": 7.25, "y": 0}, {"label": "8", "matrix": [0, 8], "x": 8.25, "y": 0}, {"label": "9", "matrix": [0, 9], "x": 9.25, "y": 0}, {"label": "0", "matrix": [0, 10], "x": 10.25, "y": 0}, {"label": "-", "matrix": [0, 11], "x": 11.25, "y": 0}, {"label": "=", "matrix": [0, 12], "x": 12.25, "y": 0}, {"label": "Backspace", "matrix": [0, 13], "x": 13.25, "y": 0, "w": 1.75}, {"label": "Tab", "matrix": [1, 0], "x": 0, "y": 1, "w": 1.75}, {"label": "Q", "matrix": [1, 1], "x": 1.75, "y": 1}, {"label": "W", "matrix": [1, 2], "x": 2.75, "y": 1}, {"label": "E", "matrix": [1, 3], "x": 3.75, "y": 1}, {"label": "R", "matrix": [1, 4], "x": 4.75, "y": 1}, {"label": "T", "matrix": [1, 5], "x": 5.75, "y": 1}, {"label": "Y", "matrix": [1, 6], "x": 6.75, "y": 1}, {"label": "U", "matrix": [1, 7], "x": 7.75, "y": 1}, {"label": "I", "matrix": [1, 8], "x": 8.75, "y": 1}, {"label": "O", "matrix": [1, 9], "x": 9.75, "y": 1}, {"label": "P", "matrix": [1, 10], "x": 10.75, "y": 1}, {"label": "[", "matrix": [1, 11], "x": 11.75, "y": 1}, {"label": "]", "matrix": [1, 12], "x": 12.75, "y": 1}, {"label": "Ctrl", "matrix": [2, 0], "x": 0, "y": 2, "w": 2}, {"label": "A", "matrix": [2, 1], "x": 2, "y": 2}, {"label": "S", "matrix": [2, 2], "x": 3, "y": 2}, {"label": "D", "matrix": [2, 3], "x": 4, "y": 2}, {"label": "F", "matrix": [2, 4], "x": 5, "y": 2}, {"label": "G", "matrix": [2, 5], "x": 6, "y": 2}, {"label": "H", "matrix": [2, 6], "x": 7, "y": 2}, {"label": "J", "matrix": [2, 7], "x": 8, "y": 2}, {"label": "K", "matrix": [2, 8], "x": 9, "y": 2}, {"label": "L", "matrix": [2, 9], "x": 10, "y": 2}, {"label": ";", "matrix": [2, 10], "x": 11, "y": 2}, {"label": "'", "matrix": [2, 11], "x": 12, "y": 2}, {"label": "#", "matrix": [2, 12], "x": 13, "y": 2}, {"label": "Enter", "matrix": [1, 13], "x": 14, "y": 1, "h": 2}, {"label": "Shift", "matrix": [3, 0], "x": 0, "y": 3, "w": 1.5}, {"label": "\\", "matrix": [3, 1], "x": 1.5, "y": 3}, {"label": "Z", "matrix": [3, 2], "x": 2.5, "y": 3}, {"label": "X", "matrix": [3, 3], "x": 3.5, "y": 3}, {"label": "C", "matrix": [3, 4], "x": 4.5, "y": 3}, {"label": "V", "matrix": [3, 5], "x": 5.5, "y": 3}, {"label": "B", "matrix": [3, 6], "x": 6.5, "y": 3}, {"label": "N", "matrix": [3, 7], "x": 7.5, "y": 3}, {"label": "M", "matrix": [3, 8], "x": 8.5, "y": 3}, {"label": ",", "matrix": [3, 9], "x": 9.5, "y": 3}, {"label": ".", "matrix": [3, 10], "x": 10.5, "y": 3}, {"label": "/", "matrix": [3, 11], "x": 11.5, "y": 3}, {"label": "Shift", "matrix": [3, 12], "x": 12.5, "y": 3, "w": 1.5}, {"label": "Fn", "matrix": [3, 13], "x": 14, "y": 3}, {"label": "Ctrl", "matrix": [4, 0], "x": 0, "y": 4, "w": 1.5}, {"label": "GUI", "matrix": [4, 1], "x": 1.5, "y": 4, "w": 1.25}, {"label": "Alt", "matrix": [4, 3], "x": 2.75, "y": 4, "w": 1.5}, {"label": "▽", "matrix": [4, 4], "x": 4.25, "y": 4, "w": 2.75}, {"label": "Space", "matrix": [4, 5], "x": 7, "y": 4}, {"label": "▽", "matrix": [4, 6], "x": 8, "y": 4, "w": 2.75}, {"label": "Alt", "matrix": [4, 8], "x": 10.75, "y": 4, "w": 1.5}, {"label": "GUI", "matrix": [4, 10], "x": 12.25, "y": 4, "w": 1.25}, {"label": "Ctrl", "matrix": [4, 11], "x": 13.5, "y": 4, "w": 1.5}]}, "LAYOUT_60_iso_tsangan": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0, "w": 1.25}, {"label": "1", "matrix": [0, 1], "x": 1.25, "y": 0}, {"label": "2", "matrix": [0, 2], "x": 2.25, "y": 0}, {"label": "3", "matrix": [0, 3], "x": 3.25, "y": 0}, {"label": "4", "matrix": [0, 4], "x": 4.25, "y": 0}, {"label": "5", "matrix": [0, 5], "x": 5.25, "y": 0}, {"label": "6", "matrix": [0, 6], "x": 6.25, "y": 0}, {"label": "7", "matrix": [0, 7], "x": 7.25, "y": 0}, {"label": "8", "matrix": [0, 8], "x": 8.25, "y": 0}, {"label": "9", "matrix": [0, 9], "x": 9.25, "y": 0}, {"label": "0", "matrix": [0, 10], "x": 10.25, "y": 0}, {"label": "-", "matrix": [0, 11], "x": 11.25, "y": 0}, {"label": "=", "matrix": [0, 12], "x": 12.25, "y": 0}, {"label": "Backspace", "matrix": [0, 13], "x": 13.25, "y": 0, "w": 1.75}, {"label": "Tab", "matrix": [1, 0], "x": 0, "y": 1, "w": 1.75}, {"label": "Q", "matrix": [1, 1], "x": 1.75, "y": 1}, {"label": "W", "matrix": [1, 2], "x": 2.75, "y": 1}, {"label": "E", "matrix": [1, 3], "x": 3.75, "y": 1}, {"label": "R", "matrix": [1, 4], "x": 4.75, "y": 1}, {"label": "T", "matrix": [1, 5], "x": 5.75, "y": 1}, {"label": "Y", "matrix": [1, 6], "x": 6.75, "y": 1}, {"label": "U", "matrix": [1, 7], "x": 7.75, "y": 1}, {"label": "I", "matrix": [1, 8], "x": 8.75, "y": 1}, {"label": "O", "matrix": [1, 9], "x": 9.75, "y": 1}, {"label": "P", "matrix": [1, 10], "x": 10.75, "y": 1}, {"label": "[", "matrix": [1, 11], "x": 11.75, "y": 1}, {"label": "]", "matrix": [1, 12], "x": 12.75, "y": 1}, {"label": "Ctrl", "matrix": [2, 0], "x": 0, "y": 2, "w": 2}, {"label": "A", "matrix": [2, 1], "x": 2, "y": 2}, {"label": "S", "matrix": [2, 2], "x": 3, "y": 2}, {"label": "D", "matrix": [2, 3], "x": 4, "y": 2}, {"label": "F", "matrix": [2, 4], "x": 5, "y": 2}, {"label": "G", "matrix": [2, 5], "x": 6, "y": 2}, {"label": "H", "matrix": [2, 6], "x": 7, "y": 2}, {"label": "J", "matrix": [2, 7], "x": 8, "y": 2}, {"label": "K", "matrix": [2, 8], "x": 9, "y": 2}, {"label": "L", "matrix": [2, 9], "x": 10, "y": 2}, {"label": ";", "matrix": [2, 10], "x": 11, "y": 2}, {"label": "'", "matrix": [2, 11], "x": 12, "y": 2}, {"label": "#", "matrix": [2, 12], "x": 13, "y": 2}, {"label": "Enter", "matrix": [1, 13], "x": 14, "y": 1, "h": 2}, {"label": "Shift", "matrix": [3, 0], "x": 0, "y": 3, "w": 1.5}, {"label": "\\", "matrix": [3, 1], "x": 1.5, "y": 3}, {"label": "Z", "matrix": [3, 2], "x": 2.5, "y": 3}, {"label": "X", "matrix": [3, 3], "x": 3.5, "y": 3}, {"label": "C", "matrix": [3, 4], "x": 4.5, "y": 3}, {"label": "V", "matrix": [3, 5], "x": 5.5, "y": 3}, {"label": "B", "matrix": [3, 6], "x": 6.5, "y": 3}, {"label": "N", "matrix": [3, 7], "x": 7.5, "y": 3}, {"label": "M", "matrix": [3, 8], "x": 8.5, "y": 3}, {"label": ",", "matrix": [3, 9], "x": 9.5, "y": 3}, {"label": ".", "matrix": [3, 10], "x": 10.5, "y": 3}, {"label": "/", "matrix": [3, 11], "x": 11.5, "y": 3}, {"label": "Shift", "matrix": [3, 12], "x": 12.5, "y": 3, "w": 2.5}, {"label": "Ctrl", "matrix": [4, 0], "x": 0, "y": 4, "w": 1.5}, {"label": "GUI", "matrix": [4, 1], "x": 1.5, "y": 4, "w": 1.25}, {"label": "Alt", "matrix": [4, 3], "x": 2.75, "y": 4, "w": 1.5}, {"label": "Space", "matrix": [4, 5], "x": 4.25, "y": 4, "w": 6.5}, {"label": "Alt", "matrix": [4, 8], "x": 10.75, "y": 4, "w": 1.5}, {"label": "GUI", "matrix": [4, 10], "x": 12.25, "y": 4, "w": 1.25}, {"label": "Ctrl", "matrix": [4, 11], "x": 13.5, "y": 4, "w": 1.5}]}}}