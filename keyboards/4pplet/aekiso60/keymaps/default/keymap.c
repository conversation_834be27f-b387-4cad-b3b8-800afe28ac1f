#include QM<PERSON>_KEYBOARD_H

const uint16_t PROGMEM keymaps[][MATRIX_ROWS][MATRIX_COLS] = {


    [0] = LAYOUT_all(
        KC_ESC,  KC_1,    KC_2,    KC_3,    KC_4,    KC_5,    <PERSON><PERSON>6,    <PERSON><PERSON>7,    <PERSON><PERSON>8,    KC_9,    <PERSON>_<PERSON>,    <PERSON><PERSON>MINS, KC_EQL,  KC_BSPC,
        KC_TAB,  KC_Q,    KC_W,    KC_E,    KC_R,    KC_T,    KC_Y,    KC_U,    KC_<PERSON>,    <PERSON><PERSON><PERSON>,    KC_P,    KC_LBRC, KC_RBRC,
        KC_LCTL, KC_A,    KC_S,    KC_D,    KC_F,    KC_G,    <PERSON>_<PERSON>,    <PERSON><PERSON><PERSON>,    <PERSON><PERSON><PERSON>,    KC_L,    KC_SCLN, KC_QUOT, KC_NUHS, KC_ENT,
        KC_LSFT, KC_NUBS, KC_Z,    KC_X,    KC_C,    KC_V,    KC_B,    KC_N,    KC_M,    KC_COMM, KC_DOT,  KC_SLSH, KC_RSFT, MO(1),
        <PERSON>_LCT<PERSON>, KC_LGUI, KC_LALT,          _______,          <PERSON><PERSON><PERSON><PERSON>,           _______,                   <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>RG<PERSON>, KC_RCTL
    ),
    [1] = LAYOUT_all(
        KC_EXEC, KC_F1,   KC_F2,   KC_F3,   KC_F4,   KC_F5,   KC_F6,   KC_F7,   KC_F8,   KC_F9,   KC_F10,  KC_F11,  KC_F12,  KC_DEL,
        _______, KC_BSPC, KC_UP,   _______, _______, _______, _______, _______, KC_PSCR, KC_SCRL, KC_PAUS, KC_UP,   _______,
        KC_CAPS, KC_LEFT, KC_DOWN, KC_RGHT, KC_MUTE, _______, KC_PAST, KC_PSLS, KC_HOME, KC_PGUP, KC_LEFT, KC_RGHT, KC_INS,  KC_PENT,
        _______, KC_MPRV, KC_MPLY, KC_MNXT, KC_VOLD, KC_VOLU, KC_PPLS, KC_PMNS, _______, KC_END,  KC_PGDN, KC_DOWN, _______, _______,
        QK_BOOT, _______, _______,          _______,          _______,          _______,                   KC_BRID, KC_BRIU, _______
    )
};
