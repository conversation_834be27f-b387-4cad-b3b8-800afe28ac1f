# 3W6

![3W6](https://raw.githubusercontent.com/weteor/3W6/main/images/3w6_1s.jpg)
![3W6](https://raw.githubusercontent.com/weteor/3W6/main/images/3w6_3s.jpg)

The 3w6 is a low profile, split ortholinear keyboard with 36 keys.

* Rev1: 
  - onboard microcontroller (ATMega32U4)
  - USB-C connector Board <-> PC
  - USB-C connectors between both split halfs
  - choc spacing (18x17mm)
  - aggressive pinky stagger
  - support for Choc V1 switches

---

* Keyboard Maintainer: [weteor](https://github.com/weteor)
* Hardware Supported: 
    * 3w6 rev1
* Hardware Availability (this is an older version, current revision is rev2): 
    * make one yourself: [Design and Productionfiles](https://github.com/weteor/3w6)
---
To reach the bootloader, connect the board to the PC and push the reset button on left half.

Make examples for this keyboard (after setting up your build environment):

    make 3w6/rev1:default
   
 ---

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
