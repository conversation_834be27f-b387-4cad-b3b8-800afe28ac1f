{"usb": {"device_version": "0.0.2", "no_startup_check": true}, "features": {"bootmagic": false, "extrakey": true, "mousekey": true, "nkro": false, "unicode": true}, "processor": "atmega32u4", "bootloader": "atmel-dfu", "layouts": {"LAYOUT": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0.8}, {"matrix": [0, 1], "x": 1, "y": 0.2}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0.2}, {"matrix": [0, 4], "x": 4, "y": 0.4}, {"matrix": [4, 0], "x": 8, "y": 0.4}, {"matrix": [4, 1], "x": 9, "y": 0.2}, {"matrix": [4, 2], "x": 10, "y": 0}, {"matrix": [4, 3], "x": 11, "y": 0.2}, {"matrix": [4, 4], "x": 12, "y": 0.8}, {"matrix": [1, 0], "x": 0, "y": 1.8}, {"matrix": [1, 1], "x": 1, "y": 1.2}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1.2}, {"matrix": [1, 4], "x": 4, "y": 1.4}, {"matrix": [5, 0], "x": 8, "y": 1.4}, {"matrix": [5, 1], "x": 9, "y": 1.2}, {"matrix": [5, 2], "x": 10, "y": 1}, {"matrix": [5, 3], "x": 11, "y": 1.2}, {"matrix": [5, 4], "x": 12, "y": 1.8}, {"matrix": [2, 0], "x": 0, "y": 2.8}, {"matrix": [2, 1], "x": 1, "y": 2.2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2.2}, {"matrix": [2, 4], "x": 4, "y": 2.4}, {"matrix": [6, 0], "x": 8, "y": 2.4}, {"matrix": [6, 1], "x": 9, "y": 2.2}, {"matrix": [6, 2], "x": 10, "y": 2}, {"matrix": [6, 3], "x": 11, "y": 2.2}, {"matrix": [6, 4], "x": 12, "y": 2.8}, {"matrix": [3, 2], "x": 3.2, "y": 3.6}, {"matrix": [3, 3], "x": 4.2, "y": 3.6}, {"matrix": [3, 4], "x": 5.2, "y": 3.8}, {"matrix": [7, 0], "x": 6.8, "y": 3.8}, {"matrix": [7, 1], "x": 7.8, "y": 3.6}, {"matrix": [7, 2], "x": 8.8, "y": 3.6}]}}}