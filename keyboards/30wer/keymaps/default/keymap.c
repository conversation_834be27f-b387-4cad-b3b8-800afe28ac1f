#include QM<PERSON>_KEY<PERSON>ARD_H

const uint16_t PROGMEM keymaps[][MATRIX_ROWS][MATRIX_COLS] = {

[0] = LAYOUT(
  KC_TAB,  KC_Q,    KC_W,    KC_<PERSON>,    KC_R,    <PERSON>_<PERSON>,    <PERSON>_<PERSON>,    <PERSON>_<PERSON>,    <PERSON><PERSON><PERSON>,    <PERSON><PERSON><PERSON>,    <PERSON><PERSON><PERSON>,    KC_LBRC, KC_BSPC,
  KC_LCTL, KC_A,    KC_S,    KC_D,    KC_F,    KC_G,    KC_H,    KC_J,    KC_K,    KC_L,    KC_SCLN, KC_QUOT, KC_ENT,
           KC_LSFT, KC_Z,    KC_X,    KC_C,    KC_V,    KC_B,    KC_N,    KC_M,    KC_COMM, KC_DOT,  KC_SLSH, LT(1, KC_SPC)
),

[1] = LAYOUT(
  KC_ESC,  KC_1,    KC_2,    KC_3,    KC_4,    KC_5,    KC_6,    KC_7,    KC_8,    KC_9,    KC_0,    KC_UP,   KC_DEL,
  _______, _______, _______, _______, <PERSON><PERSON>_BOOT, _______, _______, _______, _______, _______, KC_LEFT, <PERSON>_RGHT, _______,
           KC_LALT, _______, _______, _______, _______, _______, _______, _______, _______, _______, KC_DOWN, _______
),

};
