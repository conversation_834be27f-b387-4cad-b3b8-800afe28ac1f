{"keyboard_name": "30wer", "manufacturer": "8o7wer", "maintainer": "qmk", "usb": {"vid": "0x1234", "pid": "0x5678", "device_version": "0.0.1"}, "features": {"bootmagic": false, "console": true, "extrakey": true, "mousekey": false, "nkro": true}, "matrix_pins": {"cols": ["F4", "F5", "F6", "F7", "B1", "B3", "B2", "B6", "D1", "D0", "D4", "C6", "D7"], "rows": ["E6", "B4", "B5"]}, "diode_direction": "COL2ROW", "development_board": "promicro", "layouts": {"LAYOUT": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [0, 12], "x": 12, "y": 0, "w": 1.25}, {"matrix": [1, 0], "x": 0, "y": 1, "w": 1.25}, {"matrix": [1, 1], "x": 1.25, "y": 1}, {"matrix": [1, 2], "x": 2.25, "y": 1}, {"matrix": [1, 3], "x": 3.25, "y": 1}, {"matrix": [1, 4], "x": 4.25, "y": 1}, {"matrix": [1, 5], "x": 5.25, "y": 1}, {"matrix": [1, 6], "x": 6.25, "y": 1}, {"matrix": [1, 7], "x": 7.25, "y": 1}, {"matrix": [1, 8], "x": 8.25, "y": 1}, {"matrix": [1, 9], "x": 9.25, "y": 1}, {"matrix": [1, 10], "x": 10.25, "y": 1}, {"matrix": [1, 11], "x": 11.25, "y": 1}, {"matrix": [1, 12], "x": 12.25, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2, "w": 1.75}, {"matrix": [2, 1], "x": 1.75, "y": 2}, {"matrix": [2, 2], "x": 2.75, "y": 2}, {"matrix": [2, 3], "x": 3.75, "y": 2}, {"matrix": [2, 4], "x": 4.75, "y": 2}, {"matrix": [2, 5], "x": 5.75, "y": 2}, {"matrix": [2, 6], "x": 6.75, "y": 2}, {"matrix": [2, 7], "x": 7.75, "y": 2}, {"matrix": [2, 8], "x": 8.75, "y": 2}, {"matrix": [2, 9], "x": 9.75, "y": 2}, {"matrix": [2, 10], "x": 10.75, "y": 2}, {"matrix": [2, 11], "x": 11.75, "y": 2, "w": 1.5}]}}}