{"manufacturer": "1upkeyboards", "keyboard_name": "1upsuper16v3", "maintainer": "z<PERSON><PERSON><PERSON>", "bootloader": "rp2040", "dynamic_keymap": {"layer_count": 10}, "encoder": {"rotary": [{"pin_a": "GP20", "pin_b": "GP21"}, {"pin_a": "GP25", "pin_b": "GP26"}, {"pin_a": "GP2", "pin_b": "GP3"}, {"pin_a": "GP6", "pin_b": "GP7"}]}, "features": {"bootmagic": true, "encoder": true, "extrakey": true, "mousekey": true, "nkro": false, "rgb_matrix": true}, "matrix_pins": {"direct": [["GP23", "GP24", "GP4", "GP5"], ["GP19", "GP27", "GP1", "GP8"], ["GP18", "GP28", "GP0", "GP9"], ["GP17", "GP16", "GP15", "GP11"]]}, "processor": "RP2040", "rgb_matrix": {"animations": {"band_pinwheel_sat": true, "band_pinwheel_val": true, "band_sat": true, "band_spiral_sat": true, "band_spiral_val": true, "band_val": true, "breathing": true, "cycle_all": true, "cycle_left_right": true, "cycle_out_in": true, "cycle_out_in_dual": true, "cycle_pinwheel": true, "cycle_spiral": true, "cycle_up_down": true, "digital_rain": true, "dual_beacon": true, "gradient_left_right": true, "gradient_up_down": true, "hue_breathing": true, "hue_pendulum": true, "hue_wave": true, "jellybean_raindrops": true, "multisplash": true, "pixel_flow": true, "pixel_rain": true, "rainbow_beacon": true, "rainbow_moving_chevron": true, "rainbow_pinwheels": true, "raindrops": true, "solid_multisplash": true, "solid_reactive": true, "solid_reactive_cross": true, "solid_reactive_multicross": true, "solid_reactive_multinexus": true, "solid_reactive_multiwide": true, "solid_reactive_nexus": true, "solid_reactive_simple": true, "solid_reactive_wide": true, "solid_splash": true, "splash": true, "typing_heatmap": true}, "driver": "ws2812", "layout": [{"x": 99, "y": 1, "flags": 2}, {"x": 70, "y": 1, "flags": 2}, {"x": 42, "y": 1, "flags": 2}, {"x": 14, "y": 1, "flags": 2}, {"x": 4, "y": 4, "flags": 2}, {"x": 4, "y": 12, "flags": 2}, {"matrix": [0, 0], "x": 28, "y": 8, "flags": 4}, {"matrix": [0, 1], "x": 84, "y": 8, "flags": 4}, {"x": 4, "y": 20, "flags": 2}, {"x": 4, "y": 28, "flags": 2}, {"matrix": [1, 0], "x": 28, "y": 24, "flags": 4}, {"matrix": [1, 1], "x": 84, "y": 24, "flags": 4}, {"x": 4, "y": 36, "flags": 2}, {"x": 4, "y": 44, "flags": 2}, {"matrix": [2, 0], "x": 28, "y": 40, "flags": 4}, {"matrix": [2, 1], "x": 84, "y": 40, "flags": 4}, {"x": 4, "y": 52, "flags": 2}, {"x": 4, "y": 60, "flags": 2}, {"x": 14, "y": 63, "flags": 2}, {"matrix": [3, 0], "x": 28, "y": 56, "flags": 4}, {"x": 42, "y": 63, "flags": 2}, {"x": 70, "y": 63, "flags": 2}, {"matrix": [3, 1], "x": 84, "y": 56, "flags": 4}, {"x": 99, "y": 63, "flags": 2}, {"x": 126, "y": 63, "flags": 2}, {"matrix": [3, 2], "x": 140, "y": 56, "flags": 4}, {"x": 154, "y": 63, "flags": 2}, {"x": 182, "y": 63, "flags": 2}, {"matrix": [3, 3], "x": 196, "y": 56, "flags": 4}, {"x": 210, "y": 63, "flags": 2}, {"x": 220, "y": 60, "flags": 2}, {"x": 220, "y": 52, "flags": 2}, {"x": 220, "y": 44, "flags": 2}, {"matrix": [2, 3], "x": 196, "y": 40, "flags": 4}, {"matrix": [2, 2], "x": 140, "y": 40, "flags": 4}, {"x": 220, "y": 36, "flags": 2}, {"x": 220, "y": 28, "flags": 2}, {"matrix": [1, 3], "x": 196, "y": 24, "flags": 4}, {"matrix": [1, 2], "x": 140, "y": 24, "flags": 4}, {"x": 220, "y": 20, "flags": 2}, {"x": 220, "y": 12, "flags": 2}, {"matrix": [0, 3], "x": 196, "y": 8, "flags": 4}, {"matrix": [0, 2], "x": 140, "y": 8, "flags": 4}, {"x": 220, "y": 4, "flags": 2}, {"x": 210, "y": 1, "flags": 2}, {"x": 182, "y": 1, "flags": 2}, {"x": 154, "y": 1, "flags": 2}, {"x": 126, "y": 1, "flags": 2}], "sleep": true}, "usb": {"device_version": "1.0.0", "pid": "0x5610", "vid": "0x6F75"}, "ws2812": {"driver": "vendor", "pin": "GP29"}, "community_layouts": ["ortho_4x4"], "layouts": {"LAYOUT_ortho_4x4": {"layout": [{"label": "00", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "01", "matrix": [0, 1], "x": 1, "y": 0}, {"label": "02", "matrix": [0, 2], "x": 2, "y": 0}, {"label": "03", "matrix": [0, 3], "x": 3, "y": 0}, {"label": "10", "matrix": [1, 0], "x": 0, "y": 1}, {"label": "11", "matrix": [1, 1], "x": 1, "y": 1}, {"label": "12", "matrix": [1, 2], "x": 2, "y": 1}, {"label": "13", "matrix": [1, 3], "x": 3, "y": 1}, {"label": "20", "matrix": [2, 0], "x": 0, "y": 2}, {"label": "21", "matrix": [2, 1], "x": 1, "y": 2}, {"label": "22", "matrix": [2, 2], "x": 2, "y": 2}, {"label": "23", "matrix": [2, 3], "x": 3, "y": 2}, {"label": "30", "matrix": [3, 0], "x": 0, "y": 3}, {"label": "31", "matrix": [3, 1], "x": 1, "y": 3}, {"label": "32", "matrix": [3, 2], "x": 2, "y": 3}, {"label": "33", "matrix": [3, 3], "x": 3, "y": 3}]}}}