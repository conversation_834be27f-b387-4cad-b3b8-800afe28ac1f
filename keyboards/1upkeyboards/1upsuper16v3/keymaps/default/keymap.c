/* Copyright 2023 ziptyze
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

#include QMK_KEYBOARD_H

const uint16_t PROGMEM keymaps[][MATRIX_ROWS][MATRIX_COLS] = {
    [0] = LAYOUT_ortho_4x4(
        KC_P7, KC_P8, KC_P9,   KC_PMNS,
        KC_P4, KC_P5, KC_P6,   KC_PPLS,
        KC_P1, KC_P2, KC_<PERSON>3,   <PERSON>_<PERSON><PERSON>,
        <PERSON><PERSON>(1), <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, KC_PENT
    ),
    [1] = LAYOUT_ortho_4x4(
        RM_TOGG, RM_HUEU, RM_SATU, RM_VALU,
        RM_NEXT, RM_HUED, RM_SATD, RM_VALD,
        RM_SPDD, RM_SPDU, KC_TRNS, KC_TRNS,
        KC_TRNS, KC_TRNS, KC_TRNS, QK_BOOT
    )
};

#if defined(ENCODER_MAP_ENABLE)
const uint16_t PROGMEM encoder_map[][NUM_ENCODERS][NUM_DIRECTIONS] = {
    [0] = { ENCODER_CCW_CW(KC_VOLD, KC_VOLU), ENCODER_CCW_CW(KC_VOLD, KC_VOLU), ENCODER_CCW_CW(KC_VOLD, KC_VOLU), ENCODER_CCW_CW(KC_VOLD, KC_VOLU)  },
    [1] = { ENCODER_CCW_CW(KC_TRNS, KC_TRNS), ENCODER_CCW_CW(KC_TRNS, KC_TRNS), ENCODER_CCW_CW(KC_TRNS, KC_TRNS), ENCODER_CCW_CW(KC_TRNS, KC_TRNS) }
};
#endif
