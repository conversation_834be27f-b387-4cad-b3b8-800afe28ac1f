#include Q<PERSON><PERSON>_KEYBOARD_H

const uint16_t PROGMEM keymaps[][MATRIX_ROWS][MATRIX_COLS] = {

	LAYOUT_all(
		KC_ESC, KC_1, KC_2, KC_3, <PERSON>_4, KC_5, <PERSON><PERSON>6, <PERSON><PERSON>7, <PERSON><PERSON>8, <PERSON><PERSON>9, <PERSON>_<PERSON>, <PERSON>_MINS, KC_EQL, KC_BSLS, KC_GRV,
		KC_TAB, KC_Q, KC_W, KC_E, KC_R, KC_T, KC_Y, KC_U, KC_<PERSON>, KC<PERSON><PERSON>, KC_P, KC_LBRC, KC_RBRC, KC_BSPC,
		KC_CAPS, KC_A, KC_S, KC_D, KC_F, KC_<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, KC_L, KC_SCLN, KC_QUOT, KC_ENT, KC_ENT,
		KC_LSFT, KC_LSFT, KC_Z, KC_X, KC_C, KC_V, KC_B, KC_N, KC_M, KC_COMM, KC_DOT, KC_SLSH, KC_RSFT, MO(1),
		<PERSON><PERSON>LCT<PERSON>, KC_LGUI, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>),

	LAYOUT_all(
		QK_BOOT, KC_<PERSON>, KC_F2, KC_F3, KC_F4, KC_F5, KC_F6, KC_F7, KC_F8, KC_F9, KC_F10, KC_F11, KC_F12, KC_TRNS, KC_DEL,
		KC_TRNS, BL_TOGG, BL_DOWN,BL_UP,  BL_STEP, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_PSCR, KC_UP, KC_TRNS, KC_TRNS,
		KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_LEFT, KC_RGHT, KC_TRNS, KC_TRNS,
		KC_TRNS, KC_TRNS, UG_TOGG, UG_NEXT, UG_HUEU, UG_HUED, UG_SATU, UG_SATD, UG_VALU, UG_VALD, KC_TRNS, KC_DOWN, KC_TRNS, KC_TRNS,
		KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS)

};

bool led_update_user(led_t led_state) {
	if (led_state.caps_lock) {
		gpio_set_pin_output(B2);
		gpio_write_pin_low(B2);
	} else {
		gpio_set_pin_input(B2);
		gpio_write_pin_low(B2);
	}
	return false;
}
