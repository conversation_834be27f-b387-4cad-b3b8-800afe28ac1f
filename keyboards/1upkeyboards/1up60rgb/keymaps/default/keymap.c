#include Q<PERSON><PERSON>_KEYBOARD_H

const uint16_t PROGMEM keymaps[][MATRIX_ROWS][MATRIX_COLS] = {

	LAYOUT_all(
		KC_ESC, KC_1, KC_2, KC_3, KC_4, KC_5, <PERSON><PERSON>6, <PERSON><PERSON>7, <PERSON><PERSON>8, <PERSON><PERSON>9, <PERSON>_<PERSON>, <PERSON><PERSON><PERSON>NS, <PERSON><PERSON><PERSON><PERSON>, KC_BSPC, KC_BSPC,
		KC_TAB, KC_Q, KC_<PERSON>, KC_E, KC_R, KC_T, KC_Y, KC_U, KC_<PERSON>, <PERSON><PERSON><PERSON>, KC_P, KC_LBRC, KC_RBRC, KC_BSLS,
		KC_CAPS, KC_A, KC_S, KC_D, KC_F, <PERSON>_<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, KC_L, KC_SCLN, KC_QUOT, KC_ENT, KC_ENT,
		KC_LSFT, KC_LSFT, KC_Z, KC_X, KC_C, KC_<PERSON>, KC_<PERSON>, KC_<PERSON>, KC_M, KC_COMM, KC_DOT, KC_SLSH, KC_RSFT, KC_RSFT,
		KC_LCT<PERSON>, KC_<PERSON>G<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>(1), <PERSON><PERSON><PERSON><PERSON>),

	LAYOUT_all(
		<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS,
		KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS,
		KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS,
		KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS,
		KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS, KC_TRNS)

};

bool led_update_user(led_t led_state) {
	if (led_state.caps_lock) {
		gpio_set_pin_output(B2);
		gpio_write_pin_low(B2);
	} else {
		gpio_set_pin_input(B2);
		gpio_write_pin_low(B2);
	}
	return false;
}
