# 1up60rgb 60% RGB

Firmware for custom keyboard PCB with 60% key layout.

Keyboard Maintainer: [rempired](https://github.com/rempired)  
Hardware Supported: 1upkeyboards 60% RGB  
Hardware Availability: [1upkeyboards](https://www.1upkeyboards.com/shop/controllers/1up-rgb-underglow-pcb/)  

Make example for this keyboard (after setting up your build environment):

    make 1upkeyboards/1up60rgb:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
