{"manufacturer": "1upkeyboards", "keyboard_name": "sweet16v2", "url": "1upkeyboards.com/shop/keyboard-kits/macro-pads/sweet-16-v2-macropad-kit/", "maintainer": "z<PERSON><PERSON><PERSON>", "bootloader": "atmel-dfu", "processor": "atmega32u4", "usb": {"device_version": "1.0.0", "pid": "0x5518", "vid": "0x6F75"}, "features": {"bootmagic": true, "encoder": true, "extrakey": true, "mousekey": true, "nkro": false, "rgb_matrix": true}, "qmk": {"locking": {"enabled": true, "resync": true}}, "ws2812": {"pin": "D7"}, "matrix_pins": {"cols": ["F7", "D2", "B1", "C6"], "rows": ["D1", "B5", "B4", "E6"]}, "diode_direction": "COL2ROW", "dynamic_keymap": {"layer_count": 10}, "encoder": {"enabled": true, "rotary": [{"pin_a": "F6", "pin_b": "F5"}, {"pin_a": "D4", "pin_b": "D0"}]}, "rgb_matrix": {"animmations": {"alphas_mods": true, "gradient_up_down": true, "gradient_left_right": true, "breathing": true, "band_sat": true, "band_val": true, "band_pinwheel_sat": true, "band_pinwheel_val": true, "band_spiral_sat": true, "band_spiral_val": true, "cycle_all": true, "cycle_left_right": true, "cycle_up_down": true, "rainbow_moving_chevron": true, "cycle_out_in": true, "cycle_out_in_dual": true, "cycle_pinwheel": true, "cycle_spiral": true, "dual_beacon": true, "rainbow_beacon": true, "rainbow_pinwheels": true, "raindrops": true, "jellybean_raindrops": true, "hue_breathing": true, "hue_pendulum": true, "hue_wave": true, "pixel_rain": true, "pixel_flow": true, "pixel_fractal": true, "typing_heatmap": true, "digital_rain": true, "solid_reactive_simple": true, "solid_reactive": true, "solid_reactive_wide": true, "solid_reactive_multiwide": true, "solid_reactive_cross": true, "solid_reactive_multicross": true, "solid_reactive_nexus": true, "solid_reactive_multinexus": true, "splash": true, "multisplash": true, "solid_splash": true, "solid_multisplash": true}, "driver": "ws2812", "layout": [{"flags": 4, "matrix": [0, 0], "x": 28, "y": 12}, {"flags": 4, "matrix": [0, 1], "x": 84, "y": 12}, {"flags": 4, "matrix": [0, 2], "x": 140, "y": 12}, {"flags": 4, "matrix": [0, 3], "x": 196, "y": 12}, {"flags": 2, "x": 196, "y": 17}, {"flags": 4, "matrix": [1, 3], "x": 196, "y": 28}, {"flags": 4, "matrix": [1, 2], "x": 140, "y": 28}, {"flags": 4, "matrix": [1, 1], "x": 84, "y": 28}, {"flags": 2, "x": 28, "y": 17}, {"flags": 4, "matrix": [1, 0], "x": 28, "y": 28}, {"flags": 4, "matrix": [2, 0], "x": 28, "y": 44}, {"flags": 4, "matrix": [2, 1], "x": 84, "y": 44}, {"flags": 4, "matrix": [2, 2], "x": 140, "y": 44}, {"flags": 4, "matrix": [2, 3], "x": 196, "y": 44}, {"flags": 2, "x": 196, "y": 47}, {"flags": 4, "matrix": [3, 3], "x": 196, "y": 60}, {"flags": 4, "matrix": [3, 2], "x": 140, "y": 60}, {"flags": 4, "matrix": [3, 1], "x": 84, "y": 60}, {"flags": 2, "x": 28, "y": 47}, {"flags": 4, "matrix": [3, 0], "x": 28, "y": 60}], "sleep": true}, "community_layouts": ["ortho_4x4"], "layouts": {"LAYOUT_ortho_4x4": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3}, {"matrix": [3, 1], "x": 1, "y": 3}, {"matrix": [3, 2], "x": 2, "y": 3}, {"matrix": [3, 3], "x": 3, "y": 3}]}}}