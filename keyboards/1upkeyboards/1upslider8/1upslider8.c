/* Copyright 2022 ziptyze
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */


#include "analog.h"
#include "qmk_midi.h"

#include "quantum.h"
#include "quantum/midi/midi.h"
#include "quantum/midi/midi_device.h"

uint8_t divisor = 0;
void slider(void) {
  if (divisor++) { // only run the slider function 1/256 times it's called
    return;
  }
  midi_send_cc(&midi_device, 2, 0x3E, 0x7F - (analogReadPin(SLIDER_PIN) >> 3));
  uprintf("%d string", analogReadPin(SLIDER_PIN));
}

void housekeeping_task_kb(void) {
    slider();
}

static uint32_t oled_logo_timer = 0;
static bool clear_logo = true;
static const char PROGMEM my_logo[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0xC0,
    0xE0, 0xF0, 0x70, 0x70, 0x70, 0x70,
    0xF0, 0xE0, 0xC0, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x63, 0xE7, 0xE7, 0xCE, 0xCE,
    0xCE, 0xCE, 0xFC, 0xFC, 0x78, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0xC0, 0xC0, 0xC1,
    0x01, 0x01, 0x01, 0x01, 0x01, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0xFF,
    0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x07, 0x07, 0x07, 0x07, 0x07,
    0x07, 0x07, 0x07, 0x07, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x1F, 0x1F, 0x1F, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0xFC, 0xFC, 0xFC, 0x1C, 0x1C, 0x1C,
    0x1C, 0x3C, 0xF8, 0xF8, 0xE0, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x7F, 0x7F, 0x7F, 0x70,
    0x70, 0x70, 0x70, 0x78, 0x3F, 0x3F,
    0x0F, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0xF0,
    0xF0, 0xF0, 0x70, 0x70, 0x70, 0x70,
    0x70, 0x70, 0x70, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0xFF, 0xFF, 0xFF, 0xCE, 0xCE,
    0xCE, 0xCE, 0xCE, 0xCE, 0xCE, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0xC0, 0xC1, 0xC1, 0xC1,
    0xC1, 0xC1, 0xC1, 0xC1, 0xC1, 0x81,
    0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF,
    0xFF, 0x39, 0x39, 0x79, 0xF9, 0xF9,
    0xDF, 0x9F, 0x0F, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x07, 0x07, 0x07, 0x00, 0x00, 0x00,
    0x00, 0x03, 0x07, 0x07, 0x06, 0x04,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x1C, 0xBE,
    0xFF, 0x63, 0x63, 0xFF, 0xBE, 0x1C,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x07, 0x0F, 0x1F, 0x18, 0x18, 0x1F,
    0x0F, 0x07, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00
    };

oled_rotation_t oled_init_user(oled_rotation_t rotation) {
	return OLED_ROTATION_270;
}


static const char PROGMEM ou_logo[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0xFC, 0xFC, 0xCF, 0xCF,
    0x03, 0x03, 0x03, 0x03, 0xFF, 0xFF,
    0x03, 0x03, 0xFF, 0xFF, 0x03, 0x03,
    0x03, 0x03, 0xFF, 0xFF, 0x03, 0x03,
    0x03, 0x03, 0xF3, 0xF3, 0x03, 0x03,
    0x0C, 0x0C, 0xFC, 0xFC, 0x00, 0x00,
    0xFF, 0xFF, 0xC0, 0xC0, 0xC0, 0xC0,
    0xFF, 0xFF, 0xF0, 0xF0, 0xC3, 0xC3,
    0xC0, 0xC0, 0xF0, 0xF0, 0xFF, 0xFF,
    0xC0, 0xC0, 0xC0, 0xC0, 0xFC, 0xFC,
    0x0C, 0x0C, 0x0F, 0x0F, 0x03, 0x03,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00
    };


static char PROGMEM caps_on[] = {
    0x00, 0x00, 0x00, 0xF8, 0x04, 0x04,
    0x04, 0x88, 0x00, 0x00, 0xE0, 0x58,
    0x44, 0x58, 0xE0, 0x00, 0x00, 0xFC,
    0x24, 0x24, 0x24, 0x18, 0x00, 0x00,
    0x98, 0x24, 0x24, 0x24, 0xC8, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x01, 0x01, 0x01, 0x00, 0x00, 0x81,
    0xC0, 0xE0, 0xF0, 0xF0, 0xF0, 0xF1,
    0xF0, 0xF1, 0xF0, 0xF0, 0xE0, 0xC0,
    0x80, 0x00, 0x00, 0x01, 0x01, 0x01,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x03, 0x07, 0x0F, 0x1F,
    0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F,
    0x3F, 0x3F, 0x1F, 0x0F, 0x07, 0x03,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00
    };

static char PROGMEM caps_off[] = {
    0x00, 0x00, 0x00, 0xF8, 0x04, 0x04,
    0x04, 0x88, 0x00, 0x00, 0xE0, 0x58,
    0x44, 0x58, 0xE0, 0x00, 0x00, 0xFC,
    0x24, 0x24, 0x24, 0x18, 0x00, 0x00,
    0x98, 0x24, 0x24, 0x24, 0xC8, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x01, 0x01, 0x01, 0x00, 0x00, 0x81,
    0xC0, 0xE0, 0xF0, 0x70, 0x30, 0x31,
    0x30, 0x31, 0x70, 0xF0, 0xE0, 0xC0,
    0x80, 0x00, 0x00, 0x01, 0x01, 0x01,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0xFF, 0xFF, 0x03, 0x01, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x01, 0x03, 0xFF, 0xFF, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x03, 0x07, 0x0F, 0x1E,
    0x3C, 0x38, 0x30, 0x30, 0x30, 0x30,
    0x38, 0x3C, 0x1E, 0x0F, 0x07, 0x03,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00
    };

static const char PROGMEM num_on[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0xFC,
    0x18, 0x20, 0xC0, 0xFC, 0x00, 0x00,
    0x00, 0xFC, 0x00, 0x00, 0x00, 0xFC,
    0x00, 0x00, 0xFC, 0x18, 0x60, 0x80,
    0x60, 0x18, 0xFC, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x01, 0x00, 0x00, 0x00, 0x81,
    0xC0, 0xE0, 0xF0, 0xF0, 0xF1, 0xF1,
    0xF1, 0xF0, 0xF0, 0xF0, 0xE1, 0xC0,
    0x80, 0x01, 0x00, 0x00, 0x01, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x03, 0x07, 0x0F, 0x1F,
    0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F,
    0x3F, 0x3F, 0x1F, 0x0F, 0x07, 0x03,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00
    };

static const char PROGMEM num_off[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0xFC,
    0x18, 0x20, 0xC0, 0xFC, 0x00, 0x00,
    0x00, 0xFC, 0x00, 0x00, 0x00, 0xFC,
    0x00, 0x00, 0xFC, 0x18, 0x60, 0x80,
    0x60, 0x18, 0xFC, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x01, 0x00, 0x00, 0x00, 0x81,
    0xC0, 0xE0, 0xF0, 0x70, 0x31, 0x31,
    0x31, 0x30, 0x70, 0xF0, 0xE1, 0xC0,
    0x80, 0x01, 0x00, 0x00, 0x01, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0xFF, 0xFF, 0x03, 0x01, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x01, 0x03, 0xFF, 0xFF, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x03, 0x07, 0x0F, 0x1E,
    0x3C, 0x38, 0x30, 0x30, 0x30, 0x30,
    0x38, 0x3C, 0x1E, 0x0F, 0x07, 0x03,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00
    };

static const char PROGMEM scrl_on[] = {
    0x00, 0x00, 0x00, 0x98, 0x24, 0x24,
    0x24, 0xC8, 0x00, 0x00, 0xF8, 0x04,
    0x04, 0x04, 0x88, 0x00, 0x00, 0xFC,
    0x24, 0x24, 0xE4, 0x18, 0x00, 0x00,
    0x00, 0xFC, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x01, 0x01, 0x01, 0x00, 0x00, 0x80,
    0xC0, 0xE1, 0xF1, 0xF1, 0xF0, 0xF0,
    0xF0, 0xF1, 0xF0, 0xF0, 0xE0, 0xC1,
    0x80, 0x00, 0x00, 0x01, 0x01, 0x01,
    0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x03, 0x07, 0x0F, 0x1F,
    0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F,
    0x3F, 0x3F, 0x1F, 0x0F, 0x07, 0x03,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00
    };

static const char PROGMEM scrl_off[] = {
    0x00, 0x00, 0x00, 0x98, 0x24, 0x24,
    0x24, 0xC8, 0x00, 0x00, 0xF8, 0x04,
    0x04, 0x04, 0x88, 0x00, 0x00, 0xFC,
    0x24, 0x24, 0xE4, 0x18, 0x00, 0x00,
    0x00, 0xFC, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x01, 0x01, 0x01, 0x00, 0x00, 0x80,
    0xC0, 0xE1, 0xF1, 0x71, 0x30, 0x30,
    0x30, 0x31, 0x70, 0xF0, 0xE0, 0xC1,
    0x80, 0x00, 0x00, 0x01, 0x01, 0x01,
    0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0xFF, 0xFF, 0x03, 0x01, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x01, 0x03, 0xFF, 0xFF, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x03, 0x07, 0x0F, 0x1E,
    0x3C, 0x38, 0x30, 0x30, 0x30, 0x30,
    0x38, 0x3C, 0x1E, 0x0F, 0x07, 0x03,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00
    };

static void render_oled(void) {
    const int combined_size = sizeof(ou_logo) + sizeof(caps_off) + sizeof(num_off) + sizeof(scrl_off);
    char combined[combined_size];
    led_t led_state = host_keyboard_led_state();

    memcpy_P(combined, ou_logo, sizeof(ou_logo));

    if (led_state.caps_lock) {
        memcpy_P(combined + sizeof(ou_logo), caps_on, sizeof(caps_off));
    }
    else {
        memcpy_P(combined + sizeof(ou_logo), caps_off, sizeof(caps_off));
    }

    if (led_state.num_lock) {
        memcpy_P(combined + sizeof(ou_logo) + sizeof(caps_off), num_on, sizeof(num_off));
    }
    else {
        memcpy_P(combined + sizeof(ou_logo) + sizeof(caps_off), num_off, sizeof(num_off));
    }

    if (led_state.scroll_lock) {
        memcpy_P(combined + sizeof(ou_logo) + sizeof(caps_off) + sizeof(num_off), scrl_on, sizeof(scrl_off));
    }
    else {
        memcpy_P(combined + sizeof(ou_logo) + sizeof(caps_off) + sizeof(num_off), scrl_off, sizeof(scrl_off));
    }

    oled_write_raw_P(combined, sizeof(combined));
}

void render_logo(void) {
    oled_write_raw_P(my_logo, sizeof(my_logo));
}

void clear_screen(void) {
    if (clear_logo){
      for (uint8_t i = 0; i < OLED_DISPLAY_HEIGHT; ++i) {
        for (uint8_t j = 0; j < OLED_DISPLAY_WIDTH; ++j) {
          oled_write_raw_byte(0x0, i*OLED_DISPLAY_WIDTH + j);
        }
      }
      clear_logo = false;
    }
}

#define MIDI_CONTROL_CHANGE 0xB0

MidiDevice _midi_device;

void midi_callback(uint8_t cable, uint8_t *midi_data, uint16_t length) {
  // Check if this is a MIDI CC message on channel 2
  if ((midi_data[0] & 0xF0) == MIDI_CONTROL_CHANGE && midi_data[0] & (0x0F == 1)) {
    uprintf("%s string", midi_data );
    // ...
  }
}

void slider8_cc_callback(struct _midi_device *dev, uint8_t cable, uint8_t code_index, uint8_t value) {
    midi_callback(MIDI_CONTROL_CHANGE, &value, 1);
}

void init_timer(void){
   oled_logo_timer = timer_read32();
};

void keyboard_post_init_kb(void) {
    init_timer();

    keyboard_post_init_user();
}

void matrix_init_kb(void) {
    midi_register_cc_callback(&_midi_device, slider8_cc_callback);
    matrix_init_user();
}

#ifndef SHOW_LOGO
#    define SHOW_LOGO 5000
#endif
bool oled_task_kb(void) {
    if (!oled_task_user()) { return false; }
    if ((timer_elapsed32(oled_logo_timer) < SHOW_LOGO)){
        render_logo();
    }else{
      clear_screen();
      render_oled();
    }
    return false;
}
