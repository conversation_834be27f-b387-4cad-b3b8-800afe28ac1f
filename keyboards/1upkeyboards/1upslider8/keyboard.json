{"manufacturer": "1upkeyboards", "keyboard_name": "1upslider8", "maintainer": "z<PERSON><PERSON><PERSON>", "processor": "RP2040", "bootloader": "rp2040", "usb": {"device_version": "1.0.0", "pid": "0x5611", "vid": "0x6F75"}, "dynamic_keymap": {"layer_count": 10}, "features": {"bootmagic": true, "extrakey": true, "mousekey": true, "nkro": false, "oled": true, "midi": true, "rgb_matrix": true, "encoder": true}, "encoder": {"rotary": [{"pin_a": "GP21", "pin_b": "GP22"}]}, "matrix_pins": {"direct": [["GP27", "GP17", "GP18", "GP19", "GP20", "GP15", "GP14", "GP13", "GP16"]]}, "bootmagic": {"matrix": [0, 1]}, "ws2812": {"pin": "GP26", "driver": "vendor"}, "rgb_matrix": {"animations": {"gradient_up_down": true, "gradient_left_right": true, "breathing": true, "band_sat": true, "band_val": true, "band_pinwheel_sat": true, "band_pinwheel_val": true, "band_spiral_sat": true, "band_spiral_val": true, "cycle_all": true, "cycle_left_right": true, "cycle_up_down": true, "rainbow_moving_chevron": true, "cycle_out_in": true, "cycle_out_in_dual": true, "cycle_pinwheel": true, "cycle_spiral": true, "dual_beacon": true, "rainbow_beacon": true, "rainbow_pinwheels": true, "raindrops": true, "jellybean_raindrops": true, "hue_breathing": true, "hue_pendulum": true, "hue_wave": true, "typing_heatmap": true, "solid_reactive_simple": true, "solid_reactive": true, "solid_reactive_wide": true, "solid_reactive_multiwide": true, "solid_reactive_cross": true, "solid_reactive_multicross": true, "solid_reactive_nexus": true, "solid_reactive_multinexus": true, "splash": true, "multisplash": true, "solid_splash": true, "solid_multisplash": true}, "driver": "ws2812", "layout": [{"flags": 4, "matrix": [0, 1], "x": 28, "y": 16}, {"flags": 4, "matrix": [0, 2], "x": 84, "y": 16}, {"flags": 4, "matrix": [0, 3], "x": 140, "y": 16}, {"flags": 4, "matrix": [0, 4], "x": 196, "y": 16}, {"flags": 4, "matrix": [0, 5], "x": 28, "y": 48}, {"flags": 4, "matrix": [0, 6], "x": 84, "y": 48}, {"flags": 4, "matrix": [0, 7], "x": 140, "y": 48}, {"flags": 4, "matrix": [0, 8], "x": 196, "y": 48}], "sleep": true}, "layouts": {"LAYOUT": {"layout": [{"label": "enc", "matrix": [0, 0], "x": 3, "y": 0}, {"label": "1", "matrix": [0, 1], "x": 0, "y": 1}, {"label": "2", "matrix": [0, 2], "x": 1, "y": 1}, {"label": "3", "matrix": [0, 3], "x": 2, "y": 1}, {"label": "4", "matrix": [0, 4], "x": 3, "y": 1}, {"label": "5", "matrix": [0, 5], "x": 0, "y": 2}, {"label": "6", "matrix": [0, 6], "x": 1, "y": 2}, {"label": "7", "matrix": [0, 7], "x": 2, "y": 2}, {"label": "8", "matrix": [0, 8], "x": 3, "y": 2}]}}}