# 1upocarina

This keyboard is the Ocarina from 1upkeyboards. It is a five button keypad designed for osu. There is an option to add an encoder to the center position, and the keypad features in-switch per-key addressable RGB leds, as well as 27 right angle underglow RGB leds for very bright and smooth lighting effects.

* Keyboard Maintainer: [ziptyze](https://github.com/ziptyze)

Make example for this keyboard (after setting up your build environment):

    make 1upkeyboards/1upocarina:default

Flashing example for this keyboard:

    make 1upkeyboards/1upocarina:default:flash

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).

## Bootloader

Enter the bootloader in 2 ways:

* **Bootmagic reset**: Hold down the key at (0,0) in the matrix (usually the top left key or Escape) and plug in the keyboard
* **Physical reset button**: Hold the button on the back of the PCB and plug in the keyboard
