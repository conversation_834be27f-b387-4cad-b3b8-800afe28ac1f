# Super 16 Macro Pad

A 4x4 macropad with RGB underglow.

Keyboard Maintainer: [MechMerlin](https://github.com/mechmerlin)  
Hardware Supported: Super 16 Macropad  
Hardware Availability: [1upkeyboards](https://www.1upkeyboards.com/shop/keyboard-kits/super-16-macro-pad/)

Make example for this keyboard (after setting up your build environment):

    make 1upkeyboards/super16:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
