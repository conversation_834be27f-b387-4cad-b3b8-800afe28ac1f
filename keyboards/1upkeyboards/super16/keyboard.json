{"keyboard_name": "super16", "manufacturer": "1upkeyboards", "maintainer": "qmk", "usb": {"vid": "0x6F75", "pid": "0x5516", "device_version": "0.0.1"}, "ws2812": {"pin": "D3"}, "rgblight": {"saturation_steps": 8, "brightness_steps": 8, "led_count": 16, "sleep": true, "animations": {"breathing": true, "rainbow_mood": true, "rainbow_swirl": true, "snake": true, "knight": true, "christmas": true, "static_gradient": true, "rgb_test": true, "alternating": true, "twinkle": true}}, "rgb_matrix": {"animations": {"alphas_mods": true, "gradient_up_down": true, "gradient_left_right": true, "breathing": true, "band_sat": true, "band_val": true, "band_pinwheel_sat": true, "band_pinwheel_val": true, "band_spiral_sat": true, "band_spiral_val": true, "cycle_all": true, "cycle_left_right": true, "cycle_up_down": true, "rainbow_moving_chevron": true, "cycle_out_in": true, "cycle_out_in_dual": true, "cycle_pinwheel": true, "cycle_spiral": true, "dual_beacon": true, "rainbow_beacon": true, "rainbow_pinwheels": true, "raindrops": true, "jellybean_raindrops": true, "hue_breathing": true, "hue_pendulum": true, "hue_wave": true, "pixel_rain": true, "pixel_flow": true, "pixel_fractal": true, "typing_heatmap": true, "digital_rain": true, "solid_reactive_simple": true, "solid_reactive": true, "solid_reactive_wide": true, "solid_reactive_multiwide": true, "solid_reactive_cross": true, "solid_reactive_multicross": true, "solid_reactive_nexus": true, "solid_reactive_multinexus": true, "splash": true, "multisplash": true, "solid_splash": true, "solid_multisplash": true}, "driver": "ws2812"}, "features": {"bootmagic": true, "extrakey": true, "mousekey": true, "nkro": false, "rgb_matrix": true}, "qmk": {"locking": {"enabled": true, "resync": true}}, "matrix_pins": {"cols": ["D4", "C6", "F6", "F7"], "rows": ["D1", "D0", "F4", "F5"]}, "diode_direction": "COL2ROW", "development_board": "promicro", "community_layouts": ["ortho_4x4", "numpad_4x4"], "layouts": {"LAYOUT_ortho_4x4": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3}, {"matrix": [3, 1], "x": 1, "y": 3}, {"matrix": [3, 2], "x": 2, "y": 3}, {"matrix": [3, 3], "x": 3, "y": 3}]}, "LAYOUT_numpad_4x4": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0, "h": 2}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2, "h": 2}, {"matrix": [3, 1], "x": 0, "y": 3, "w": 2}, {"matrix": [3, 2], "x": 2, "y": 3}]}}}