{"keyboard_name": "pi50", "manufacturer": "1upkeyboards", "maintainer": "z<PERSON><PERSON><PERSON>", "processor": "RP2040", "bootloader": "rp2040", "board": "GENERIC_RP_RP2040", "usb": {"vid": "0x6F75", "pid": "0x5606", "device_version": "1.0.0"}, "diode_direction": "COL2ROW", "dynamic_keymap": {"layer_count": 10}, "features": {"bootmagic": true, "extrakey": true, "mousekey": true, "nkro": false, "rgb_matrix": true, "oled": true}, "qmk": {"locking": {"enabled": true, "resync": true}}, "matrix_pins": {"rows": ["GP20", "GP15", "GP19", "GP14", "GP18", "GP13", "GP17", "GP12", "GP16", "GP21"], "cols": ["GP1", "GP2", "GP3", "GP4", "GP5", "GP6", "GP9"]}, "encoder": {"enabled": true, "rotary": [{"pin_a": "GP8", "pin_b": "GP7"}]}, "rgb_matrix": {"driver": "ws2812", "max_brightness": 150, "animations": {"alphas_mods": true, "gradient_up_down": true, "gradient_left_right": true, "breathing": true, "band_sat": true, "band_val": true, "band_pinwheel_sat": true, "band_pinwheel_val": true, "band_spiral_sat": true, "band_spiral_val": true, "cycle_all": true, "cycle_left_right": true, "cycle_up_down": true, "cycle_out_in": true, "cycle_out_in_dual": true, "rainbow_moving_chevron": true, "cycle_pinwheel": true, "cycle_spiral": true, "dual_beacon": true, "rainbow_beacon": true, "rainbow_pinwheels": true, "raindrops": true, "jellybean_raindrops": true, "hue_breathing": true, "hue_pendulum": true, "hue_wave": true, "pixel_fractal": true, "pixel_flow": true, "pixel_rain": true, "typing_heatmap": true, "digital_rain": true, "solid_reactive_simple": true, "solid_reactive": true, "solid_reactive_wide": true, "solid_reactive_multiwide": true, "solid_reactive_cross": true, "solid_reactive_multicross": true, "solid_reactive_nexus": true, "solid_reactive_multinexus": true, "splash": true, "multisplash": true, "solid_splash": true, "solid_multisplash": true}, "sleep": true}, "ws2812": {"pin": "GP0", "driver": "vendor"}}