{"rgb_matrix": {"layout": [{"flags": 1, "matrix": [0, 0], "x": 10, "y": 7}, {"flags": 4, "matrix": [1, 0], "x": 28, "y": 7}, {"flags": 4, "matrix": [0, 1], "x": 46, "y": 7}, {"flags": 4, "matrix": [1, 1], "x": 65, "y": 7}, {"flags": 4, "matrix": [0, 2], "x": 84, "y": 7}, {"flags": 4, "matrix": [1, 2], "x": 102, "y": 7}, {"flags": 4, "matrix": [0, 3], "x": 121, "y": 7}, {"flags": 4, "matrix": [1, 3], "x": 140, "y": 7}, {"flags": 4, "matrix": [0, 4], "x": 159, "y": 7}, {"flags": 4, "matrix": [1, 4], "x": 177, "y": 7}, {"flags": 4, "matrix": [0, 5], "x": 196, "y": 7}, {"flags": 1, "matrix": [1, 5], "x": 215, "y": 7}, {"flags": 1, "matrix": [3, 5], "x": 215, "y": 19}, {"flags": 4, "matrix": [2, 5], "x": 196, "y": 19}, {"flags": 4, "matrix": [3, 4], "x": 177, "y": 19}, {"flags": 4, "matrix": [2, 4], "x": 159, "y": 19}, {"flags": 4, "matrix": [3, 3], "x": 140, "y": 19}, {"flags": 4, "matrix": [2, 3], "x": 121, "y": 19}, {"flags": 4, "matrix": [3, 2], "x": 102, "y": 19}, {"flags": 4, "matrix": [2, 2], "x": 84, "y": 19}, {"flags": 4, "matrix": [3, 1], "x": 65, "y": 19}, {"flags": 4, "matrix": [2, 1], "x": 46, "y": 19}, {"flags": 4, "matrix": [3, 0], "x": 28, "y": 19}, {"flags": 1, "matrix": [2, 0], "x": 9, "y": 19}, {"flags": 1, "matrix": [4, 0], "x": 9, "y": 32}, {"flags": 4, "matrix": [5, 0], "x": 28, "y": 32}, {"flags": 4, "matrix": [4, 1], "x": 46, "y": 32}, {"flags": 4, "matrix": [5, 1], "x": 65, "y": 32}, {"flags": 4, "matrix": [4, 2], "x": 84, "y": 32}, {"flags": 4, "matrix": [5, 2], "x": 102, "y": 32}, {"flags": 4, "matrix": [4, 3], "x": 121, "y": 32}, {"flags": 4, "matrix": [5, 3], "x": 140, "y": 32}, {"flags": 4, "matrix": [4, 4], "x": 159, "y": 32}, {"flags": 4, "matrix": [5, 4], "x": 177, "y": 32}, {"flags": 4, "matrix": [4, 5], "x": 196, "y": 32}, {"flags": 1, "matrix": [5, 5], "x": 215, "y": 32}, {"flags": 1, "matrix": [7, 5], "x": 215, "y": 45}, {"flags": 4, "matrix": [6, 5], "x": 196, "y": 45}, {"flags": 4, "matrix": [7, 4], "x": 177, "y": 45}, {"flags": 4, "matrix": [6, 4], "x": 159, "y": 45}, {"flags": 4, "matrix": [7, 3], "x": 140, "y": 45}, {"flags": 4, "matrix": [6, 3], "x": 121, "y": 45}, {"flags": 4, "matrix": [7, 2], "x": 102, "y": 45}, {"flags": 4, "matrix": [6, 2], "x": 84, "y": 45}, {"flags": 4, "matrix": [7, 1], "x": 65, "y": 45}, {"flags": 4, "matrix": [6, 1], "x": 46, "y": 45}, {"flags": 4, "matrix": [7, 0], "x": 28, "y": 45}, {"flags": 4, "matrix": [6, 0], "x": 9, "y": 45}, {"flags": 1, "matrix": [8, 0], "x": 9, "y": 57}, {"flags": 1, "matrix": [9, 0], "x": 28, "y": 57}, {"flags": 1, "matrix": [8, 1], "x": 46, "y": 57}, {"flags": 1, "matrix": [9, 1], "x": 65, "y": 57}, {"flags": 1, "matrix": [8, 2], "x": 84, "y": 57}, {"flags": 1, "matrix": [9, 2], "x": 112, "y": 57}, {"flags": 1, "matrix": [9, 3], "x": 140, "y": 57}, {"flags": 1, "matrix": [8, 4], "x": 159, "y": 57}, {"flags": 1, "matrix": [9, 4], "x": 177, "y": 57}, {"flags": 1, "matrix": [8, 5], "x": 196, "y": 57}, {"flags": 1, "matrix": [9, 5], "x": 215, "y": 57}]}, "layouts": {"LAYOUT_ortho_5x12": {"layout": [{"matrix": [0, 6], "x": 11, "y": 0}, {"matrix": [0, 0], "x": 0, "y": 1}, {"matrix": [1, 0], "x": 1, "y": 1}, {"matrix": [0, 1], "x": 2, "y": 1}, {"matrix": [1, 1], "x": 3, "y": 1}, {"matrix": [0, 2], "x": 4, "y": 1}, {"matrix": [1, 2], "x": 5, "y": 1}, {"matrix": [0, 3], "x": 6, "y": 1}, {"matrix": [1, 3], "x": 7, "y": 1}, {"matrix": [0, 4], "x": 8, "y": 1}, {"matrix": [1, 4], "x": 9, "y": 1}, {"matrix": [0, 5], "x": 10, "y": 1}, {"matrix": [1, 5], "x": 11, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [3, 0], "x": 1, "y": 2}, {"matrix": [2, 1], "x": 2, "y": 2}, {"matrix": [3, 1], "x": 3, "y": 2}, {"matrix": [2, 2], "x": 4, "y": 2}, {"matrix": [3, 2], "x": 5, "y": 2}, {"matrix": [2, 3], "x": 6, "y": 2}, {"matrix": [3, 3], "x": 7, "y": 2}, {"matrix": [2, 4], "x": 8, "y": 2}, {"matrix": [3, 4], "x": 9, "y": 2}, {"matrix": [2, 5], "x": 10, "y": 2}, {"matrix": [3, 5], "x": 11, "y": 2}, {"matrix": [4, 0], "x": 0, "y": 3}, {"matrix": [5, 0], "x": 1, "y": 3}, {"matrix": [4, 1], "x": 2, "y": 3}, {"matrix": [5, 1], "x": 3, "y": 3}, {"matrix": [4, 2], "x": 4, "y": 3}, {"matrix": [5, 2], "x": 5, "y": 3}, {"matrix": [4, 3], "x": 6, "y": 3}, {"matrix": [5, 3], "x": 7, "y": 3}, {"matrix": [4, 4], "x": 8, "y": 3}, {"matrix": [5, 4], "x": 9, "y": 3}, {"matrix": [4, 5], "x": 10, "y": 3}, {"matrix": [5, 5], "x": 11, "y": 3}, {"matrix": [6, 0], "x": 0, "y": 4}, {"matrix": [7, 0], "x": 1, "y": 4}, {"matrix": [6, 1], "x": 2, "y": 4}, {"matrix": [7, 1], "x": 3, "y": 4}, {"matrix": [6, 2], "x": 4, "y": 4}, {"matrix": [7, 2], "x": 5, "y": 4}, {"matrix": [6, 3], "x": 6, "y": 4}, {"matrix": [7, 3], "x": 7, "y": 4}, {"matrix": [6, 4], "x": 8, "y": 4}, {"matrix": [7, 4], "x": 9, "y": 4}, {"matrix": [6, 5], "x": 10, "y": 4}, {"matrix": [7, 5], "x": 11, "y": 4}, {"matrix": [8, 0], "x": 0, "y": 5}, {"matrix": [9, 0], "x": 1, "y": 5}, {"matrix": [8, 1], "x": 2, "y": 5}, {"matrix": [9, 1], "x": 3, "y": 5}, {"matrix": [8, 2], "x": 4, "y": 5}, {"matrix": [9, 2], "x": 5, "y": 5}, {"matrix": [8, 3], "x": 6, "y": 5}, {"matrix": [9, 3], "x": 7, "y": 5}, {"matrix": [8, 4], "x": 8, "y": 5}, {"matrix": [9, 4], "x": 9, "y": 5}, {"matrix": [8, 5], "x": 10, "y": 5}, {"matrix": [9, 5], "x": 11, "y": 5}]}}}