{"keyboard_name": "1up60hte", "manufacturer": "1upkeyboards", "url": "https://www.1upkeyboards.com/shop/controllers/1up-rgb-60-pcb-hte/", "maintainer": "1upkeyboards", "usb": {"vid": "0x6F75", "pid": "0x6874", "device_version": "0.0.1"}, "build": {"lto": true}, "features": {"backlight": true, "bootmagic": true, "extrakey": true, "mousekey": true, "nkro": true, "rgblight": true}, "matrix_pins": {"cols": ["F6", "F5", "F4", "F1", "E6", "D0", "D1", "D2", "D3", "D5", "D6", "D7", "B4", "B5"], "rows": ["B3", "B2", "B1", "B0", "D4"]}, "diode_direction": "COL2ROW", "qmk": {"locking": {"enabled": true, "resync": true}}, "backlight": {"pin": "B7"}, "indicators": {"caps_lock": "B6", "on_state": 0}, "rgblight": {"saturation_steps": 8, "brightness_steps": 8, "led_count": 14, "animations": {"breathing": true, "rainbow_mood": true, "rainbow_swirl": true, "snake": true, "knight": true, "christmas": true, "static_gradient": true, "rgb_test": true, "alternating": true, "twinkle": true}}, "ws2812": {"pin": "F0"}, "processor": "atmega32u4", "bootloader": "atmel-dfu", "layout_aliases": {"LAYOUT_tsangan": "LAYOUT_60_ansi_tsangan_split_bs_rshift", "LAYOUT_60_tsangan_hhkb": "LAYOUT_60_ansi_tsangan_split_bs_rshift"}, "community_layouts": ["60_hhkb", "60_ansi_t<PERSON>an_split_bs_rshift"], "layouts": {"LAYOUT_60_ansi_tsangan_split_bs_rshift": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [0, 12], "x": 12, "y": 0}, {"matrix": [0, 13], "x": 13, "y": 0}, {"matrix": [4, 13], "x": 14, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1, "w": 1.5}, {"matrix": [1, 1], "x": 1.5, "y": 1}, {"matrix": [1, 2], "x": 2.5, "y": 1}, {"matrix": [1, 3], "x": 3.5, "y": 1}, {"matrix": [1, 4], "x": 4.5, "y": 1}, {"matrix": [1, 5], "x": 5.5, "y": 1}, {"matrix": [1, 6], "x": 6.5, "y": 1}, {"matrix": [1, 7], "x": 7.5, "y": 1}, {"matrix": [1, 8], "x": 8.5, "y": 1}, {"matrix": [1, 9], "x": 9.5, "y": 1}, {"matrix": [1, 10], "x": 10.5, "y": 1}, {"matrix": [1, 11], "x": 11.5, "y": 1}, {"matrix": [1, 12], "x": 12.5, "y": 1}, {"matrix": [1, 13], "x": 13.5, "y": 1, "w": 1.5}, {"matrix": [2, 0], "x": 0, "y": 2, "w": 1.75}, {"matrix": [2, 1], "x": 1.75, "y": 2}, {"matrix": [2, 2], "x": 2.75, "y": 2}, {"matrix": [2, 3], "x": 3.75, "y": 2}, {"matrix": [2, 4], "x": 4.75, "y": 2}, {"matrix": [2, 5], "x": 5.75, "y": 2}, {"matrix": [2, 6], "x": 6.75, "y": 2}, {"matrix": [2, 7], "x": 7.75, "y": 2}, {"matrix": [2, 8], "x": 8.75, "y": 2}, {"matrix": [2, 9], "x": 9.75, "y": 2}, {"matrix": [2, 10], "x": 10.75, "y": 2}, {"matrix": [2, 11], "x": 11.75, "y": 2}, {"matrix": [2, 12], "x": 12.75, "y": 2, "w": 2.25}, {"matrix": [3, 0], "x": 0, "y": 3, "w": 2.25}, {"matrix": [3, 1], "x": 2.25, "y": 3}, {"matrix": [3, 2], "x": 3.25, "y": 3}, {"matrix": [3, 3], "x": 4.25, "y": 3}, {"matrix": [3, 4], "x": 5.25, "y": 3}, {"matrix": [3, 5], "x": 6.25, "y": 3}, {"matrix": [3, 6], "x": 7.25, "y": 3}, {"matrix": [3, 7], "x": 8.25, "y": 3}, {"matrix": [3, 8], "x": 9.25, "y": 3}, {"matrix": [3, 9], "x": 10.25, "y": 3}, {"matrix": [3, 10], "x": 11.25, "y": 3}, {"matrix": [3, 11], "x": 12.25, "y": 3, "w": 1.75}, {"matrix": [3, 12], "x": 14, "y": 3}, {"matrix": [4, 0], "x": 0, "y": 4, "w": 1.5}, {"matrix": [4, 1], "x": 1.5, "y": 4}, {"matrix": [4, 2], "x": 2.5, "y": 4, "w": 1.5}, {"matrix": [4, 6], "x": 4, "y": 4, "w": 7}, {"matrix": [4, 10], "x": 11, "y": 4, "w": 1.5}, {"matrix": [4, 11], "x": 12.5, "y": 4}, {"matrix": [4, 12], "x": 13.5, "y": 4, "w": 1.5}]}, "LAYOUT_60_hhkb": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0}, {"matrix": [0, 5], "x": 5, "y": 0}, {"matrix": [0, 6], "x": 6, "y": 0}, {"matrix": [0, 7], "x": 7, "y": 0}, {"matrix": [0, 8], "x": 8, "y": 0}, {"matrix": [0, 9], "x": 9, "y": 0}, {"matrix": [0, 10], "x": 10, "y": 0}, {"matrix": [0, 11], "x": 11, "y": 0}, {"matrix": [0, 12], "x": 12, "y": 0}, {"matrix": [0, 13], "x": 13, "y": 0}, {"matrix": [4, 13], "x": 14, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1, "w": 1.5}, {"matrix": [1, 1], "x": 1.5, "y": 1}, {"matrix": [1, 2], "x": 2.5, "y": 1}, {"matrix": [1, 3], "x": 3.5, "y": 1}, {"matrix": [1, 4], "x": 4.5, "y": 1}, {"matrix": [1, 5], "x": 5.5, "y": 1}, {"matrix": [1, 6], "x": 6.5, "y": 1}, {"matrix": [1, 7], "x": 7.5, "y": 1}, {"matrix": [1, 8], "x": 8.5, "y": 1}, {"matrix": [1, 9], "x": 9.5, "y": 1}, {"matrix": [1, 10], "x": 10.5, "y": 1}, {"matrix": [1, 11], "x": 11.5, "y": 1}, {"matrix": [1, 12], "x": 12.5, "y": 1}, {"matrix": [1, 13], "x": 13.5, "y": 1, "w": 1.5}, {"matrix": [2, 0], "x": 0, "y": 2, "w": 1.75}, {"matrix": [2, 1], "x": 1.75, "y": 2}, {"matrix": [2, 2], "x": 2.75, "y": 2}, {"matrix": [2, 3], "x": 3.75, "y": 2}, {"matrix": [2, 4], "x": 4.75, "y": 2}, {"matrix": [2, 5], "x": 5.75, "y": 2}, {"matrix": [2, 6], "x": 6.75, "y": 2}, {"matrix": [2, 7], "x": 7.75, "y": 2}, {"matrix": [2, 8], "x": 8.75, "y": 2}, {"matrix": [2, 9], "x": 9.75, "y": 2}, {"matrix": [2, 10], "x": 10.75, "y": 2}, {"matrix": [2, 11], "x": 11.75, "y": 2}, {"matrix": [2, 12], "x": 12.75, "y": 2, "w": 2.25}, {"matrix": [3, 0], "x": 0, "y": 3, "w": 2.25}, {"matrix": [3, 1], "x": 2.25, "y": 3}, {"matrix": [3, 2], "x": 3.25, "y": 3}, {"matrix": [3, 3], "x": 4.25, "y": 3}, {"matrix": [3, 4], "x": 5.25, "y": 3}, {"matrix": [3, 5], "x": 6.25, "y": 3}, {"matrix": [3, 6], "x": 7.25, "y": 3}, {"matrix": [3, 7], "x": 8.25, "y": 3}, {"matrix": [3, 8], "x": 9.25, "y": 3}, {"matrix": [3, 9], "x": 10.25, "y": 3}, {"matrix": [3, 10], "x": 11.25, "y": 3}, {"matrix": [3, 11], "x": 12.25, "y": 3, "w": 1.75}, {"matrix": [3, 12], "x": 14, "y": 3}, {"matrix": [4, 1], "x": 1.5, "y": 4}, {"matrix": [4, 2], "x": 2.5, "y": 4, "w": 1.5}, {"matrix": [4, 6], "x": 4, "y": 4, "w": 7}, {"matrix": [4, 10], "x": 11, "y": 4, "w": 1.5}, {"matrix": [4, 11], "x": 12.5, "y": 4}]}}}