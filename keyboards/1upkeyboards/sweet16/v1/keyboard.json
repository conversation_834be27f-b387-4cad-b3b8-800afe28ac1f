{"keyboard_name": "Sweet16", "manufacturer": "1up <PERSON>", "maintainer": "skullydazed", "usb": {"vid": "0x6F75", "pid": "0x0161", "device_version": "0.0.1"}, "features": {"bootmagic": false, "mousekey": true, "extrakey": true, "nkro": true, "rgblight": true}, "build": {"lto": true}, "qmk": {"locking": {"enabled": true, "resync": true}}, "rgblight": {"saturation_steps": 8, "brightness_steps": 8, "led_count": 1, "animations": {"breathing": true, "rainbow_mood": true, "rainbow_swirl": true, "snake": true, "knight": true, "christmas": true, "static_gradient": true, "rgb_test": true, "alternating": true, "twinkle": true}}, "ws2812": {"pin": "B1"}, "matrix_pins": {"cols": ["D1", "D0", "D4", "C6"], "rows": ["F4", "F5", "F6", "F7"]}, "diode_direction": "COL2ROW", "development_board": "promicro", "layouts": {"LAYOUT_ortho_4x4": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2}, {"matrix": [3, 0], "x": 0, "y": 3}, {"matrix": [3, 1], "x": 1, "y": 3}, {"matrix": [3, 2], "x": 2, "y": 3}, {"matrix": [3, 3], "x": 3, "y": 3}]}, "LAYOUT_numpad_4x4": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0}, {"matrix": [0, 1], "x": 1, "y": 0}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0, "h": 2}, {"matrix": [1, 0], "x": 0, "y": 1}, {"matrix": [1, 1], "x": 1, "y": 1}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [2, 0], "x": 0, "y": 2}, {"matrix": [2, 1], "x": 1, "y": 2}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2, "h": 2}, {"matrix": [3, 1], "x": 0, "y": 3, "w": 2}, {"matrix": [3, 2], "x": 2, "y": 3}]}}}