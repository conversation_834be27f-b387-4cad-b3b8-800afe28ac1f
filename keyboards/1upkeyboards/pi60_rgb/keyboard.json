{"manufacturer": "1upkeyboards", "keyboard_name": "pi60_rgb", "maintainer": "z<PERSON><PERSON><PERSON>", "processor": "RP2040", "bootloader": "rp2040", "board": "GENERIC_RP_RP2040", "usb": {"device_version": "1.0.0", "pid": "0x5602", "vid": "0x6F75"}, "diode_direction": "COL2ROW", "dynamic_keymap": {"layer_count": 10}, "features": {"bootmagic": true, "extrakey": true, "mousekey": true, "nkro": false, "rgb_matrix": true}, "qmk": {"locking": {"enabled": true, "resync": true}}, "ws2812": {"pin": "GP19", "driver": "vendor"}, "matrix_pins": {"cols": ["GP3", "GP4", "GP5", "GP20", "GP18", "GP13", "GP17", "GP16", "GP15", "GP12", "GP11", "GP14", "GP10", "GP9"], "rows": ["GP1", "GP2", "GP8", "GP6", "GP0", "GP21"]}, "mouse_key": {"enabled": true}, "rgb_matrix": {"animations": {"alphas_mods": true, "gradient_up_down": true, "gradient_left_right": true, "breathing": true, "band_sat": true, "band_val": true, "band_pinwheel_sat": true, "band_pinwheel_val": true, "band_spiral_sat": true, "band_spiral_val": true, "cycle_all": true, "cycle_left_right": true, "cycle_up_down": true, "rainbow_moving_chevron": true, "cycle_out_in": true, "cycle_out_in_dual": true, "cycle_pinwheel": true, "cycle_spiral": true, "dual_beacon": true, "rainbow_beacon": true, "rainbow_pinwheels": true, "raindrops": true, "jellybean_raindrops": true, "hue_breathing": true, "hue_pendulum": true, "hue_wave": true, "pixel_rain": true, "pixel_flow": true, "pixel_fractal": true, "typing_heatmap": true, "digital_rain": true, "solid_reactive_simple": true, "solid_reactive": true, "solid_reactive_wide": true, "solid_reactive_multiwide": true, "solid_reactive_cross": true, "solid_reactive_multicross": true, "solid_reactive_nexus": true, "solid_reactive_multinexus": true, "splash": true, "multisplash": true, "solid_splash": true, "solid_multisplash": true}, "driver": "ws2812", "layout": [{"flags": 1, "matrix": [5, 5], "x": 103, "y": 58}, {"flags": 1, "matrix": [5, 10], "x": 159, "y": 58}, {"flags": 1, "matrix": [5, 11], "x": 178, "y": 58}, {"flags": 1, "matrix": [5, 12], "x": 196, "y": 58}, {"flags": 1, "matrix": [5, 13], "x": 215, "y": 58}, {"flags": 1, "matrix": [3, 13], "x": 204, "y": 45}, {"flags": 4, "matrix": [3, 10], "x": 176, "y": 45}, {"flags": 4, "matrix": [3, 9], "x": 161, "y": 45}, {"flags": 4, "matrix": [3, 8], "x": 146, "y": 45}, {"flags": 4, "matrix": [3, 7], "x": 131, "y": 45}, {"flags": 4, "matrix": [3, 6], "x": 116, "y": 45}, {"flags": 4, "matrix": [3, 5], "x": 101, "y": 45}, {"flags": 4, "matrix": [3, 4], "x": 86, "y": 45}, {"flags": 4, "matrix": [3, 3], "x": 71, "y": 45}, {"flags": 4, "matrix": [3, 2], "x": 56, "y": 45}, {"flags": 4, "matrix": [3, 1], "x": 41, "y": 45}, {"flags": 1, "matrix": [4, 2], "x": 47, "y": 58}, {"flags": 1, "matrix": [4, 1], "x": 28, "y": 58}, {"flags": 1, "matrix": [4, 0], "x": 10, "y": 58}, {"flags": 1, "matrix": [3, 0], "x": 17, "y": 45}, {"flags": 1, "matrix": [2, 0], "x": 13, "y": 32}, {"flags": 4, "matrix": [2, 1], "x": 34, "y": 32}, {"flags": 4, "matrix": [2, 2], "x": 49, "y": 32}, {"flags": 4, "matrix": [2, 3], "x": 64, "y": 32}, {"flags": 4, "matrix": [2, 4], "x": 79, "y": 32}, {"flags": 4, "matrix": [2, 5], "x": 94, "y": 32}, {"flags": 4, "matrix": [2, 6], "x": 109, "y": 32}, {"flags": 4, "matrix": [2, 7], "x": 124, "y": 32}, {"flags": 4, "matrix": [2, 8], "x": 139, "y": 32}, {"flags": 4, "matrix": [2, 9], "x": 153, "y": 32}, {"flags": 4, "matrix": [2, 10], "x": 168, "y": 32}, {"flags": 4, "matrix": [2, 11], "x": 183, "y": 32}, {"flags": 1, "matrix": [2, 13], "x": 208, "y": 32}, {"flags": 1, "matrix": [1, 13], "x": 213, "y": 19}, {"flags": 4, "matrix": [1, 12], "x": 195, "y": 19}, {"flags": 4, "matrix": [1, 11], "x": 180, "y": 19}, {"flags": 4, "matrix": [1, 10], "x": 165, "y": 19}, {"flags": 4, "matrix": [1, 9], "x": 150, "y": 19}, {"flags": 4, "matrix": [1, 8], "x": 135, "y": 19}, {"flags": 4, "matrix": [1, 7], "x": 120, "y": 19}, {"flags": 4, "matrix": [1, 6], "x": 105, "y": 19}, {"flags": 4, "matrix": [1, 5], "x": 90, "y": 19}, {"flags": 4, "matrix": [1, 4], "x": 75, "y": 19}, {"flags": 4, "matrix": [1, 3], "x": 60, "y": 19}, {"flags": 4, "matrix": [1, 2], "x": 45, "y": 19}, {"flags": 4, "matrix": [1, 1], "x": 30, "y": 19}, {"flags": 1, "matrix": [1, 0], "x": 12, "y": 19}, {"flags": 1, "matrix": [0, 0], "x": 8, "y": 7}, {"flags": 4, "matrix": [0, 1], "x": 23, "y": 7}, {"flags": 4, "matrix": [0, 2], "x": 38, "y": 7}, {"flags": 4, "matrix": [0, 3], "x": 53, "y": 7}, {"flags": 4, "matrix": [0, 4], "x": 68, "y": 7}, {"flags": 4, "matrix": [0, 5], "x": 83, "y": 7}, {"flags": 4, "matrix": [0, 6], "x": 98, "y": 7}, {"flags": 4, "matrix": [0, 7], "x": 113, "y": 7}, {"flags": 4, "matrix": [0, 8], "x": 127, "y": 7}, {"flags": 4, "matrix": [0, 9], "x": 142, "y": 7}, {"flags": 4, "matrix": [0, 10], "x": 157, "y": 7}, {"flags": 4, "matrix": [0, 11], "x": 172, "y": 7}, {"flags": 4, "matrix": [0, 12], "x": 187, "y": 7}, {"flags": 1, "matrix": [0, 13], "x": 209, "y": 7}], "max_brightness": 125, "sleep": true}, "layouts": {"LAYOUT_60_ansi": {"layout": [{"label": "Esc", "matrix": [0, 0], "x": 0, "y": 0}, {"label": "1", "matrix": [0, 1], "x": 1, "y": 0}, {"label": "2", "matrix": [0, 2], "x": 2, "y": 0}, {"label": "3", "matrix": [0, 3], "x": 3, "y": 0}, {"label": "4", "matrix": [0, 4], "x": 4, "y": 0}, {"label": "5", "matrix": [0, 5], "x": 5, "y": 0}, {"label": "6", "matrix": [0, 6], "x": 6, "y": 0}, {"label": "7", "matrix": [0, 7], "x": 7, "y": 0}, {"label": "8", "matrix": [0, 8], "x": 8, "y": 0}, {"label": "9", "matrix": [0, 9], "x": 9, "y": 0}, {"label": "0", "matrix": [0, 10], "x": 10, "y": 0}, {"label": "-", "matrix": [0, 11], "x": 11, "y": 0}, {"label": "=", "matrix": [0, 12], "x": 12, "y": 0}, {"label": "BS", "matrix": [0, 13], "w": 2, "x": 13, "y": 0}, {"label": "Tab", "matrix": [1, 0], "w": 1.5, "x": 0, "y": 1}, {"label": "Q", "matrix": [1, 1], "x": 1.5, "y": 1}, {"label": "W", "matrix": [1, 2], "x": 2.5, "y": 1}, {"label": "E", "matrix": [1, 3], "x": 3.5, "y": 1}, {"label": "R", "matrix": [1, 4], "x": 4.5, "y": 1}, {"label": "T", "matrix": [1, 5], "x": 5.5, "y": 1}, {"label": "Y", "matrix": [1, 6], "x": 6.5, "y": 1}, {"label": "U", "matrix": [1, 7], "x": 7.5, "y": 1}, {"label": "I", "matrix": [1, 8], "x": 8.5, "y": 1}, {"label": "O", "matrix": [1, 9], "x": 9.5, "y": 1}, {"label": "P", "matrix": [1, 10], "x": 10.5, "y": 1}, {"label": "[", "matrix": [1, 11], "x": 11.5, "y": 1}, {"label": "]", "matrix": [1, 12], "x": 12.5, "y": 1}, {"label": "|", "matrix": [1, 13], "w": 1.5, "x": 13.5, "y": 1}, {"label": "Caps", "matrix": [2, 0], "w": 1.75, "x": 0, "y": 2}, {"label": "A", "matrix": [2, 1], "x": 1.75, "y": 2}, {"label": "S", "matrix": [2, 2], "x": 2.75, "y": 2}, {"label": "D", "matrix": [2, 3], "x": 3.75, "y": 2}, {"label": "F", "matrix": [2, 4], "x": 4.75, "y": 2}, {"label": "G", "matrix": [2, 5], "x": 5.75, "y": 2}, {"label": "H", "matrix": [2, 6], "x": 6.75, "y": 2}, {"label": "J", "matrix": [2, 7], "x": 7.75, "y": 2}, {"label": "K", "matrix": [2, 8], "x": 8.75, "y": 2}, {"label": "L", "matrix": [2, 9], "x": 9.75, "y": 2}, {"label": ";", "matrix": [2, 10], "x": 10.75, "y": 2}, {"label": "'", "matrix": [2, 11], "x": 11.75, "y": 2}, {"label": "Enter", "matrix": [2, 13], "w": 2.25, "x": 12.75, "y": 2}, {"label": "LShift", "matrix": [3, 0], "w": 2.25, "x": 0, "y": 3}, {"label": "Z", "matrix": [3, 1], "x": 2.25, "y": 3}, {"label": "X", "matrix": [3, 2], "x": 3.25, "y": 3}, {"label": "C", "matrix": [3, 3], "x": 4.25, "y": 3}, {"label": "V", "matrix": [3, 4], "x": 5.25, "y": 3}, {"label": "B", "matrix": [3, 5], "x": 6.25, "y": 3}, {"label": "N", "matrix": [3, 6], "x": 7.25, "y": 3}, {"label": "M", "matrix": [3, 7], "x": 8.25, "y": 3}, {"label": ",", "matrix": [3, 8], "x": 9.25, "y": 3}, {"label": ".", "matrix": [3, 9], "x": 10.25, "y": 3}, {"label": "/", "matrix": [3, 10], "x": 11.25, "y": 3}, {"label": "RShift", "matrix": [3, 13], "w": 2.75, "x": 12.25, "y": 3}, {"label": "LCtrl", "matrix": [4, 0], "w": 1.25, "x": 0, "y": 4}, {"label": "<PERSON><PERSON><PERSON>", "matrix": [4, 1], "w": 1.25, "x": 1.25, "y": 4}, {"label": "LAlt", "matrix": [4, 2], "w": 1.25, "x": 2.5, "y": 4}, {"label": "Space", "matrix": [5, 5], "w": 6.25, "x": 3.75, "y": 4}, {"label": "RAlt", "matrix": [5, 9], "w": 1.25, "x": 10, "y": 4}, {"label": "R<PERSON><PERSON>", "matrix": [5, 10], "w": 1.25, "x": 11.25, "y": 4}, {"label": "FN", "matrix": [5, 12], "w": 1.25, "x": 12.5, "y": 4}, {"label": "RCtrl", "matrix": [5, 13], "w": 1.25, "x": 13.75, "y": 4}]}}}