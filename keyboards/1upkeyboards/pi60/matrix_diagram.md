# Matrix Diagram for 1upkeyboards pi60

```
                                                    ┌───────┐
                                       2u Backspace │0D     │
                                                    └───────┘
┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┐
│00 │01 │02 │03 │04 │05 │06 │07 │08 │09 │0A │0B │0C │0D │0E │ ─ Switch or Encoder
├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┤      ┌─────┐                ┌─────┐                           ┌─────┐
│10   │11 │12 │13 │14 │15 │16 │17 │18 │19 │1A │1B │1C │1D   │      │     │                │1D   │                           │1D   │
├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤   ┌──┴┐2D  │ ISO Enter   ┌──┴┬────┤ 1u/1.25u Split Enter   ┌──┴─┬───┤ 1.25u/1u Split Enter
│2F    │21 │22 │23 │24 │25 │26 │27 │28 │29 │2A │2B │2D      │   │2C │    │             │2C │2D  │                        │2C  │2D │
├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴┬───┬───┤   └───┴────┘             └───┴────┘                        └────┴───┘
│3F  │31 │32 │33 │34 │35 │36 │37 │38 │39 │3A │3B    │3C │3E │─┐
└────┴───┴───┴───┴───┴───┴───┴───┴───┴───┴───┴──────┴───┴───┘ │
┌────────┐                                   ┌───┬──────┬───┐ │
│3F      │ 2.25u LShift   1u/1.75u/1u RShift │3B │3C    │3E │─┼─ Switch or Encoder
└────────┘                                   └───┴──────┴───┘ │
                                             ┌───┐  ┌───┬───┐ │
                   1u/0.75u Gap/1u/1u RShift │3B │  │3C │3E │─┘
                                             └───┘  └───┴───┘

[Bottom Rows]────────────────────────────────────────────────
────────────────────[6u & 6.25u Spacebar]────────────────────
┌────┬────┬────┬────────────────────────┬────┬────┬────┬────┐
│4F  │41  │42  │46                      │4A  │4B  │4D  │4E  │
└────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
┌─────┬───┬────┬──────────┬────┬────────┬────┬────┬───┬─────┐
│4F   │41 │42  │44        │46  │48      │4A  │4B  │4D │4E   │
└─────┴───┴────┴──────────┴────┴────────┴────┴────┴───┴─────┘
               ┌────────┬────┬──────────┬───┬───┬───┬───┬───┐
               │44      │46  │48        │4A │4B │4C │4D │4E │
               └────────┴────┴──────────┴───┴───┴───┴───┴───┘
                ┌───────────────────────┬───┬─────┬────┬────┐
                │46                     │4A │4B   │4D  │4E  │
                └───────────────────────┴───┴─────┴────┴────┘
                                        ┌───┬─────┬───┬─────┐
                                        │4A │4B   │4D │4E   │
                                        └───┴─────┴───┴─────┘

────────────────────────[7u Spacebar]────────────────────────
┌────┬────┬─────┬───────────────────────────┬───┬───┬───┬───┐
│4F  │41  │42   │46                         │4B │4C │4D │4E │
└────┴────┴─────┴───────────────────────────┴───┴───┴───┴───┘
┌─────┬───┬─────┬──────────┬─────┬──────────┬─────┬────┬────┐
│4F   │41 │42   │44        │46   │48        │4B   │4D  │4E  │
└─────┴───┴─────┴──────────┴─────┴──────────┴─────┴────┴────┘
                ┌───────────┬───┬───────────┬─────┬───┬─────┐
                │44         │46 │48         │4B   │4D │4E   │
                └───────────┴───┴───────────┴─────┴───┴─────┘
                              └─ Switch or Encoder

───────────────────────[10u Spacebar]────────────────────────
┌────┬────┬───────────────────────────────────────┬────┬────┐
│4F  │41  │46                                     │4D  │4E  │
└────┴────┴───────────────────────────────────────┴────┴────┘
┌─────┬───┬───────────────────────────────────────┬───┬─────┐
│4F   │41 │46                                     │4D │4E   │
└─────┴───┴───────────────────────────────────────┴───┴─────┘
```
