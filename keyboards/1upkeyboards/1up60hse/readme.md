# 1up60hse (hot swap edition)

![1up60hse](https://i2.wp.com/www.1upkeyboards.com/wp-content/uploads/2018/08/PCB-1up-60-hotswap-group-RGB.jpg?fit=1280%2C1280&ssl=1)

A 60% PCB with USB C, RGB underglow, backlighting, hotswappable switches, and a standard ANSI layout.

Keyboard Maintainer: [MechMerlin](https://github.com/mechmerlin)  
Hardware Supported: 1up60hse 60% PCB.  
Hardware Availability: [1upkeyboards.com](https://www.1upkeyboards.com/shop/controllers/1up-rgb-pcb-hse/)

Make example for this keyboard (after setting up your build environment):

    make 1upkeyboards/1up60hse:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
