{"keyboard_name": "pi40", "manufacturer": "1upkeyboards", "maintainer": "z<PERSON><PERSON><PERSON>", "processor": "RP2040", "bootloader": "rp2040", "usb": {"vid": "0x6F75", "pid": "0x5600", "device_version": "1.0.0"}, "diode_direction": "COL2ROW", "dynamic_keymap": {"layer_count": 10}, "ws2812": {"pin": "GP0", "driver": "vendor"}, "features": {"bootmagic": true, "extrakey": true, "mousekey": true, "nkro": false, "oled": true, "rgb_matrix": true}, "matrix_pins": {"rows": ["GP21", "GP20", "GP19", "GP18"], "cols": ["GP1", "GP2", "GP3", "GP4", "GP5", "GP6", "GP7", "GP8", "GP9", "GP10", "GP11", "GP12", "GP22"]}, "encoder": {"enabled": true, "rotary": [{"pin_a": "GP14", "pin_b": "GP13"}]}, "rgb_matrix": {"driver": "ws2812", "layout": [{"flags": 1, "matrix": [0, 0], "x": 9, "y": 8}, {"flags": 4, "matrix": [0, 1], "x": 28, "y": 8}, {"flags": 4, "matrix": [0, 2], "x": 46, "y": 8}, {"flags": 4, "matrix": [0, 3], "x": 65, "y": 8}, {"flags": 4, "matrix": [0, 4], "x": 84, "y": 8}, {"flags": 4, "matrix": [0, 5], "x": 102, "y": 8}, {"flags": 4, "matrix": [0, 6], "x": 121, "y": 8}, {"flags": 4, "matrix": [0, 7], "x": 140, "y": 8}, {"flags": 4, "matrix": [0, 8], "x": 159, "y": 8}, {"flags": 4, "matrix": [0, 9], "x": 177, "y": 8}, {"flags": 4, "matrix": [0, 10], "x": 196, "y": 8}, {"flags": 1, "matrix": [0, 11], "x": 215, "y": 8}, {"flags": 1, "matrix": [1, 11], "x": 215, "y": 24}, {"flags": 4, "matrix": [1, 10], "x": 196, "y": 24}, {"flags": 4, "matrix": [1, 9], "x": 177, "y": 24}, {"flags": 4, "matrix": [1, 8], "x": 159, "y": 24}, {"flags": 4, "matrix": [1, 7], "x": 140, "y": 24}, {"flags": 4, "matrix": [1, 6], "x": 121, "y": 24}, {"flags": 4, "matrix": [1, 5], "x": 102, "y": 24}, {"flags": 4, "matrix": [1, 4], "x": 84, "y": 24}, {"flags": 4, "matrix": [1, 3], "x": 65, "y": 24}, {"flags": 4, "matrix": [1, 2], "x": 46, "y": 24}, {"flags": 4, "matrix": [1, 1], "x": 28, "y": 24}, {"flags": 1, "matrix": [1, 0], "x": 9, "y": 24}, {"flags": 1, "matrix": [2, 0], "x": 9, "y": 40}, {"flags": 4, "matrix": [2, 1], "x": 28, "y": 40}, {"flags": 4, "matrix": [2, 2], "x": 46, "y": 40}, {"flags": 4, "matrix": [2, 3], "x": 65, "y": 40}, {"flags": 4, "matrix": [2, 4], "x": 84, "y": 40}, {"flags": 4, "matrix": [2, 5], "x": 102, "y": 40}, {"flags": 4, "matrix": [2, 6], "x": 121, "y": 40}, {"flags": 4, "matrix": [2, 7], "x": 140, "y": 40}, {"flags": 4, "matrix": [2, 8], "x": 159, "y": 40}, {"flags": 4, "matrix": [2, 9], "x": 177, "y": 40}, {"flags": 4, "matrix": [2, 10], "x": 196, "y": 40}, {"flags": 1, "matrix": [2, 11], "x": 215, "y": 40}, {"flags": 1, "matrix": [3, 11], "x": 215, "y": 56}, {"flags": 1, "matrix": [3, 10], "x": 196, "y": 56}, {"flags": 1, "matrix": [3, 9], "x": 177, "y": 56}, {"flags": 1, "matrix": [3, 8], "x": 159, "y": 56}, {"flags": 1, "matrix": [3, 7], "x": 140, "y": 56}, {"flags": 1, "matrix": [3, 5], "x": 112, "y": 56}, {"flags": 1, "matrix": [3, 4], "x": 84, "y": 56}, {"flags": 1, "matrix": [3, 3], "x": 65, "y": 56}, {"flags": 1, "matrix": [3, 2], "x": 46, "y": 56}, {"flags": 1, "matrix": [3, 1], "x": 28, "y": 56}, {"flags": 1, "matrix": [3, 0], "x": 9, "y": 56}]}, "layouts": {"LAYOUT_ortho_4x12": {"layout": [{"matrix": [3, 6], "x": 11, "y": 0}, {"matrix": [0, 0], "x": 0, "y": 1}, {"matrix": [0, 1], "x": 1, "y": 1}, {"matrix": [0, 2], "x": 2, "y": 1}, {"matrix": [0, 3], "x": 3, "y": 1}, {"matrix": [0, 4], "x": 4, "y": 1}, {"matrix": [0, 5], "x": 5, "y": 1}, {"matrix": [0, 6], "x": 6, "y": 1}, {"matrix": [0, 7], "x": 7, "y": 1}, {"matrix": [0, 8], "x": 8, "y": 1}, {"matrix": [0, 9], "x": 9, "y": 1}, {"matrix": [0, 10], "x": 10, "y": 1}, {"matrix": [0, 11], "x": 11, "y": 1}, {"matrix": [1, 0], "x": 0, "y": 2}, {"matrix": [1, 1], "x": 1, "y": 2}, {"matrix": [1, 2], "x": 2, "y": 2}, {"matrix": [1, 3], "x": 3, "y": 2}, {"matrix": [1, 4], "x": 4, "y": 2}, {"matrix": [1, 5], "x": 5, "y": 2}, {"matrix": [1, 6], "x": 6, "y": 2}, {"matrix": [1, 7], "x": 7, "y": 2}, {"matrix": [1, 8], "x": 8, "y": 2}, {"matrix": [1, 9], "x": 9, "y": 2}, {"matrix": [1, 10], "x": 10, "y": 2}, {"matrix": [1, 11], "x": 11, "y": 2}, {"matrix": [2, 0], "x": 0, "y": 3}, {"matrix": [2, 1], "x": 1, "y": 3}, {"matrix": [2, 2], "x": 2, "y": 3}, {"matrix": [2, 3], "x": 3, "y": 3}, {"matrix": [2, 4], "x": 4, "y": 3}, {"matrix": [2, 5], "x": 5, "y": 3}, {"matrix": [2, 6], "x": 6, "y": 3}, {"matrix": [2, 7], "x": 7, "y": 3}, {"matrix": [2, 8], "x": 8, "y": 3}, {"matrix": [2, 9], "x": 9, "y": 3}, {"matrix": [2, 10], "x": 10, "y": 3}, {"matrix": [2, 11], "x": 11, "y": 3}, {"matrix": [3, 0], "x": 0, "y": 4}, {"matrix": [3, 1], "x": 1, "y": 4}, {"matrix": [3, 2], "x": 2, "y": 4}, {"matrix": [3, 3], "x": 3, "y": 4}, {"matrix": [3, 4], "x": 4, "y": 4}, {"matrix": [3, 5], "x": 5, "y": 4}, {"matrix": [0, 12], "x": 6, "y": 4}, {"matrix": [3, 7], "x": 7, "y": 4}, {"matrix": [3, 8], "x": 8, "y": 4}, {"matrix": [3, 9], "x": 9, "y": 4}, {"matrix": [3, 10], "x": 10, "y": 4}, {"matrix": [3, 11], "x": 11, "y": 4}]}}}