{"keyboard": "adm42/rev4", "keymap": "default", "commit": "3bf01bb9ed202b14f78105db2aa2a75d01fc4323", "layout": "LAYOUT", "layers": [["LWIN_T(KC_GRV)", "KC_Q", "KC_W", "KC_E", "KC_R", "KC_T", "KC_Y", "KC_U", "KC_I", "KC_O", "KC_P", "RWIN_T(KC_EQL)", "LCTL_T(KC_TAB)", "KC_A", "KC_S", "KC_D", "KC_F", "KC_G", "KC_H", "KC_J", "KC_K", "KC_L", "KC_SCLN", "RCTL_T(KC_QUOT)", "KC_LALT", "KC_Z", "KC_X", "KC_C", "KC_V", "KC_B", "KC_N", "KC_M", "KC_COMM", "KC_DOT", "KC_SLSH", "KC_LALT", "LT(2,KC_ESC)", "LSFT_T(KC_BSPC)", "LT(4,KC_DEL)", "LT(3,KC_ENT)", "RSFT_T(KC_SPC)", "LT(2,KC_RALT)"], ["LWIN_T(KC_GRV)", "KC_Q", "KC_W", "KC_F", "KC_P", "KC_B", "KC_J", "KC_L", "KC_U", "KC_Y", "KC_SCLN", "RWIN_T(KC_EQL)", "LCTL_T(KC_TAB)", "KC_A", "KC_R", "KC_S", "KC_T", "KC_G", "KC_M", "KC_N", "KC_E", "KC_I", "KC_O", "RCTL_T(KC_QUOT)", "KC_LALT", "KC_Z", "KC_X", "KC_C", "KC_D", "KC_V", "KC_K", "KC_H", "KC_COMM", "KC_DOT", "KC_SLSH", "KC_LALT", "LT(2,KC_ESC)", "LSFT_T(KC_BSPC)", "LT(4,KC_DEL)", "LT(3,KC_ENT)", "RSFT_T(KC_SPC)", "LT(2,KC_RALT)"], ["_______", "KC_1", "KC_2", "KC_3", "KC_4", "KC_5", "KC_6", "KC_7", "KC_8", "KC_9", "KC_0", "RWIN_T(KC_BSLS)", "KC_LCTL", "KC_LBRC", "KC_RBRC", "KC_LPRN", "KC_RPRN", "KC_EXLM", "KC_LEFT", "KC_DOWN", "KC_UP", "KC_RGHT", "KC_MINS", "KC_RCTL", "_______", "KC_AMPR", "KC_AT", "KC_LCBR", "KC_RCBR", "KC_PIPE", "KC_UNDS", "KC_ASTR", "KC_HASH", "KC_PERC", "KC_TILD", "_______", "KC_ESC", "_______", "KC_DEL", "KC_ENT", "_______", "KC_RALT"], ["LWIN_T(KC_F11)", "KC_F1", "KC_F2", "KC_F3", "KC_F4", "KC_F5", "KC_F6", "KC_F7", "KC_F8", "KC_F9", "KC_F10", "RWIN_T(KC_F12)", "KC_LCTL", "KC_PAUS", "KC_INS", "KC_VOLD", "KC_VOLU", "KC_MUTE", "KC_HOME", "KC_PGDN", "KC_PGUP", "KC_END", "KC_APP", "KC_RCTL", "_______", "KC_SLEP", "KC_PWR", "KC_MSTP", "KC_MNXT", "KC_MPLY", "_______", "KC_BRID", "KC_BRIU", "KC_PSCR", "KC_WAKE", "_______", "KC_CAPS", "_______", "_______", "_______", "_______", "KC_CAPS"], ["XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "RGB_M_B", "RM_VALD", "RM_VALU", "RM_SATD", "RM_SATU", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "RM_TOGG", "RM_NEXT", "RM_PREV", "RM_HUED", "RM_HUEU", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "RGB_M_P", "RM_SPDD", "RM_SPDU", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "MO(5)", "XXXXXXX", "XXXXXXX"], ["XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "DF(0)", "DF(1)", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "EE_CLR", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX", "XXXXXXX"]]}