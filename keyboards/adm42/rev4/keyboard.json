{"keyboard_name": "ADM42", "manufacturer": "<PERSON>", "url": "https://adm42.dev/", "maintainer": "<PERSON><PERSON><PERSON>", "debounce": 10, "usb": {"vid": "0x04D8", "pid": "0xE873", "device_version": "0.0.1"}, "features": {"bootmagic": true, "extrakey": true, "mousekey": true, "nkro": true, "rgb_matrix": true}, "ws2812": {"pin": "B7"}, "rgb_matrix": {"driver": "ws2812", "animations": {"breathing": true, "band_val": true, "cycle_left_right": true, "cycle_up_down": true, "cycle_out_in": true, "cycle_pinwheel": true, "jellybean_raindrops": true, "pixel_fractal": true, "pixel_rain": true, "typing_heatmap": true, "digital_rain": true, "solid_reactive_simple": true, "splash": true}, "layout": [{"matrix": [3, 6], "x": 112, "y": 32, "flags": 4}, {"matrix": [2, 5], "x": 92, "y": 36, "flags": 4}, {"matrix": [1, 5], "x": 94, "y": 25, "flags": 4}, {"matrix": [0, 5], "x": 97, "y": 14, "flags": 4}, {"matrix": [0, 4], "x": 80, "y": 13, "flags": 4}, {"matrix": [1, 4], "x": 77, "y": 24, "flags": 4}, {"matrix": [1, 3], "x": 62, "y": 18, "flags": 4}, {"matrix": [0, 3], "x": 64, "y": 8, "flags": 4}, {"matrix": [0, 2], "x": 49, "y": 6, "flags": 4}, {"matrix": [1, 2], "x": 46, "y": 17, "flags": 4}, {"matrix": [1, 1], "x": 28, "y": 23, "flags": 4}, {"matrix": [0, 1], "x": 30, "y": 13, "flags": 4}, {"matrix": [0, 0], "x": 14, "y": 11, "flags": 4}, {"matrix": [1, 0], "x": 11, "y": 22, "flags": 4}, {"matrix": [2, 0], "x": 8, "y": 32, "flags": 4}, {"matrix": [2, 1], "x": 26, "y": 34, "flags": 4}, {"matrix": [2, 2], "x": 43, "y": 28, "flags": 4}, {"matrix": [2, 3], "x": 61, "y": 29, "flags": 4}, {"matrix": [2, 4], "x": 76, "y": 34, "flags": 4}, {"matrix": [3, 3], "x": 78, "y": 46, "flags": 4}, {"matrix": [3, 4], "x": 97, "y": 49, "flags": 4}, {"matrix": [3, 5], "x": 112, "y": 56, "flags": 4}, {"matrix": [3, 7], "x": 127, "y": 49, "flags": 4}, {"matrix": [3, 8], "x": 146, "y": 46, "flags": 4}, {"matrix": [2, 6], "x": 132, "y": 36, "flags": 4}, {"matrix": [2, 7], "x": 148, "y": 34, "flags": 4}, {"matrix": [2, 8], "x": 164, "y": 29, "flags": 4}, {"matrix": [2, 9], "x": 180, "y": 28, "flags": 4}, {"matrix": [2, 10], "x": 198, "y": 34, "flags": 4}, {"matrix": [2, 11], "x": 215, "y": 32, "flags": 4}, {"matrix": [1, 11], "x": 212, "y": 22, "flags": 4}, {"matrix": [1, 10], "x": 196, "y": 23, "flags": 4}, {"matrix": [1, 9], "x": 178, "y": 17, "flags": 4}, {"matrix": [1, 8], "x": 161, "y": 18, "flags": 4}, {"matrix": [1, 7], "x": 146, "y": 24, "flags": 4}, {"matrix": [1, 6], "x": 130, "y": 25, "flags": 4}, {"matrix": [0, 6], "x": 126, "y": 14, "flags": 4}, {"matrix": [0, 7], "x": 143, "y": 13, "flags": 4}, {"matrix": [0, 8], "x": 159, "y": 8, "flags": 4}, {"matrix": [0, 9], "x": 175, "y": 6, "flags": 4}, {"matrix": [0, 10], "x": 194, "y": 13, "flags": 4}, {"matrix": [0, 11], "x": 210, "y": 11, "flags": 4}], "led_process_limit": 21, "max_brightness": 170, "sat_steps": 24, "sleep": true}, "qmk": {"tap_keycode_delay": 1}, "build": {"debounce_type": "sym_eager_pk", "lto": true}, "matrix_pins": {"cols": ["C6", "B6", "B5", "B4", "D7", "D6", "F0", "F1", "F4", "F5", "F6", "F7"], "rows": ["C7", "D5", "D3", "D2"]}, "diode_direction": "ROW2COL", "processor": "atmega32u4", "bootloader": "atmel-dfu", "layout_aliases": {"LAYOUT_adm42_3x12_6": "LAYOUT"}, "layouts": {"LAYOUT": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0.8}, {"matrix": [0, 1], "x": 1, "y": 0.8}, {"matrix": [0, 2], "x": 2, "y": 0}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0.5}, {"matrix": [0, 5], "x": 5, "y": 0.5}, {"matrix": [0, 6], "x": 7.5, "y": 0.5}, {"matrix": [0, 7], "x": 8.5, "y": 0.5}, {"matrix": [0, 8], "x": 9.5, "y": 0}, {"matrix": [0, 9], "x": 10.5, "y": 0}, {"matrix": [0, 10], "x": 11.5, "y": 0.8}, {"matrix": [0, 11], "x": 12.5, "y": 0.8}, {"matrix": [1, 0], "x": 0, "y": 1.8}, {"matrix": [1, 1], "x": 1, "y": 1.8}, {"matrix": [1, 2], "x": 2, "y": 1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [1, 4], "x": 4, "y": 1.5}, {"matrix": [1, 5], "x": 5, "y": 1.5}, {"matrix": [1, 6], "x": 7.5, "y": 1.5}, {"matrix": [1, 7], "x": 8.5, "y": 1.5}, {"matrix": [1, 8], "x": 9.5, "y": 1}, {"matrix": [1, 9], "x": 10.5, "y": 1}, {"matrix": [1, 10], "x": 11.5, "y": 1.8}, {"matrix": [1, 11], "x": 12.5, "y": 1.8}, {"matrix": [2, 0], "x": 0, "y": 2.8}, {"matrix": [2, 1], "x": 1, "y": 2.8}, {"matrix": [2, 2], "x": 2, "y": 2}, {"matrix": [2, 3], "x": 3, "y": 2}, {"matrix": [2, 4], "x": 4, "y": 2.5}, {"matrix": [2, 5], "x": 5, "y": 2.5}, {"matrix": [2, 6], "x": 7.5, "y": 2.5}, {"matrix": [2, 7], "x": 8.5, "y": 2.5}, {"matrix": [2, 8], "x": 9.5, "y": 2}, {"matrix": [2, 9], "x": 10.5, "y": 2}, {"matrix": [2, 10], "x": 11.5, "y": 2.8}, {"matrix": [2, 11], "x": 12.5, "y": 2.8}, {"matrix": [3, 3], "x": 4.25, "y": 3.55}, {"matrix": [3, 4], "x": 5.25, "y": 3.65}, {"matrix": [3, 6], "x": 6.25, "y": 1.875, "h": 1.25}, {"matrix": [3, 5], "x": 6.25, "y": 4.05}, {"matrix": [3, 7], "x": 7.25, "y": 3.65}, {"matrix": [3, 8], "x": 8.25, "y": 3.55}]}}}