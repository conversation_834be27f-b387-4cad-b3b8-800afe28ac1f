# Cassette42

![cassette42](https://pbs.twimg.com/media/D63q5S0UcAE9Rfj?format=jpg&name=large)

An audio control pad with 4 switches and 2 rotary encoders.

* Keyboard Maintainer: [monksoffunk](https://github.com/monksoffunk)  [@monksoffunkJP](https://twitter.com/monksoffunkJP)
* Hardware Supported: Cassette 42 PCB
* Hardware Availability: [Yushakobo Shop](https://yushakobo.jp/shop/cassette42/)

Make example for this keyboard (after setting up your build environment):

    make 25keys/cassette42:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
