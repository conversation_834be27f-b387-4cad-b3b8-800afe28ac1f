# ALETH42

![ALETH42](https://i.imgur.com/6hJVBQl.png)

40% keyboard with rotary encoder (optional)

* Keyboard Maintainer: [monksoffunk](https://github.com/monksoffunk/) [@monksoffunkJP](https://twitter.com/monksoffunkJP)
* Hardware Supported: ALETH42 PCB
* Hardware Availability: [twitter](https://twitter.com/monksoffunkJP)

Make example for this keyboard (after setting up your build environment):

    make aleth42:default

See the [build environment setup](https://docs.qmk.fm/#/getting_started_build_tools) and the [make instructions](https://docs.qmk.fm/#/getting_started_make_guide) for more information. Brand new to QMK? Start with our [Complete Newbs Guide](https://docs.qmk.fm/#/newbs).
