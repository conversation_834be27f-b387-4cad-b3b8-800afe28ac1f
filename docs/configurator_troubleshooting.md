# Configurator Troubleshooting

## My .json file is not working

If the .json file was generated with QMK Configurator, congratulations you have stumbled upon a bug. File an issue at [qmk_configurator](https://github.com/qmk/qmk_configurator/issues).

If not... how did you miss the big bold message at the top saying not to use other .json files?

## There are extra spaces in my layout? What do I do?

If you're referring to having three spots for space bar, the best course of action is to just fill them all with Space. The same can be done for Backspace and Shift keys.

## What is the keycode for...

Please see:

* [Basic Keycode Reference](keycodes_basic)
* [Advanced Keycode Reference](feature_advanced_keycodes)

## It won't compile

Please double check the other layers of your keymap to make sure there are no random keys present.

## Problems and Bugs

We are always accepting customer requests and bug reports. Please file them at [qmk_configurator](https://github.com/qmk/qmk_configurator/issues).
