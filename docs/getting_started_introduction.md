# Introduction

This page attempts to explain the basic information you need to know to work with the QMK project. It assumes that you are familiar with navigating a Unix shell, but does not assume you are familiar with C or with compiling using make.

## Basic QMK Structure

QMK is a fork of [<PERSON>ako](https://github.com/tmk)'s [tmk_keyboard](https://github.com/tmk/tmk_keyboard) project. The original TMK code, with modifications, can be found in the `tmk_core` folder. The QMK additions to the project may be found in the `quantum` folder. Keyboard projects may be found in the `keyboards` folder.

### Userspace Structure

Within the folder `users` is a directory for each user. This is a place for users to put code that they might use between keyboards. See the docs for [Userspace feature](feature_userspace) for more information.

### Keyboard Project Structure

Within the folder `keyboards`, its subfolder `handwired` and its vendor and manufacture subdirectories e.g. `clueboard` is a directory for each keyboard project, for example `qmk_firmware/keyboards/clueboard/2x1800`. Within it, you'll find the following structure:

* `keymaps/`: Different keymaps that can be built
* `rules.mk`: The file that sets the default "make" options. Do not edit this file directly, instead use a keymap specific `rules.mk`.
* `config.h`: The file that sets the default compile time options. Do not edit this file directly, instead use a keymap specific `config.h`.
* `info.json`: The file used for setting layout for QMK Configurator. See [Configurator Support](reference_configurator_support) for more information.
* `readme.md`: A brief overview of the keyboard.
* `<keyboardName>.h`: This file is where the keyboard layout is defined against the keyboard's switch matrix.
* `<keyboardName>.c`: This file is where you can find custom code for the keyboard.  

For more information on project structure, see [QMK Keyboard Guidelines](hardware_keyboard_guidelines).

### Keymap Structure

In every keymap folder, the following files may be found. Only `keymap.c` is required, and if the rest of the files are not found the default options will be chosen.

* `config.h`: the options to configure your keymap
* `keymap.c`: all of your keymap code, required
* `rules.mk`: the features of QMK that are enabled
* `readme.md`: a description of your keymap, how others might use it, and explanations of features. Please upload images to a service like imgur.

# The `config.h` File

There are 3 possible `config.h` locations:

* keyboard (`/keyboards/<keyboard>/config.h`)
* userspace (`/users/<user>/config.h`)
* keymap (`/keyboards/<keyboard>/keymaps/<keymap>/config.h`)

The build system automatically picks up the config files in the above order. If you wish to override any setting set by a previous `config.h` you will need to first include some boilerplate code for the settings you wish to change.

```
#pragma once
```

Then to override a setting from the previous `config.h` file you must `#undef` and then `#define` the setting again.

The boilerplate code and setting look like this together:

```
#pragma once

// overrides go here!
#undef MY_SETTING
#define MY_SETTING 4
```
