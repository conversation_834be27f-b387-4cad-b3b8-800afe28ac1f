# Best Git Practices for Working with QMK

## Or, "How I Learned to Stop Worrying and Love Git."

This section aims to instruct novices in the best ways to have a smooth experience in contributing to QMK. We will walk through the process of contributing to QMK, detailing some ways to make this task easier, and then later we'll break some things in order to teach you how to fix them.

This section assumes a few things:

1. You have a GitHub account, and have [forked the qmk_firmware repository](getting_started_github) to your account.
2. You've set up both [your build environment](newbs_getting_started#set-up-your-environment) and [QMK](newbs_getting_started#set-up-qmk).

---

- Part 1: [Your Fork's Master: Update Often, Commit Never](newbs_git_using_your_master_branch)
- Part 2: [Resolving Merge Conflicts](newbs_git_resolving_merge_conflicts)
- Part 3: [Resynchronizing an Out-of-Sync Git Branch](newbs_git_resynchronize_a_branch)
