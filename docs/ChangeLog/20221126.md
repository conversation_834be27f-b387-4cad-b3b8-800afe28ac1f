# QMK Breaking Changes - 2022 November 26 Changelog

## Notable Features {#notable-features}

### Autocorrect ([#15699](https://github.com/qmk/qmk_firmware/pull/15699)) {#autocorrect}

_@getreuer_ in their infinite wisdom decided that autocorrect was a feature needed by QMK. As is customary, _@drashna_ adapted it to core and got it into a state that everyone else can use it. See [Feature: Autocorrect](../features/autocorrect) for more ifnormation (grin).

## Changes Requiring User Action {#changes-requiring-user-action}

### Updated Keyboard Codebases {#updated-keyboard-codebases}

The following keyboards have had their source moved within QMK:

| Old Keyboard Name                    | New Keyboard Name                    |
|--------------------------------------|--------------------------------------|
| converter/numeric_keypad_IIe         | converter/numeric_keypad_iie         |
| durgod/k3x0/k310                     | durgod/k310                          |
| durgod/k3x0/k320                     | durgod/k320                          |
| emptystring/NQG                      | emptystring/nqg                      |
| handwired/hillside/46                | hillside/46                          |
| handwired/hillside/48                | hillside/48                          |
| handwired/hillside/52                | hillside/52                          |
| maple_computing/christmas_tree/V2017 | maple_computing/christmas_tree/v2017 |

### Keycodes refactoring {#keycodes-overhaul-user-action}

QMK's keycodes got a very significant overhaul this breaking changes cycle, with the bulk of the work done by _@zvecr_ and _@fauxpark_ -- renaming, reordering, removing has been their focus in this area. In an attempt to standardise interoperation with host applications, keycode values now have strong versioning so that any connected application has confidence that the keys it thinks exist on the board actually match up with what's compiled in. These strongly-versioned keycode definitions are now published online and will not change, so tools that remap keycodes have a reference to work with. In future versions of QMK, any new or changed keycodes will result in a new version specification. See [API docs](../api_docs#qmk-constants) for more information on the published versions if you're writing a tool to manage keycodes.

In most cases user keymaps in the repository have already been updated to reflect the new naming scheme. In some cases user keymaps outside the repository may strike a missing keycode with the old name -- it's highly likely that the name had already been deprecated for some time, and should have been updated previously.

See below for the full list of changesets.

::: warning
Keycode aliases have been put in place in most cases to cater for "old names" being mapped to "new names" -- the documentation already reflects all the new naming of keys.
:::

### Configuration Item Refactoring {#config-refactoring}

A number of configuration items have been renamed for consistency.

RGB Matrix configuration:

| Old Config              | New Config              |
|-------------------------|-------------------------|
| DRIVER_LED_COUNT        | RGB_MATRIX_LED_COUNT    |
| RGB_DISABLE_TIMEOUT     | RGB_MATRIX_TIMEOUT      |
| RGB_MATRIX_STARTUP_HUE  | RGB_MATRIX_DEFAULT_HUE  |
| RGB_MATRIX_STARTUP_MODE | RGB_MATRIX_DEFAULT_MODE |
| RGB_MATRIX_STARTUP_SAT  | RGB_MATRIX_DEFAULT_SAT  |
| RGB_MATRIX_STARTUP_SPD  | RGB_MATRIX_DEFAULT_SPD  |
| RGB_MATRIX_STARTUP_VAL  | RGB_MATRIX_DEFAULT_VAL  |

LED Matrix configuration:

| Old Config              | New Config              |
|-------------------------|-------------------------|
| DRIVER_LED_COUNT        | LED_MATRIX_LED_COUNT    |
| LED_DISABLE_TIMEOUT     | LED_MATRIX_TIMEOUT      |
| LED_MATRIX_STARTUP_MODE | LED_MATRIX_DEFAULT_MODE |
| LED_MATRIX_STARTUP_SPD  | LED_MATRIX_DEFAULT_SPD  |
| LED_MATRIX_STARTUP_VAL  | LED_MATRIX_DEFAULT_VAL  |

Joystick configuration:

| Old Config               | New Config               |
|--------------------------|--------------------------|
| JOYSTICK_AXES_COUNT      | JOYSTICK_AXIS_COUNT      |
| JOYSTICK_AXES_RESOLUTION | JOYSTICK_AXIS_RESOLUTION |

### Data-driven USB IDs Refactoring ([#18152](https://github.com/qmk/qmk_firmware/pull/18152)) {#usb-ids-Refactoring}

QMK has decided to deprecate the specification of USB IDs inside `config.h` in favour of `info.json`, leaving data-driven as the only method to specify USB information. As per the deprecation schedule put forward last breaking changes cycle, USB information must be specified in `info.json` instead.

Previously in `config.h`:
```c
#define VENDOR_ID    0x1234
#define PRODUCT_ID   0x5678
#define DEVICE_VER   0x0001
#define MANUFACTURER Me
#define PRODUCT      MyKeyboard
```

Replaced by `info.json`:
```json
{
    "keyboard_name": "MyKeyboard",
    "manufacturer": "Me",
    "usb": {
        "vid": "0x1234",
        "pid": "0x5678",
        "device_version": "0.0.1"
    }
}
```

### LED Indicator callback refactoring ([#14864](https://github.com/qmk/qmk_firmware/pull/18450)) {#led-callback-refactor}

_RGB Matrix_ and _LED Matrix_ Indicator display code was traditionally difficult to override in keymaps as they did not follow the standard pattern of `bool *_kb()` deferring to `bool *_user()` functions, allowing signalling to the higher level that processing had already been done.

This changes the standard callback model to allow for a base implementation to be provided by a keyboard, but also still allow for keymap-level overrides without needing to modify the keyboard's code.

The old RGB Matrix keymap code went something like this:

```c
void rgb_matrix_indicators_user(void) {
    // keymap LED code
}
```

...but the new RGB Matrix keymap code looks like this:
```c
bool rgb_matrix_indicators_user(void) {
    // keymap LED code
    return false;
}
```

Keyboard designers should now structure their keyboard-level routines like the following, in order to allow for keymap overrides:

```c
bool rgb_matrix_indicators_kb(void) {
    // Defer to the keymap if they want to override
    if (!rgb_matrix_indicators_user()) { return false; }

    // keyboard LED code
    return true;
}
```

The equivalent transformations should be done for LED Matrix boards.

### Unicode mode refactoring {#unicode-mode-renaming}

Unicode modes were renamed in order to prevent collision with equivalent keycodes. The available values for `UNICODE_SELECTED_MODES` changed -- see [Feature: Unicode](../features/unicode#setting-the-input-mode) for the new list of values and how to configure them.

## Notable core changes {#notable-core}

This breaking changes cycle, a lot of the core changes are related to cleanup and refactoring -- commonly called "tech debt".

### Keycodes refactoring {#keycodes-overhaul-core-changes}

We aren't going to list each and every change -- they're far too numerous -- instead, we'll just list the related PRs in order to convey just how wide-reaching these changes were:

* Align audio keycode names ([#18962](https://github.com/qmk/qmk_firmware/pull/18962))
* Align dynamic tapping term keycode names ([#18963](https://github.com/qmk/qmk_firmware/pull/18963))
* Align haptic feedback keycode names ([#18964](https://github.com/qmk/qmk_firmware/pull/18964))
* Deprecate `CAPS_WORD`/`CAPSWRD` for `CW_TOGG` ([#18834](https://github.com/qmk/qmk_firmware/pull/18834))
* Deprecate `KC_LEAD` for `QK_LEAD` ([#18792](https://github.com/qmk/qmk_firmware/pull/18792))
* Deprecate `KC_LOCK` for `QK_LOCK` ([#18796](https://github.com/qmk/qmk_firmware/pull/18796))
* Deprecate `KEY_OVERRIDE_*` keycodes for `KO_*` ([#18843](https://github.com/qmk/qmk_firmware/pull/18843))
* Deprecate `ONESHOT_*` keycodes for `QK_ONE_SHOT_*` ([#18844](https://github.com/qmk/qmk_firmware/pull/18844))
* Deprecate `SECURE_*` keycodes for `QK_SECURE_*` ([#18847](https://github.com/qmk/qmk_firmware/pull/18847))
* Deprecate `VLK_TOG` for `VK_TOGG` ([#18807](https://github.com/qmk/qmk_firmware/pull/18807))
* Initial DD keycode migration ([#18643](https://github.com/qmk/qmk_firmware/pull/18643))
* Macro keycode name refactoring ([#18958](https://github.com/qmk/qmk_firmware/pull/18958))
* Move mousekey keycodes into newly freed up keycode block ([#16076](https://github.com/qmk/qmk_firmware/pull/16076))
* Normalise Auto Shift keycodes ([#18892](https://github.com/qmk/qmk_firmware/pull/18892))
* Normalise Autocorrect keycodes ([#18893](https://github.com/qmk/qmk_firmware/pull/18893))
* Normalise Combo keycodes ([#18877](https://github.com/qmk/qmk_firmware/pull/18877))
* Normalise Dynamic Macro keycodes ([#18939](https://github.com/qmk/qmk_firmware/pull/18939))
* Normalise Joystick and Programmable Button keycodes ([#18832](https://github.com/qmk/qmk_firmware/pull/18832))
* Normalise MIDI keycodes ([#18972](https://github.com/qmk/qmk_firmware/pull/18972))
* Normalise output selection (Bluetooth) keycodes ([#19004](https://github.com/qmk/qmk_firmware/pull/19004))
* Normalise Space Cadet keycodes ([#18864](https://github.com/qmk/qmk_firmware/pull/18864))
* Normalise Unicode keycodes ([#18898](https://github.com/qmk/qmk_firmware/pull/18898))
* Publish constants metadata to API ([#19143](https://github.com/qmk/qmk_firmware/pull/19143))
* Relocate US ANSI shifted keycode aliases ([#18634](https://github.com/qmk/qmk_firmware/pull/18634))
* Remove `KC_DELT` ([#18882](https://github.com/qmk/qmk_firmware/pull/18882))
* Remove `UNICODE_KEY_OSX` and `UC_OSX` ([#18290](https://github.com/qmk/qmk_firmware/pull/18290))
* Remove deprecated RESET keycode alias ([#18271](https://github.com/qmk/qmk_firmware/pull/18271))
* Remove legacy Debug keycode ([#18769](https://github.com/qmk/qmk_firmware/pull/18769))
* Remove legacy EEPROM clear keycodes ([#18782](https://github.com/qmk/qmk_firmware/pull/18782))
* Remove legacy fauxclicky and unicode keycodes ([#18800](https://github.com/qmk/qmk_firmware/pull/18800))
* Remove legacy Grave Escape keycodes ([#18787](https://github.com/qmk/qmk_firmware/pull/18787))
* Remove legacy international keycodes ([#18588](https://github.com/qmk/qmk_firmware/pull/18588))
* Remove legacy keycodes, part 2 ([#18660](https://github.com/qmk/qmk_firmware/pull/18660))
* Remove legacy keycodes, part 3 ([#18669](https://github.com/qmk/qmk_firmware/pull/18669))
* Remove legacy keycodes, part 4 ([#18683](https://github.com/qmk/qmk_firmware/pull/18683))
* Remove legacy keycodes, part 5 ([#18710](https://github.com/qmk/qmk_firmware/pull/18710))
* Remove legacy keycodes, part 6 ([#18740](https://github.com/qmk/qmk_firmware/pull/18740))
* Remove legacy locking caps/num/scroll keycodes ([#18601](https://github.com/qmk/qmk_firmware/pull/18601))
* Remove legacy sendstring keycodes ([#18749](https://github.com/qmk/qmk_firmware/pull/18749))
* Reworked backlight keycodes. ([#18961](https://github.com/qmk/qmk_firmware/pull/18961))

### Board Converters {#board-converters}

There was additional work in the space of board converters -- historically QMK allowed for "converting" a Pro Micro build to a QMK Proton-C build. The last few versions of QMK have added support for replacement boards much like the Proton-C, and this quarter was no exception:

* Add Bonsai C4 as a platform board file ([#18901](https://github.com/qmk/qmk_firmware/pull/18901))
* Add converter support to keymap.json ([#18776](https://github.com/qmk/qmk_firmware/pull/18776))
* Add Elite-C to converters ([#18309](https://github.com/qmk/qmk_firmware/pull/18309))
* Add Elite-Pi converter ([#18236](https://github.com/qmk/qmk_firmware/pull/18236))
* Allow QK_MAKE to work with converters ([#18637](https://github.com/qmk/qmk_firmware/pull/18637))

See [Feature: Converters](../feature_converters) for the full list of board conversions available.

### Pointing and Digitizer device updates {#pointing-and-digitizer}

Both pointing devices and digitizer got a host of updates this cycle. Inertia, automatic mouse layers, fixes for preventing sleep... you even get more buttons with digitizers!

* add "inertia" mode for mouse keys ([#18774](https://github.com/qmk/qmk_firmware/pull/18774))
* Digitizer feature improvements ([#19034](https://github.com/qmk/qmk_firmware/pull/19034))
* Enabling Pointing Device support in register code functions ([#18363](https://github.com/qmk/qmk_firmware/pull/18363))
* Feature: pointing device automatic mouse layer ([#17962](https://github.com/qmk/qmk_firmware/pull/17962))
* Fix mouse report comparison failing on shared EP (fixes KB preventing sleep) ([#18060](https://github.com/qmk/qmk_firmware/pull/18060))
* Fix mouse use within send_string ([#18659](https://github.com/qmk/qmk_firmware/pull/18659))
* Handle mouse keys more consistently ([#18513](https://github.com/qmk/qmk_firmware/pull/18513))
* Invert pointing device motion pin for cirque touchpads ([#18404](https://github.com/qmk/qmk_firmware/pull/18404))
* Refactor more host code (programmable button & digitizer) ([#18565](https://github.com/qmk/qmk_firmware/pull/18565))

## Full changelist {#full-changelist}

Core:
* quantum: led: split out led_update_ports() for customization of led behaviour ([#14452](https://github.com/qmk/qmk_firmware/pull/14452))
* Add getreuer's Autocorrect feature to core ([#15699](https://github.com/qmk/qmk_firmware/pull/15699))
* Move mousekey keycodes into newly freed up keycode block ([#16076](https://github.com/qmk/qmk_firmware/pull/16076))
* Introduce pointing device specific debug messages ([#17663](https://github.com/qmk/qmk_firmware/pull/17663))
* PWM Backlight for RP2040 ([#17706](https://github.com/qmk/qmk_firmware/pull/17706))
* Adjust PWM hardware audio driver for RP2040 ([#17723](https://github.com/qmk/qmk_firmware/pull/17723))
* Prevent tap dance from wiping dynamic macros ([#17880](https://github.com/qmk/qmk_firmware/pull/17880))
* Feature: pointing device automatic mouse layer ([#17962](https://github.com/qmk/qmk_firmware/pull/17962))
* Allow custom timings for WS2812 PIO driver ([#18006](https://github.com/qmk/qmk_firmware/pull/18006))
* Use `TAP_CODE_DELAY` for encoder mapping by default. Add docs. ([#18098](https://github.com/qmk/qmk_firmware/pull/18098))
* Move Oneshot mod callbacks to after mods are set ([#18101](https://github.com/qmk/qmk_firmware/pull/18101))
* mcp23018: add return status to init ([#18178](https://github.com/qmk/qmk_firmware/pull/18178))
* Switch over MANUFACTURER and PRODUCT to string literals ([#18183](https://github.com/qmk/qmk_firmware/pull/18183))
* Remove deprecated USBasp and bootloadHID bootloader types ([#18195](https://github.com/qmk/qmk_firmware/pull/18195))
* Chromeos keycodes ([#18212](https://github.com/qmk/qmk_firmware/pull/18212))
* VIA V3 - The Custom UI Update ([#18222](https://github.com/qmk/qmk_firmware/pull/18222))
* Move bootloader.mk to platforms ([#18228](https://github.com/qmk/qmk_firmware/pull/18228))
* Simplify extrakeys sending at the host driver level ([#18230](https://github.com/qmk/qmk_firmware/pull/18230))
* Add unicode mode change callbacks ([#18235](https://github.com/qmk/qmk_firmware/pull/18235))
* Add Elite-Pi converter ([#18236](https://github.com/qmk/qmk_firmware/pull/18236))
* Better handle EEPROM reset keycode ([#18244](https://github.com/qmk/qmk_firmware/pull/18244))
* Work around WinCompose issue for U+Axxx or U+Exxx ([#18260](https://github.com/qmk/qmk_firmware/pull/18260))
* Remove deprecated RESET keycode alias ([#18271](https://github.com/qmk/qmk_firmware/pull/18271))
* Move Bluetooth-related function calls up to host/keyboard level ([#18274](https://github.com/qmk/qmk_firmware/pull/18274))
* Added analog support for WB32 MCU. ([#18289](https://github.com/qmk/qmk_firmware/pull/18289))
* Remove `UNICODE_KEY_OSX` and `UC_OSX` ([#18290](https://github.com/qmk/qmk_firmware/pull/18290))
* Add Elite-C to converters ([#18309](https://github.com/qmk/qmk_firmware/pull/18309))
* RN42 driver: small cleanups ([#18310](https://github.com/qmk/qmk_firmware/pull/18310))
* Reboot wb32 devices after flashing ([#18323](https://github.com/qmk/qmk_firmware/pull/18323))
* Refactor Unicode feature ([#18333](https://github.com/qmk/qmk_firmware/pull/18333))
* Move fake EE_HANDS from EEPROM init. ([#18352](https://github.com/qmk/qmk_firmware/pull/18352))
* Enabling Pointing Device support in register code functions ([#18363](https://github.com/qmk/qmk_firmware/pull/18363))
* Start Bluetooth API ([#18366](https://github.com/qmk/qmk_firmware/pull/18366))
* Add UART support for Kinetis boards ([#18370](https://github.com/qmk/qmk_firmware/pull/18370))
* [QP] Add RGB565 surface. Docs clarification, cleanup, tabsification, and reordering. ([#18396](https://github.com/qmk/qmk_firmware/pull/18396))
* Change `DRIVER_LED_COUNT` to `{LED,RGB}_MATRIX_LED_COUNT` ([#18399](https://github.com/qmk/qmk_firmware/pull/18399))
* Invert pointing device motion pin for cirque touchpads ([#18404](https://github.com/qmk/qmk_firmware/pull/18404))
* Change `{LED,RGB}_DISABLE_TIMEOUT` to `{LED,RGB}_MATRIX_TIMEOUT` ([#18415](https://github.com/qmk/qmk_firmware/pull/18415))
* rewrite locking in split transaction handlers ([#18417](https://github.com/qmk/qmk_firmware/pull/18417))
* remove busy waiting from rgblight functions ([#18418](https://github.com/qmk/qmk_firmware/pull/18418))
* Serial-protocol: always clear receive queue on main half of split keyboard ([#18419](https://github.com/qmk/qmk_firmware/pull/18419))
* Stabilize RP2040 Half-duplex PIO split comms take 2 ([#18421](https://github.com/qmk/qmk_firmware/pull/18421))
* Copy RP2040 vector table to RAM on startup ([#18424](https://github.com/qmk/qmk_firmware/pull/18424))
* Further refactoring of joystick feature ([#18437](https://github.com/qmk/qmk_firmware/pull/18437))
* Start moving towards introspection-based data retrieval ([#18441](https://github.com/qmk/qmk_firmware/pull/18441))
* RP2040: use built-in integer hardware divider and optimized i64 multiplication ([#18464](https://github.com/qmk/qmk_firmware/pull/18464))
* Only trigger encoder callbacks on primary side ([#18467](https://github.com/qmk/qmk_firmware/pull/18467))
* Handle mouse keys more consistently ([#18513](https://github.com/qmk/qmk_firmware/pull/18513))
* Gentoo install script — build newlib with `nano` USE flag ([#18527](https://github.com/qmk/qmk_firmware/pull/18527))
* Small un/register_code() cleanups ([#18544](https://github.com/qmk/qmk_firmware/pull/18544))
* Refactor more host code (programmable button & digitizer) ([#18565](https://github.com/qmk/qmk_firmware/pull/18565))
* Don't clear keys on layer change unless STRICT_LAYER_RELEASE is enabled ([#18577](https://github.com/qmk/qmk_firmware/pull/18577))
* Remove legacy international keycodes ([#18588](https://github.com/qmk/qmk_firmware/pull/18588))
* onekey: Enable ADC for STM32F072 Discovery ([#18592](https://github.com/qmk/qmk_firmware/pull/18592))
* Implement split comms watchdog ([#18599](https://github.com/qmk/qmk_firmware/pull/18599))
* Remove legacy locking caps/num/scroll keycodes ([#18601](https://github.com/qmk/qmk_firmware/pull/18601))
* Use `get_u16_str` instead of `snprintf` in `autoshift_timer_report` ([#18606](https://github.com/qmk/qmk_firmware/pull/18606))
* Refactor `send_extra` ([#18615](https://github.com/qmk/qmk_firmware/pull/18615))
* LUFA: Consolidate report sending code ([#18629](https://github.com/qmk/qmk_firmware/pull/18629))
* Relocate US ANSI shifted keycode aliases ([#18634](https://github.com/qmk/qmk_firmware/pull/18634))
* Allow QK_MAKE to work with converters ([#18637](https://github.com/qmk/qmk_firmware/pull/18637))
* Programmable Button API refactor and improve docs ([#18641](https://github.com/qmk/qmk_firmware/pull/18641))
* Initial DD keycode migration ([#18643](https://github.com/qmk/qmk_firmware/pull/18643))
* Remove legacy keycodes, part 2 ([#18660](https://github.com/qmk/qmk_firmware/pull/18660))
* Remove legacy keycodes, part 3 ([#18669](https://github.com/qmk/qmk_firmware/pull/18669))
* Remove legacy keycodes, part 4 ([#18683](https://github.com/qmk/qmk_firmware/pull/18683))
* Revert "mcp23018: add return status to init" ([#18709](https://github.com/qmk/qmk_firmware/pull/18709))
* Remove legacy keycodes, part 5 ([#18710](https://github.com/qmk/qmk_firmware/pull/18710))
* Make QP driver init functions weak. ([#18717](https://github.com/qmk/qmk_firmware/pull/18717))
* Add unit tests for HOLD_ON_OTHER_KEY_PRESS ([#18721](https://github.com/qmk/qmk_firmware/pull/18721))
* Remove legacy keycodes, part 6 ([#18740](https://github.com/qmk/qmk_firmware/pull/18740))
* Remove legacy sendstring keycodes ([#18749](https://github.com/qmk/qmk_firmware/pull/18749))
* 4 Driver support for IS31FL3737 ([#18750](https://github.com/qmk/qmk_firmware/pull/18750))
* Remove quantum/audio from global VPATH ([#18753](https://github.com/qmk/qmk_firmware/pull/18753))
* Widen the ARM Cortex-M family support. Allow USB peripheral change. ([#18767](https://github.com/qmk/qmk_firmware/pull/18767))
* Remove legacy Debug keycode ([#18769](https://github.com/qmk/qmk_firmware/pull/18769))
* add "inertia" mode for mouse keys ([#18774](https://github.com/qmk/qmk_firmware/pull/18774))
* Remove legacy EEPROM clear keycodes ([#18782](https://github.com/qmk/qmk_firmware/pull/18782))
* Remove legacy Grave Escape keycodes ([#18787](https://github.com/qmk/qmk_firmware/pull/18787))
* Deprecate `KC_LEAD` for `QK_LEAD` ([#18792](https://github.com/qmk/qmk_firmware/pull/18792))
* Deprecate `KC_LOCK` for `QK_LOCK` ([#18796](https://github.com/qmk/qmk_firmware/pull/18796))
* Remove legacy fauxclicky and unicode keycodes ([#18800](https://github.com/qmk/qmk_firmware/pull/18800))
* Generalise CTPC logic from common_features ([#18803](https://github.com/qmk/qmk_firmware/pull/18803))
* Deprecate `VLK_TOG` for `VK_TOGG` ([#18807](https://github.com/qmk/qmk_firmware/pull/18807))
* ChibiOS USB: Add a dummy IN callback to work around LLD bugs ([#18811](https://github.com/qmk/qmk_firmware/pull/18811))
* Normalise Joystick and Programmable Button keycodes ([#18832](https://github.com/qmk/qmk_firmware/pull/18832))
* Deprecate `CAPS_WORD`/`CAPSWRD` for `CW_TOGG` ([#18834](https://github.com/qmk/qmk_firmware/pull/18834))
* added BS_TOGG so BS_SWAP and BS_NORM can be on a single key ([#18837](https://github.com/qmk/qmk_firmware/pull/18837))
* Remove some assumptions on sequential keycode ranges ([#18838](https://github.com/qmk/qmk_firmware/pull/18838))
* Deprecate `KEY_OVERRIDE_*` keycodes for `KO_*` ([#18843](https://github.com/qmk/qmk_firmware/pull/18843))
* Deprecate `ONESHOT_*` keycodes for `QK_ONE_SHOT_*` ([#18844](https://github.com/qmk/qmk_firmware/pull/18844))
* Deprecate `SECURE_*` keycodes for `QK_SECURE_*` ([#18847](https://github.com/qmk/qmk_firmware/pull/18847))
* Normalise Space Cadet keycodes ([#18864](https://github.com/qmk/qmk_firmware/pull/18864))
* Allow overriding of dynamic keymap start address. ([#18867](https://github.com/qmk/qmk_firmware/pull/18867))
* Formalise keyboard- and user-specific EEPROM blocks ([#18874](https://github.com/qmk/qmk_firmware/pull/18874))
* Normalise Combo keycodes ([#18877](https://github.com/qmk/qmk_firmware/pull/18877))
* Remove rgblight_list.h ([#18878](https://github.com/qmk/qmk_firmware/pull/18878))
* Remove `KC_DELT` ([#18882](https://github.com/qmk/qmk_firmware/pull/18882))
* Simplify Keymap Config EEPROM ([#18886](https://github.com/qmk/qmk_firmware/pull/18886))
* Normalise Auto Shift keycodes ([#18892](https://github.com/qmk/qmk_firmware/pull/18892))
* Normalise Autocorrect keycodes ([#18893](https://github.com/qmk/qmk_firmware/pull/18893))
* Normalise Unicode keycodes ([#18898](https://github.com/qmk/qmk_firmware/pull/18898))
* Add Bonsai C4 as a platform board file ([#18901](https://github.com/qmk/qmk_firmware/pull/18901))
* Normalise Dynamic Macro keycodes ([#18939](https://github.com/qmk/qmk_firmware/pull/18939))
* Reduce includes for sequencer header ([#18946](https://github.com/qmk/qmk_firmware/pull/18946))
* Reduce includes for crc header ([#18947](https://github.com/qmk/qmk_firmware/pull/18947))
* Reduce includes for caps_word header ([#18948](https://github.com/qmk/qmk_firmware/pull/18948))
* Reduce includes for wpm header ([#18949](https://github.com/qmk/qmk_firmware/pull/18949))
* Reduce includes for dip_switch header ([#18951](https://github.com/qmk/qmk_firmware/pull/18951))
* Reduce includes for send_string header ([#18952](https://github.com/qmk/qmk_firmware/pull/18952))
* Macro keycode name refactoring ([#18958](https://github.com/qmk/qmk_firmware/pull/18958))
* Remove thermal printer. ([#18959](https://github.com/qmk/qmk_firmware/pull/18959))
* Reworked backlight keycodes. ([#18961](https://github.com/qmk/qmk_firmware/pull/18961))
* Align audio keycode names ([#18962](https://github.com/qmk/qmk_firmware/pull/18962))
* Align dynamic tapping term keycode names ([#18963](https://github.com/qmk/qmk_firmware/pull/18963))
* Align haptic feedback keycode names ([#18964](https://github.com/qmk/qmk_firmware/pull/18964))
* NVRAM refactor, phase 1. ([#18969](https://github.com/qmk/qmk_firmware/pull/18969))
* Normalise MIDI keycodes ([#18972](https://github.com/qmk/qmk_firmware/pull/18972))
* Normalise output selection (Bluetooth) keycodes ([#19004](https://github.com/qmk/qmk_firmware/pull/19004))
* Move EFL wear-leveling driver to be default for F1, F3, F4, L4, G4, WB32, GD32V. ([#19020](https://github.com/qmk/qmk_firmware/pull/19020))
* Digitizer feature improvements ([#19034](https://github.com/qmk/qmk_firmware/pull/19034))
* Joystick feature improvements ([#19052](https://github.com/qmk/qmk_firmware/pull/19052))
* Add default limit to OLED dirty processing ([#19068](https://github.com/qmk/qmk_firmware/pull/19068))
* Change `RGB_MATRIX_STARTUP_*` defines to `RGB_MATRIX_DEFAULT_*` ([#19079](https://github.com/qmk/qmk_firmware/pull/19079))
* Change `LED_MATRIX_STARTUP_*` defines to `LED_MATRIX_DEFAULT_*` ([#19080](https://github.com/qmk/qmk_firmware/pull/19080))
* Extend eeconfig kb/user datablock API ([#19094](https://github.com/qmk/qmk_firmware/pull/19094))
* Remove .noci functionality ([#19122](https://github.com/qmk/qmk_firmware/pull/19122))

CLI:
* Reject json with duplicate keys ([#18108](https://github.com/qmk/qmk_firmware/pull/18108))
* Add pointing device support to data driven config ([#18215](https://github.com/qmk/qmk_firmware/pull/18215))
* Disconnect `usb.device_ver` ([#18259](https://github.com/qmk/qmk_firmware/pull/18259))
* Normalise info_config.h define generation ([#18439](https://github.com/qmk/qmk_firmware/pull/18439))
* Generate DD RGBLight/LED/RGB Matrix animation defines ([#18459](https://github.com/qmk/qmk_firmware/pull/18459))
* Add converter support to keymap.json ([#18776](https://github.com/qmk/qmk_firmware/pull/18776))
* Ensure consistent clean behaviour ([#18781](https://github.com/qmk/qmk_firmware/pull/18781))
* Format DD mappings and schemas ([#18924](https://github.com/qmk/qmk_firmware/pull/18924))
* Publish hjson files as json ([#18996](https://github.com/qmk/qmk_firmware/pull/18996))
* Add raw output option for QGF/QFF files. ([#18998](https://github.com/qmk/qmk_firmware/pull/18998))
* Improve LED config parsing error messages ([#19007](https://github.com/qmk/qmk_firmware/pull/19007))
* Revert "Add pointing device support to data driven config (#18215)" ([#19063](https://github.com/qmk/qmk_firmware/pull/19063))
* Additional DD backlight config ([#19124](https://github.com/qmk/qmk_firmware/pull/19124))
* Publish constants metadata to API ([#19143](https://github.com/qmk/qmk_firmware/pull/19143))

Submodule updates:
* Use a macro to compute the size of arrays at compile time ([#18044](https://github.com/qmk/qmk_firmware/pull/18044))
* Update pico-sdk to version 1.4.0 ([#18423](https://github.com/qmk/qmk_firmware/pull/18423))

Keyboards:
* Rework PS/2 driver selection ([#17892](https://github.com/qmk/qmk_firmware/pull/17892))
* Durgod K310/K320 Refactor ([#18224](https://github.com/qmk/qmk_firmware/pull/18224))
* Optimise LAYOUT macro generation ([#18262](https://github.com/qmk/qmk_firmware/pull/18262))
* Rename keyboards with uppercase letters ([#18268](https://github.com/qmk/qmk_firmware/pull/18268))
* Remove legacy USE_SERIAL define ([#18292](https://github.com/qmk/qmk_firmware/pull/18292))
* Resolve conflict merging master to develop ([#18297](https://github.com/qmk/qmk_firmware/pull/18297))
* Remove legacy define USE_SERIAL_PD2 ([#18298](https://github.com/qmk/qmk_firmware/pull/18298))
* Remove legacy define SERIAL_USE_MULTI_TRANSACTION ([#18299](https://github.com/qmk/qmk_firmware/pull/18299))
* Adapt spidey3 userspace to recent unicode refactoring ([#18345](https://github.com/qmk/qmk_firmware/pull/18345))
* Remove remaining use of terminal keys and related comment labels ([#18402](https://github.com/qmk/qmk_firmware/pull/18402))
* Add DD mapping for LED/RGB Matrix center ([#18432](https://github.com/qmk/qmk_firmware/pull/18432))
* develop updates for Drashna Keymaps ([#18472](https://github.com/qmk/qmk_firmware/pull/18472))
* Remove lingering `DRIVER_LED_TOTAL` references ([#18475](https://github.com/qmk/qmk_firmware/pull/18475))
* Remove lingering `DRIVER_LED_TOTAL` references ([#18594](https://github.com/qmk/qmk_firmware/pull/18594))
* update andrebrait GMMK Pro keymap ([#18608](https://github.com/qmk/qmk_firmware/pull/18608))
* AnnePro2: Adjust RGB flushing ([#18640](https://github.com/qmk/qmk_firmware/pull/18640))
* Remove lingering `DRIVER_LED_TOTAL` references ([#18662](https://github.com/qmk/qmk_firmware/pull/18662))
* Update snowe's KC_RESET to use QK_BOOT ([#18667](https://github.com/qmk/qmk_firmware/pull/18667))
* Remove some .gitignore files ([#18689](https://github.com/qmk/qmk_firmware/pull/18689))
* Remove keymaps that still reference legacy macros ([#18690](https://github.com/qmk/qmk_firmware/pull/18690))
* Remove keymaps that still reference legacy macros ([#18693](https://github.com/qmk/qmk_firmware/pull/18693))
* Remove stale userspace/keymaps ([#18700](https://github.com/qmk/qmk_firmware/pull/18700))
* Update keyboards readme ([#18714](https://github.com/qmk/qmk_firmware/pull/18714))
* Allow changes to the moonlander default music map ([#18715](https://github.com/qmk/qmk_firmware/pull/18715))
* led_update_kb -> led_update_ports where appropriate ([#18716](https://github.com/qmk/qmk_firmware/pull/18716))
* Update converter/usb_usb user keymaps to use LAYOUT_fullsize ([#18720](https://github.com/qmk/qmk_firmware/pull/18720))
* Remove RGBLIGHT_ANIMATION and clean up effect defines for G-K ([#18726](https://github.com/qmk/qmk_firmware/pull/18726))
* Remove RGBLIGHT_ANIMATION and clean up effect defines for L-Q ([#18727](https://github.com/qmk/qmk_firmware/pull/18727))
* Remove RGBLIGHT_ANIMATION and clean up effect defines for R-Z ([#18728](https://github.com/qmk/qmk_firmware/pull/18728))
* Remove RGBLIGHT_ANIMATION and clean up effect defines for layouts+users ([#18729](https://github.com/qmk/qmk_firmware/pull/18729))
* Update info.json configs to explicitly list RGBLIGHT animations ([#18730](https://github.com/qmk/qmk_firmware/pull/18730))
* A little personal cleanup after #18726 and #18729 ([#18734](https://github.com/qmk/qmk_firmware/pull/18734))
* Move Hillside out of handwired ([#18751](https://github.com/qmk/qmk_firmware/pull/18751))
* wilba_tech: allow keymaps to override backlight_effect_indicators() ([#18791](https://github.com/qmk/qmk_firmware/pull/18791))
* Remove broken userspace and keymaps ([#18806](https://github.com/qmk/qmk_firmware/pull/18806))
* Add support for KBDfans Odin V2 ([#18910](https://github.com/qmk/qmk_firmware/pull/18910))
* Remove more `UNUSED_PINS` defines ([#18940](https://github.com/qmk/qmk_firmware/pull/18940))
* Remove hardcoded VIA keycode range ([#18956](https://github.com/qmk/qmk_firmware/pull/18956))
* KC_GESC -> QK_GESC, better alignment for OCD ([#19018](https://github.com/qmk/qmk_firmware/pull/19018))
* Add missing `manufacturer` fields ([#19065](https://github.com/qmk/qmk_firmware/pull/19065))
* Update use of legacy keycodes ([#19120](https://github.com/qmk/qmk_firmware/pull/19120))

Keyboard fixes:
* [GMMK Pro] Fix unintentional taps to the volume keys when using the encoder ([#17129](https://github.com/qmk/qmk_firmware/pull/17129))
* Luna keyboard pet OLED timeout fix ([#17189](https://github.com/qmk/qmk_firmware/pull/17189))
* Handle escaping of manufacturer/product strings ([#18194](https://github.com/qmk/qmk_firmware/pull/18194))
* kegen/gboy: add manufacturer string ([#18196](https://github.com/qmk/qmk_firmware/pull/18196))
* Ensure all keyboards have a bootloader set ([#18234](https://github.com/qmk/qmk_firmware/pull/18234))
* Reverse keymap search order ([#18449](https://github.com/qmk/qmk_firmware/pull/18449))
* Fixup cradio bootloader/processor ([#18477](https://github.com/qmk/qmk_firmware/pull/18477))
* onekey: enable ADC for Bluepill and Blackpill ([#18545](https://github.com/qmk/qmk_firmware/pull/18545))
* Fixup controllerworks/mini42 ([#18553](https://github.com/qmk/qmk_firmware/pull/18553))
* RESET -> QK_BOOT user keymaps ([#18560](https://github.com/qmk/qmk_firmware/pull/18560))
* Fixup linworks/fave84h ([#18593](https://github.com/qmk/qmk_firmware/pull/18593))
* Fix compilation of 1upkeyboards on develop ([#18618](https://github.com/qmk/qmk_firmware/pull/18618))
* Various keyboard fixes ([#18649](https://github.com/qmk/qmk_firmware/pull/18649))
* Fixup twig50 ([#18651](https://github.com/qmk/qmk_firmware/pull/18651))
* Fixup handwired/jopr — remove deprecated keycode ([#18668](https://github.com/qmk/qmk_firmware/pull/18668))
* Fixup keychron/q3 ([#18687](https://github.com/qmk/qmk_firmware/pull/18687))
* Fixup dumbpad/v3x ([#18692](https://github.com/qmk/qmk_firmware/pull/18692))
* Fix aurora/sweep ([#18701](https://github.com/qmk/qmk_firmware/pull/18701))
* Fix build failures uncovered by #18753 ([#18789](https://github.com/qmk/qmk_firmware/pull/18789))
* Fixup emptystring/nqg ([#18804](https://github.com/qmk/qmk_firmware/pull/18804))
* Fixup controllerwords/mini36 ([#18840](https://github.com/qmk/qmk_firmware/pull/18840))
* Fixup 1upkeyboards/pi60_rgb ([#18858](https://github.com/qmk/qmk_firmware/pull/18858))
* Fixup doio/kb16 ([#18859](https://github.com/qmk/qmk_firmware/pull/18859))
* Fixup keebio/sinc/rev3 ([#18866](https://github.com/qmk/qmk_firmware/pull/18866))
* elephant42: fix default keymap ([#18884](https://github.com/qmk/qmk_firmware/pull/18884))
* Properly fix elephant42 ([#18908](https://github.com/qmk/qmk_firmware/pull/18908))
* Fix syntax error introduced in #18800 ([#18933](https://github.com/qmk/qmk_firmware/pull/18933))
* Resolve info.json/rules.mk feature conflicts in three boards ([#18942](https://github.com/qmk/qmk_firmware/pull/18942))
* Fix DD warnings for RGBKB boards ([#18944](https://github.com/qmk/qmk_firmware/pull/18944))
* Fix "no matrix definition" errors for some boards ([#18954](https://github.com/qmk/qmk_firmware/pull/18954))
* LED config fixes ([#18973](https://github.com/qmk/qmk_firmware/pull/18973))
* `handwired/swiftrax/walter`: fix layout mismatch ([#18974](https://github.com/qmk/qmk_firmware/pull/18974))
* Fix use of shifted custom keycode ([#18978](https://github.com/qmk/qmk_firmware/pull/18978))
* `pizzakeyboards/pizza65`: fix layouts ([#18979](https://github.com/qmk/qmk_firmware/pull/18979))
* `cannonkeys/db60/hotswap`: fix layouts ([#18982](https://github.com/qmk/qmk_firmware/pull/18982))
* `handwired/swiftrax/cowfish`: fix layouts ([#18984](https://github.com/qmk/qmk_firmware/pull/18984))
* Fixup hotdox76v2 on develop ([#18991](https://github.com/qmk/qmk_firmware/pull/18991))
* `mechlovin/adelais/standard_led/avr/rev1`: fix layout ([#18997](https://github.com/qmk/qmk_firmware/pull/18997))
* `gboards/gergoplex`: fix matrix pins ([#18999](https://github.com/qmk/qmk_firmware/pull/18999))
* Fixup keychron/q1/iso_encoder ([#19006](https://github.com/qmk/qmk_firmware/pull/19006))
* Rollback unrelated changes from previous PR. ([#19015](https://github.com/qmk/qmk_firmware/pull/19015))
* Fixup bn006 on develop ([#19029](https://github.com/qmk/qmk_firmware/pull/19029))
* onekey: disable NKRO and mousekeys by default ([#19038](https://github.com/qmk/qmk_firmware/pull/19038))
* Fix up laser_ninja/pumpkin_pad ([#19060](https://github.com/qmk/qmk_firmware/pull/19060))
* Fixup keychron/q6 ([#19066](https://github.com/qmk/qmk_firmware/pull/19066))
* Fixup handwired/alcor_dactyl ([#19072](https://github.com/qmk/qmk_firmware/pull/19072))
* Fix some old keycodes ([#19086](https://github.com/qmk/qmk_firmware/pull/19086))
* Update more `DRIVER_LED_TOTAL` defines to `RGB_MATRIX_LED_COUNT` ([#19089](https://github.com/qmk/qmk_firmware/pull/19089))
* Fix references to `mouse_report_t` (which doesnt exist) ([#19107](https://github.com/qmk/qmk_firmware/pull/19107))
* Fixup keychron/q5 ([#19119](https://github.com/qmk/qmk_firmware/pull/19119))
* Fixup aeboards/satellite ([#19137](https://github.com/qmk/qmk_firmware/pull/19137))
* Fixup aurora/corne on develop ([#19144](https://github.com/qmk/qmk_firmware/pull/19144))
* Minor lint fixes for various info.json ([#19146](https://github.com/qmk/qmk_firmware/pull/19146))

Others:
* Add DD mapping for LED/RGB Matrix max brightness ([#18403](https://github.com/qmk/qmk_firmware/pull/18403))
* Add DD mapping for LED/RGB Matrix split count ([#18408](https://github.com/qmk/qmk_firmware/pull/18408))
* Add DD mapping for LED/RGB Matrix HSVS steps ([#18414](https://github.com/qmk/qmk_firmware/pull/18414))
* Remove RGBLIGHT_ANIMTION and clean up effect defines for 0-F ([#18725](https://github.com/qmk/qmk_firmware/pull/18725))
* Merge API update workflow ([#19121](https://github.com/qmk/qmk_firmware/pull/19121))

Bugs:
* Fix layer switching from tap dances by redoing the keymap lookup ([#17935](https://github.com/qmk/qmk_firmware/pull/17935))
* ws2812: replace RGBLED_NUM with driver-owned constant to decouple driver from RGBLEDs/RGBMATRIX defines ([#18036](https://github.com/qmk/qmk_firmware/pull/18036))
* Prevent USB peripheral fault when restarting USB on WB32 MCUs ([#18058](https://github.com/qmk/qmk_firmware/pull/18058))
* Fix mouse report comparison failing on shared EP (fixes KB preventing sleep) ([#18060](https://github.com/qmk/qmk_firmware/pull/18060))
* Fix incorrect `bluetooth.driver` rules.mk mapping ([#18205](https://github.com/qmk/qmk_firmware/pull/18205))
* Adjust `EXTRAKEY_ENABLE` ifdefs for `send_extra()` ([#18249](https://github.com/qmk/qmk_firmware/pull/18249))
* Fix docs regarding cirque pinnacle attenuation ([#18279](https://github.com/qmk/qmk_firmware/pull/18279))
* Avoid repeated calls to rgblight_set() in tight succession when setting lighting layers ([#18338](https://github.com/qmk/qmk_firmware/pull/18338))
* Fix cirque tap from secondary side of split keyboard ([#18351](https://github.com/qmk/qmk_firmware/pull/18351))
* Fix EECONFIG_KEYMAP_UPPER_BYTE init ([#18394](https://github.com/qmk/qmk_firmware/pull/18394))
* Fix retain brightness when val is changed while a layer is active ([#18426](https://github.com/qmk/qmk_firmware/pull/18426))
* Update Chibios to latest 21.11.2 changes for RP2040 XIP deadlock mitigation ([#18428](https://github.com/qmk/qmk_firmware/pull/18428))
* Fix incorrect g_led_config generation ([#18431](https://github.com/qmk/qmk_firmware/pull/18431))
* Fix Per Key LED Indicator Callbacks ([#18450](https://github.com/qmk/qmk_firmware/pull/18450))
* Update chibios-contrib for RP2040 i2c fixes take 2 ([#18455](https://github.com/qmk/qmk_firmware/pull/18455))
* Fix comment of CM_QUES (Colemak question mark) ([#18557](https://github.com/qmk/qmk_firmware/pull/18557))
* ChibiOS: Fix USB bus disconnect handling ([#18566](https://github.com/qmk/qmk_firmware/pull/18566))
* Update ChibiOS-Contrib for USB IRQ and bus handling fixes ([#18574](https://github.com/qmk/qmk_firmware/pull/18574))
* RP2040: only clear RX FIFO for serial pio driver clear ([#18581](https://github.com/qmk/qmk_firmware/pull/18581))
* Fix ST7565 handler deadlock ([#18609](https://github.com/qmk/qmk_firmware/pull/18609))
* Fix/Update ChibiOS hardware ID ([#18613](https://github.com/qmk/qmk_firmware/pull/18613))
* Fix some rp2040 hardware ID errors ([#18617](https://github.com/qmk/qmk_firmware/pull/18617))
* Fix joystick functionality for ChibiOS and OTG (Blackpill) ([#18631](https://github.com/qmk/qmk_firmware/pull/18631))
* fix typo in solenoid.h ([#18635](https://github.com/qmk/qmk_firmware/pull/18635))
* Fix boundary in `RGB_MATRIX_INDICATOR_SET_COLOR` ([#18650](https://github.com/qmk/qmk_firmware/pull/18650))
* Fix MIDI output endpoint to use the out direction ([#18654](https://github.com/qmk/qmk_firmware/pull/18654))
* Fix mouse use within send_string ([#18659](https://github.com/qmk/qmk_firmware/pull/18659))
* Correctly build keymap.json containing additional config ([#18766](https://github.com/qmk/qmk_firmware/pull/18766))
* Correctly build out of tree keymap.json containing additional config ([#18775](https://github.com/qmk/qmk_firmware/pull/18775))
* Fix garbled test output ([#18822](https://github.com/qmk/qmk_firmware/pull/18822))
* Fix rgb_matrix_set_flags_noeeprom declaration ([#18860](https://github.com/qmk/qmk_firmware/pull/18860))
* Add missing Space Cadet alias ([#18876](https://github.com/qmk/qmk_firmware/pull/18876))
* Fix oled_render to render all dirty blocks. ([#18887](https://github.com/qmk/qmk_firmware/pull/18887))
* compiler.txt: ensure file exists before comparison ([#18921](https://github.com/qmk/qmk_firmware/pull/18921))
* Fix compilation issue with WPM ([#18965](https://github.com/qmk/qmk_firmware/pull/18965))
* Fix keycode parameter extraction to match the new DD keycodes ([#18977](https://github.com/qmk/qmk_firmware/pull/18977))
* Fix jump in mouse_report value when scale changes during cirque get report ([#18992](https://github.com/qmk/qmk_firmware/pull/18992))
* Fixup WS2812 vendor driver ([#19028](https://github.com/qmk/qmk_firmware/pull/19028))
* Add missing prototype for get_hold_on_other_key_press to resolve #18855 ([#19056](https://github.com/qmk/qmk_firmware/pull/19056))
* Fix duplicate key in keyboard.jsonschema ([#19058](https://github.com/qmk/qmk_firmware/pull/19058))
* Fixup `keyboard.jsonschema`. ([#19059](https://github.com/qmk/qmk_firmware/pull/19059))
* fixed MOUSEKEY_INERTIA on AVR ([#19096](https://github.com/qmk/qmk_firmware/pull/19096))
* Fix encoder_init call order in keyboard_init ([#19140](https://github.com/qmk/qmk_firmware/pull/19140))
* Fixup installation procedure for different Fedora versions. ([#19159](https://github.com/qmk/qmk_firmware/pull/19159))
