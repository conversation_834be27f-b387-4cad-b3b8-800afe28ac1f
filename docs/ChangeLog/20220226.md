# QMK Breaking Changes - 2022 February 26 Changelog

## Notable Features {#notable-features}

### Default USB Polling rate now 1kHz ([#15352](https://github.com/qmk/qmk_firmware/pull/15352))

The default USB Polling rate has been aligned across supported platforms to now be 1ms/1kHz.

Something something *Lets go gamers!*

### Split support for pointing devices ([#15304](https://github.com/qmk/qmk_firmware/pull/15304))

Pointing devices can now be shared across a split keyboard with support for a single pointing device or a pointing device on each side.

See the [Pointing Device](../features/pointing_device) documentation for further configuration options.

## Changes Requiring User Action {#changes-requiring-user-action}

### Legacy macro and action_function system removed ([#16025](https://github.com/qmk/qmk_firmware/pull/16025))

The long time deprecated `MACRO()` and `action_get_macro` methods have been removed. Where possible, existing usages have been migrated over to core [Macros](../feature_macros).

### Create a build error if no bootloader is specified ([#16181](https://github.com/qmk/qmk_firmware/pull/16181))

Bootloader configuration is no longer assumed. Keyboards must now set either:

* `BOOTLOADER` within `rules.mk`
* `bootloader` within `info.json`

### Rename `AdafruitBLE` to `BluefruitLE` ([#16127](https://github.com/qmk/qmk_firmware/pull/16127))

In preparation of future bluetooth work, the `AdafruitBLE` integration has been renamed to allow potential for any other Adafruit BLE products. 

### Updated Keyboard Codebases {#updated-keyboard-codebases}

The following keyboards have had their source moved within QMK:

| Old Keyboard Name          | New Keyboard Name                  |
|----------------------------|------------------------------------|
|  6ball                     |  maple_computing/6ball             |
|  7skb                      |  salicylic_acid3/7skb              |
|  7splus                    |  salicylic_acid3/7splus            |
|  acr60                     |  mechkeys/acr60                    |
|  adalyn                    |  tominabox1/adalyn                 |
|  ajisai74                  |  salicylic_acid3/ajisai74          |
|  aleth42                   |  25keys/aleth42                    |
|  alicia_cook               |  ibnuda/alicia_cook                |
|  allison_numpad            |  prototypist/allison_numpad        |
|  allison                   |  prototypist/allison               |
|  alu84                     |  mechkeys/alu84                    |
|  angel17                   |  kakunpc/angel17                   |
|  angel64/alpha             |  kakunpc/angel64/alpha             |
|  angel64/rev1              |  kakunpc/angel64/rev1              |
|  arch_36                   |  obosob/arch_36                    |
|  bakeneko60                |  kkatano/bakeneko60                |
|  bakeneko65/rev2           |  kkatano/bakeneko65/rev2           |
|  bakeneko65/rev3           |  kkatano/bakeneko65/rev3           |
|  bakeneko80                |  kkatano/bakeneko80                |
|  barleycorn                |  yiancardesigns/barleycorn         |
|  bat43/rev1                |  dailycraft/bat43/rev1             |
|  bat43/rev2                |  dailycraft/bat43/rev2             |
|  bigseries/1key            |  woodkeys/bigseries/1key           |
|  bigseries/2key            |  woodkeys/bigseries/2key           |
|  bigseries/3key            |  woodkeys/bigseries/3key           |
|  bigseries/4key            |  woodkeys/bigseries/4key           |
|  bkf                       |  drhigsby/bkf                      |
|  business_card/alpha       |  kakunpc/business_card/alpha       |
|  business_card/beta        |  kakunpc/business_card/beta        |
|  butterstick               |  gboards/butterstick               |
|  c39                       |  maple_computing/c39               |
|  cassette42                |  25keys/cassette42                 |
|  chidori                   |  kagizaraya/chidori                |
|  chili                     |  ydkb/chili                        |
|  chimera_ergo              |  glenpickle/chimera_ergo           |
|  chimera_ls                |  glenpickle/chimera_ls             |
|  chimera_ortho             |  glenpickle/chimera_ortho          |
|  chimera_ortho_plus        |  glenpickle/chimera_ortho_plus     |
|  choco60                   |  recompile_keys/choco60            |
|  choc_taro                 |  kakunpc/choc_taro                 |
|  christmas_tree            |  maple_computing/christmas_tree    |
|  claw44/rev1               |  dailycraft/claw44/rev1            |
|  cocoa40                   |  recompile_keys/cocoa40            |
|  comet46                   |  satt/comet46                      |
|  cu24                      |  capsunlocked/cu24                 |
|  cu75                      |  capsunlocked/cu75                 |
|  cu80                      |  capsunlocked/cu80/v1              |
|  delilah                   |  rainkeebs/delilah                 |
|  diverge3                  |  unikeyboard/diverge3              |
|  divergetm2                |  unikeyboard/divergetm2            |
|  dozen0                    |  yynmt/dozen0                      |
|  dubba175                  |  drhigsby/dubba175                 |
|  eggman                    |  qpockets/eggman                   |
|  ergo42                    |  biacco42/ergo42                   |
|  ergoarrows                |  salicylic_acid3/ergoarrows        |
|  ergodash/mini             |  omkbd/ergodash/mini               |
|  ergodash/rev1             |  omkbd/ergodash/rev1               |
|  ergodox_infinity          |  input_club/ergodox_infinity       |
|  ergotaco                  |  gboards/ergotaco                  |
|  espectro                  |  mechkeys/espectro                 |
|  felix                     |  unikeyboard/felix                 |
|  four_banger               |  bpiphany/four_banger              |
|  freyr                     |  hnahkb/freyr                      |
|  geminate60                |  weirdo/geminate60                 |
|  georgi                    |  gboards/georgi                    |
|  gergo                     |  gboards/gergo                     |
|  getta25                   |  salicylic_acid3/getta25           |
|  gingham                   |  yiancardesigns/gingham            |
|  gurindam                  |  ibnuda/gurindam                   |
|  halberd                   |  kagizaraya/halberd                |
|  hecomi/alpha              |  takashiski/hecomi/alpha           |
|  hid_liber                 |  bpiphany/hid_liber                |
|  id67/default_rgb          |  idobao/id67/default_rgb           |
|  id67/rgb                  |  idobao/id67/rgb                   |
|  id80                      |  idobao/id80/v1                    |
|  id87                      |  idobao/id87/v1                    |
|  idobo                     |  idobao/id75/v1                    |
|  infinity60                |  input_club/infinity60             |
|  ivy/rev1                  |  maple_computing/ivy/rev1          |
|  jisplit89                 |  salicylic_acid3/jisplit89         |
|  jnao                      |  maple_computing/jnao              |
|  just60                    |  ydkb/just60                       |
|  kagamidget                |  yynmt/kagamidget                  |
|  kelowna/rgb64             |  weirdo/kelowna/rgb64              |
|  kprepublic/bm65hsrgb_iso  |  kprepublic/bm65hsrgb_iso/rev1     |
|  kprepublic/bm68hsrgb      |  kprepublic/bm68hsrgb/rev1         |
|  k_type                    |  input_club/k_type                 |
|  latin17rgb                |  latincompass/latin17rgb           |
|  latin47ble                |  latincompass/latin47ble           |
|  latin60rgb                |  latincompass/latin60rgb           |
|  latin64ble                |  latincompass/latin64ble           |
|  latin6rgb                 |  latincompass/latin6rgb            |
|  latinpadble               |  latincompass/latinpadble          |
|  latinpad                  |  latincompass/latinpad             |
|  launchpad/rev1            |  maple_computing/launchpad/rev1    |
|  lck75                     |  lyso1/lck75                       |
|  le_chiffre                |  tominabox1/le_chiffre             |
|  lefishe                   |  lyso1/lefishe                     |
|  lets_split_eh/eh          |  maple_computing/lets_split_eh/eh  |
|  ls_60                     |  weirdo/ls_60                      |
|  m3n3van                   |  matthewdias/m3n3van               |
|  mechmini/v1               |  mechkeys/mechmini/v1              |
|  mechmini/v2               |  mechkeys/mechmini/v2              |
|  meira                     |  woodkeys/meira                    |
|  meishi2                   |  biacco42/meishi2                  |
|  meishi                    |  biacco42/meishi                   |
|  minidox/rev1              |  maple_computing/minidox/rev1      |
|  minim                     |  matthewdias/minim                 |
|  mio                       |  recompile_keys/mio                |
|  model_v                   |  matthewdias/model_v               |
|  montex                    |  idobao/montex/v1                  |
|  nafuda                    |  salicylic_acid3/nafuda            |
|  naiping/np64              |  weirdo/naiping/np64               |
|  naiping/nphhkb            |  weirdo/naiping/nphhkb             |
|  naiping/npminila          |  weirdo/naiping/npminila           |
|  naked48                   |  salicylic_acid3/naked48           |
|  naked60                   |  salicylic_acid3/naked60           |
|  naked64                   |  salicylic_acid3/naked64           |
|  namecard2x4               |  takashiski/namecard2x4            |
|  nebula12                  |  spaceholdings/nebula12            |
|  nebula68b                 |  spaceholdings/nebula68b           |
|  nebula68                  |  spaceholdings/nebula68            |
|  niu_mini                  |  kbdfans/niu_mini                  |
|  nk1                       |  novelkeys/nk1                     |
|  nk65                      |  novelkeys/nk65                    |
|  nk87                      |  novelkeys/nk87                    |
|  nknl7en                   |  salicylic_acid3/nknl7en           |
|  nknl7jp                   |  salicylic_acid3/nknl7jp           |
|  nomu30                    |  recompile_keys/nomu30             |
|  novelpad                  |  novelkeys/novelpad                |
|  ogurec                    |  drhigsby/ogurec                   |
|  otaku_split/rev0          |  takashiski/otaku_split/rev0       |
|  otaku_split/rev1          |  takashiski/otaku_split/rev1       |
|  owl8                      |  dailycraft/owl8                   |
|  packrat                   |  drhigsby/packrat                  |
|  pistachio_mp              |  rate/pistachio_mp                 |
|  pistachio_pro             |  rate/pistachio_pro                |
|  pistachio                 |  rate/pistachio                    |
|  plexus75                  |  checkerboards/plexus75            |
|  pursuit40                 |  checkerboards/pursuit40           |
|  qaz                       |  tominabox1/qaz                    |
|  quark                     |  checkerboards/quark               |
|  rabbit_capture_plan       |  kakunpc/rabbit_capture_plan       |
|  rainkeeb                  |  rainkeebs/rainkeeb                |
|  reviung33                 |  reviung/reviung33                 |
|  reviung34                 |  reviung/reviung34                 |
|  reviung39                 |  reviung/reviung39                 |
|  reviung41                 |  reviung/reviung41                 |
|  reviung53                 |  reviung/reviung53                 |
|  reviung5                  |  reviung/reviung5                  |
|  reviung61                 |  reviung/reviung61                 |
|  runner3680/3x6            |  omkbd/runner3680/3x6              |
|  runner3680/3x7            |  omkbd/runner3680/3x7              |
|  runner3680/3x8            |  omkbd/runner3680/3x8              |
|  runner3680/4x6            |  omkbd/runner3680/4x6              |
|  runner3680/4x7            |  omkbd/runner3680/4x7              |
|  runner3680/4x8            |  omkbd/runner3680/4x8              |
|  runner3680/5x6_5x8        |  omkbd/runner3680/5x6_5x8          |
|  runner3680/5x6            |  omkbd/runner3680/5x6              |
|  runner3680/5x7            |  omkbd/runner3680/5x7              |
|  runner3680/5x8            |  omkbd/runner3680/5x8              |
|  scarletbandana            |  woodkeys/scarletbandana           |
|  scythe                    |  kagizaraya/scythe                 |
|  seigaiha                  |  yiancardesigns/seigaiha           |
|  setta21                   |  salicylic_acid3/setta21           |
|  space_space/rev1          |  qpockets/space_space/rev1         |
|  space_space/rev2          |  qpockets/space_space/rev2         |
|  spiderisland/winry25tc    |  winry/winry25tc                   |
|  splitreus62               |  nacly/splitreus62                 |
|  squiggle/rev1             |  ibnuda/squiggle/rev1              |
|  standaside                |  edi/standaside                    |
|  steal_this_keyboard       |  obosob/steal_this_keyboard        |
|  stella                    |  hnahkb/stella                     |
|  suihankey/alpha           |  kakunpc/suihankey/alpha           |
|  suihankey/rev1            |  kakunpc/suihankey/rev1            |
|  suihankey/split           |  kakunpc/suihankey/split           |
|  thedogkeyboard            |  kakunpc/thedogkeyboard            |
|  the_ruler                 |  maple_computing/the_ruler         |
|  tiger910                  |  weirdo/tiger910                   |
|  treadstone32              |  marksard/treadstone32             |
|  treadstone48/rev1         |  marksard/treadstone48/rev1        |
|  treadstone48/rev2         |  marksard/treadstone48/rev2        |
|  txuu                      |  matthewdias/txuu                  |
|  ua62                      |  nacly/ua62                        |
|  underscore33/rev1         |  tominabox1/underscore33/rev1      |
|  underscore33/rev2         |  tominabox1/underscore33/rev2      |
|  vn66                      |  hnahkb/vn66                       |
|  wallaby                   |  kkatano/wallaby                   |
|  wanten                    |  qpockets/wanten                   |
|  whitefox                  |  input_club/whitefox               |
|  wings42/rev1              |  dailycraft/wings42/rev1           |
|  wings42/rev1_extkeys      |  dailycraft/wings42/rev1_extkeys   |
|  wings42/rev2              |  dailycraft/wings42/rev2           |
|  yasui                     |  rainkeebs/yasui                   |
|  yd60mq                    |  ymdk/yd60mq                       |
|  yd68                      |  ydkb/yd68                         |
|  ymd75                     |  ymdk/ymd75                        |
|  ymd96                     |  ymdk/ymd96                        |
|  ymdk_np21                 |  ymdk/np21                         |
|  yurei                     |  kkatano/yurei                     |
|  zinc                      |  25keys/zinc                       |
|  zinc/rev1                 |  25keys/zinc/rev1                  |
|  zinc/reva                 |  25keys/zinc/reva                  |

## Notable core changes {#notable-core}

### New MCU Support {#new-mcu-support}

Building on previous cycles, QMK firmware picked up support for a couple extra MCU variants:

* STM32L432
* STM32L442

### New Drivers

QMK now has core-supplied support for the following device peripherals:

#### LED

* IS31FL3742A
* IS31FL3743A
* IS31FL3745
* IS31FL3746A

#### GPIO

* SN74x138
* mcp23018

---

## Full changelist

Core:
* Initial pass at data driven new-keyboard subcommand ([#12795](https://github.com/qmk/qmk_firmware/pull/12795))
* Don't send keyboard reports that propagate no changes to the host ([#14065](https://github.com/qmk/qmk_firmware/pull/14065))
* Custom matrix lite support for split keyboards ([#14674](https://github.com/qmk/qmk_firmware/pull/14674))
* Add sym_defer_pr debouncer type ([#14948](https://github.com/qmk/qmk_firmware/pull/14948))
* Add RGB matrix & LED Matrix support for IS31FL3742A, IS31FL3743A, IS31FL3745, IS31FL3746A ([#14989](https://github.com/qmk/qmk_firmware/pull/14989))
* New combo configuration options ([#15083](https://github.com/qmk/qmk_firmware/pull/15083))
* IS31FL3733 driver for LED Matrix ([#15088](https://github.com/qmk/qmk_firmware/pull/15088))
* Add open-drain GPIO support. ([#15282](https://github.com/qmk/qmk_firmware/pull/15282))
* Make (un)register code functions weak ([#15285](https://github.com/qmk/qmk_firmware/pull/15285))
* Split support for pointing devices. ([#15304](https://github.com/qmk/qmk_firmware/pull/15304))
* Added cancel_key_lock function ([#15321](https://github.com/qmk/qmk_firmware/pull/15321))
* Remove matrix_is_modified() and debounce_is_active() ([#15349](https://github.com/qmk/qmk_firmware/pull/15349))
* Change default USB Polling rate to 1kHz ([#15352](https://github.com/qmk/qmk_firmware/pull/15352))
* Implement MAGIC_TOGGLE_CONTROL_CAPSLOCK ([#15368](https://github.com/qmk/qmk_firmware/pull/15368))
* Tidy up existing i2c_master implementations ([#15376](https://github.com/qmk/qmk_firmware/pull/15376))
* Generalize Unicode defines ([#15409](https://github.com/qmk/qmk_firmware/pull/15409))
* Added external spi flash driver. ([#15419](https://github.com/qmk/qmk_firmware/pull/15419))
* Remove Deprecated USB Polling comment from vusb.c ([#15420](https://github.com/qmk/qmk_firmware/pull/15420))
* Expand rotational range for PMW3360 Optical Sensor ([#15431](https://github.com/qmk/qmk_firmware/pull/15431))
* ChibiOS SVN mirror script update ([#15435](https://github.com/qmk/qmk_firmware/pull/15435))
* Refactor `bootloader_jump()` implementations ([#15450](https://github.com/qmk/qmk_firmware/pull/15450))
* added missing audio_off_user() callback ([#15457](https://github.com/qmk/qmk_firmware/pull/15457))
* Migrate serial_uart usages to UART driver ([#15479](https://github.com/qmk/qmk_firmware/pull/15479))
* Migrate RN42 to UART driver and refactor ([#15492](https://github.com/qmk/qmk_firmware/pull/15492))
* pwm3360 driver cleanup and diff reduction to adns9800 ([#15559](https://github.com/qmk/qmk_firmware/pull/15559))
* Advanced deferred_exec for core-side code. ([#15579](https://github.com/qmk/qmk_firmware/pull/15579))
* Adjust tap_code16 to account for TAP_HOLD_CAPS_DELAY ([#15635](https://github.com/qmk/qmk_firmware/pull/15635))
* Slight tidy up of keyboard task loop ([#15725](https://github.com/qmk/qmk_firmware/pull/15725))
* Unify the key up/down behaviour of RGB keycodes ([#15730](https://github.com/qmk/qmk_firmware/pull/15730))
* Add PMW3389 optical sensor Support (Updated) ([#15740](https://github.com/qmk/qmk_firmware/pull/15740))
* ChibiOS: add support for HID Programmable Buttons ([#15787](https://github.com/qmk/qmk_firmware/pull/15787))
* ChibiOS: shorten USB disconnect state on boot to 50ms ([#15805](https://github.com/qmk/qmk_firmware/pull/15805))
* Add init function to clear previous matrix effect ([#15815](https://github.com/qmk/qmk_firmware/pull/15815))
* Optimize initialization of PMW3360 Sensor ([#15821](https://github.com/qmk/qmk_firmware/pull/15821))
* Add Pixel Flow RGB matrix effect ([#15829](https://github.com/qmk/qmk_firmware/pull/15829))
* PMW3389 Revert Firmware load during Initilization ([#15859](https://github.com/qmk/qmk_firmware/pull/15859))
* Combo `TAP_CODE_DELAY` and `clear_weak_mods` ([#15866](https://github.com/qmk/qmk_firmware/pull/15866))
* Relocate matrix_scan_quantum tasks ([#15882](https://github.com/qmk/qmk_firmware/pull/15882))
* Adjust mouse key defaults ([#15883](https://github.com/qmk/qmk_firmware/pull/15883))
* RGB Matrix: Reload from EEPROM ([#15923](https://github.com/qmk/qmk_firmware/pull/15923))
* Enable a default task throttle for split pointing. ([#15925](https://github.com/qmk/qmk_firmware/pull/15925))
* Move mcp23018 driver to core ([#15944](https://github.com/qmk/qmk_firmware/pull/15944))
* Relocate matrix_init_quantum content ([#15953](https://github.com/qmk/qmk_firmware/pull/15953))
* Align location of some host led logic ([#15954](https://github.com/qmk/qmk_firmware/pull/15954))
* Rename some Quantum keycodes ([#15968](https://github.com/qmk/qmk_firmware/pull/15968))
* Migrate more makefile utilities to builddefs sub-directory ([#16002](https://github.com/qmk/qmk_firmware/pull/16002))
* Various Makefile optimisations ([#16015](https://github.com/qmk/qmk_firmware/pull/16015))
* Add support for STM32L432, STM32L442. ([#16016](https://github.com/qmk/qmk_firmware/pull/16016))
* EEPROM refactor: remove `eeprom_teensy.c` by default, use transient instead ([#16020](https://github.com/qmk/qmk_firmware/pull/16020))
* Deprecate Split Transaction status field ([#16023](https://github.com/qmk/qmk_firmware/pull/16023))
* Rip out old macro and action_function system ([#16025](https://github.com/qmk/qmk_firmware/pull/16025))
* Add a script that simplifies running commands under docker. ([#16028](https://github.com/qmk/qmk_firmware/pull/16028))
* Add support for Q-series on the ckled2001 LED driver ([#16051](https://github.com/qmk/qmk_firmware/pull/16051))
* Remove unused suspend_idle ([#16063](https://github.com/qmk/qmk_firmware/pull/16063))
* Initial migration of suspend callbacks ([#16067](https://github.com/qmk/qmk_firmware/pull/16067))
* Add layout change callbacks to VIA ([#16087](https://github.com/qmk/qmk_firmware/pull/16087))
* Rename `AdafruitBLE` to `BluefruitLE` ([#16127](https://github.com/qmk/qmk_firmware/pull/16127))
* Update outputselect to use platform connected state API ([#16185](https://github.com/qmk/qmk_firmware/pull/16185))
* Remove default pointing device driver. ([#16190](https://github.com/qmk/qmk_firmware/pull/16190))
* Add SN74x138 demultiplexer driver ([#16217](https://github.com/qmk/qmk_firmware/pull/16217))
* Standardise error output. ([#16220](https://github.com/qmk/qmk_firmware/pull/16220))
* Followup to #16220, more test error output. ([#16221](https://github.com/qmk/qmk_firmware/pull/16221))
* Misc size regression script improvements. ([#16268](https://github.com/qmk/qmk_firmware/pull/16268))
* Align existing pca9555 driver to better match mcp23018 API ([#16277](https://github.com/qmk/qmk_firmware/pull/16277))
* Size checks print out target firmware file instead ([#16290](https://github.com/qmk/qmk_firmware/pull/16290))

CLI:
* `develop` changelog generator: use the PR title instead ([#15537](https://github.com/qmk/qmk_firmware/pull/15537))
* `develop` changelog generator: skip code formatting in listing ([#16215](https://github.com/qmk/qmk_firmware/pull/16215))

Keyboards:
* Durgod: Increase scan rate by using wait_us timer ([#14091](https://github.com/qmk/qmk_firmware/pull/14091))
* Add another GMMK Pro ANSI Keymap with custom RGB. ([#14243](https://github.com/qmk/qmk_firmware/pull/14243))
* Parse USB device version BCD ([#14580](https://github.com/qmk/qmk_firmware/pull/14580))
* Add vitoni keymap for GMMK Pro (ISO) ([#15006](https://github.com/qmk/qmk_firmware/pull/15006))
* Move bm65hsrgb_iso and bm68hsrgb to rev1/ to prepare for updates to the boards ([#15132](https://github.com/qmk/qmk_firmware/pull/15132))
* Convert ergoinu to SPLIT_KEYBOARD ([#15305](https://github.com/qmk/qmk_firmware/pull/15305))
* Convert not_so_minidox to SPLIT_KEYBOARD ([#15306](https://github.com/qmk/qmk_firmware/pull/15306))
* Added new handwired keyboard Wakizashi 40 ([#15336](https://github.com/qmk/qmk_firmware/pull/15336))
* Convert ai03/orbit to SPLIT_KEYBOARD ([#15340](https://github.com/qmk/qmk_firmware/pull/15340))
* Remove manual enable of LTO within user keymaps ([#15378](https://github.com/qmk/qmk_firmware/pull/15378))
* Move to organization folder ([#15481](https://github.com/qmk/qmk_firmware/pull/15481))
* Convert some more boards to Matrix Lite ([#15489](https://github.com/qmk/qmk_firmware/pull/15489))
* Organize Reviung boards into a directory ([#15636](https://github.com/qmk/qmk_firmware/pull/15636))
* move winry25tc to winry/ ([#15637](https://github.com/qmk/qmk_firmware/pull/15637))
* Rename ymdk_np21 to np21 + move to ymdk vendor folder ([#15641](https://github.com/qmk/qmk_firmware/pull/15641))
* move ymd96 to ymdk vendor folder ([#15643](https://github.com/qmk/qmk_firmware/pull/15643))
* move ymd75 to ymdk vendor folder ([#15645](https://github.com/qmk/qmk_firmware/pull/15645))
* move yd60mq to ymdk vendor folder ([#15647](https://github.com/qmk/qmk_firmware/pull/15647))
* rename idobo to idobao/id75, move to vendor folder ([#15661](https://github.com/qmk/qmk_firmware/pull/15661))
* move ID67 to IDOBAO vendor folder ([#15662](https://github.com/qmk/qmk_firmware/pull/15662))
* move ID80 to IDOBAO vendor folder ([#15665](https://github.com/qmk/qmk_firmware/pull/15665))
* move ID87 to IDOBAO vendor folder ([#15667](https://github.com/qmk/qmk_firmware/pull/15667))
* move montex to IDOBAO vendor folder ([#15668](https://github.com/qmk/qmk_firmware/pull/15668))
* move @yangdigi 's keyboards to a YDKB folder ([#15681](https://github.com/qmk/qmk_firmware/pull/15681))
* move @kkatano 's keyboards to kkatano user folder ([#15684](https://github.com/qmk/qmk_firmware/pull/15684))
* Sol 3 Keyboard from RGBKB ([#15687](https://github.com/qmk/qmk_firmware/pull/15687))
* move cu24, cu75, cu80/v1 into capsunlocked folder ([#15758](https://github.com/qmk/qmk_firmware/pull/15758))
* move mechkeys keyboards into the mechkeys/ vendor folder ([#15760](https://github.com/qmk/qmk_firmware/pull/15760))
* move @lyso1 's boards into lyso1/ ([#15767](https://github.com/qmk/qmk_firmware/pull/15767))
* move prototypist boards into vendor folder ([#15780](https://github.com/qmk/qmk_firmware/pull/15780))
* move @yiancar 's boards into yiancardesigns/ ([#15781](https://github.com/qmk/qmk_firmware/pull/15781))
* move novelkeys keyboards to vendor folder ([#15783](https://github.com/qmk/qmk_firmware/pull/15783))
* move @weirdo-f 's keyboards into weirdo/ ([#15785](https://github.com/qmk/qmk_firmware/pull/15785))
* move @marksard 's boards to marksard/ ([#15786](https://github.com/qmk/qmk_firmware/pull/15786))
* move input club keyboards into vendor folder ([#15788](https://github.com/qmk/qmk_firmware/pull/15788))
* move @monksoffunk 's boards into 25keys/ ([#15789](https://github.com/qmk/qmk_firmware/pull/15789))
* move @Salicylic-acid3 's keyboards to salicylic-acid3/ ([#15791](https://github.com/qmk/qmk_firmware/pull/15791))
* move @rainkeebs 's keyboards to rainkeebs/ ([#15797](https://github.com/qmk/qmk_firmware/pull/15797))
* move standaside into edi/ ([#15798](https://github.com/qmk/qmk_firmware/pull/15798))
* move @obosob 's boards into obosob/ ([#15799](https://github.com/qmk/qmk_firmware/pull/15799))
* move @nacly 's boards to nacly/ ([#15801](https://github.com/qmk/qmk_firmware/pull/15801))
* move @kakunpc 's keebs into kakunpc/ ([#15814](https://github.com/qmk/qmk_firmware/pull/15814))
* move @qpocket 's keyboards to qpocket/ ([#15827](https://github.com/qmk/qmk_firmware/pull/15827))
* BDN9 keymap ([#15924](https://github.com/qmk/qmk_firmware/pull/15924))
* move @matthewdias 's keebs into matthewdias/ ([#15991](https://github.com/qmk/qmk_firmware/pull/15991))
* move id80 and id75 to v1 to accommodate for id75 v2 and id80 v3 ([#15992](https://github.com/qmk/qmk_firmware/pull/15992))
* Remove `action_function()` from LFKeyboards boards ([#15993](https://github.com/qmk/qmk_firmware/pull/15993))
* move @latincompass (aka @18438880 , @haierwangwei2005)'s boards to /latincompass ([#16039](https://github.com/qmk/qmk_firmware/pull/16039))
* move g heavy industry boards into /gboards ([#16040](https://github.com/qmk/qmk_firmware/pull/16040))
* move @drhigsby 's boards into /drhigsby ([#16041](https://github.com/qmk/qmk_firmware/pull/16041))
* More keyboard rules.mk cleanups ([#16044](https://github.com/qmk/qmk_firmware/pull/16044))
* move @That-Canadian 's boards into /maple_computing ([#16050](https://github.com/qmk/qmk_firmware/pull/16050))
* move @takai 's keyboards into /recompile_keys ([#16053](https://github.com/qmk/qmk_firmware/pull/16053))
* move @satt99 's comet46 to satt/ ([#16059](https://github.com/qmk/qmk_firmware/pull/16059))
* move @ka2hiro 's boards into /kagizaraya ([#16070](https://github.com/qmk/qmk_firmware/pull/16070))
* move @GlenPickle 's chimera* boards into a folder ([#16072](https://github.com/qmk/qmk_firmware/pull/16072))
* move @yynmt 's boards into /yynmt ([#16075](https://github.com/qmk/qmk_firmware/pull/16075))
* move @Biacco42 's keebs into /biacco42 ([#16080](https://github.com/qmk/qmk_firmware/pull/16080))
* move unikeyboard boards to /unikeyboard ([#16081](https://github.com/qmk/qmk_firmware/pull/16081))
* move four_banger to bpiphany ([#16082](https://github.com/qmk/qmk_firmware/pull/16082))
* move @takashiski 's keebs into /takashiski ([#16089](https://github.com/qmk/qmk_firmware/pull/16089))
* move hid_liber to /bpiphany ([#16091](https://github.com/qmk/qmk_firmware/pull/16091))
* move spaceholdings boards into /spaceholdings ([#16096](https://github.com/qmk/qmk_firmware/pull/16096))
* move @7-rate 's keebs to /rate ([#16099](https://github.com/qmk/qmk_firmware/pull/16099))
* move @npspears 's boards into /checkerboards ([#16100](https://github.com/qmk/qmk_firmware/pull/16100))
* move @vuhopkep 's keebs into /hnahkb ([#16102](https://github.com/qmk/qmk_firmware/pull/16102))
* move @ibnuda 's keebs into /ibnuda ([#16108](https://github.com/qmk/qmk_firmware/pull/16108))
* move @tominabox1 's keebs into /tominabox1 ([#16109](https://github.com/qmk/qmk_firmware/pull/16109))
* move niu_mini to /kbdfans ([#16112](https://github.com/qmk/qmk_firmware/pull/16112))
* move woodkeys.click keyboards to /woodkeys ([#16113](https://github.com/qmk/qmk_firmware/pull/16113))
* move @omkbd 's boards to /omkbd ([#16116](https://github.com/qmk/qmk_firmware/pull/16116))
* Overhaul Tractyl Manuform ([#16134](https://github.com/qmk/qmk_firmware/pull/16134))
* Reduce firmware size for dztech/dz60rgb_wkl/v2_1:via ([#16254](https://github.com/qmk/qmk_firmware/pull/16254))

Keyboard fixes:
* Fix build failure for UT47 ([#15483](https://github.com/qmk/qmk_firmware/pull/15483))
* Update grs_70ec to use newer custom matrix ([#15609](https://github.com/qmk/qmk_firmware/pull/15609))
* fix compiler issue with Tractyl Manuform 4x6 ([#15646](https://github.com/qmk/qmk_firmware/pull/15646))
* Fix CI. ([#15828](https://github.com/qmk/qmk_firmware/pull/15828))
* Yet another bad `DEFAULT_FOLDER` fix. ([#15904](https://github.com/qmk/qmk_firmware/pull/15904))
* Fix build failures for `mschwingen/modelm` ([#15987](https://github.com/qmk/qmk_firmware/pull/15987))
* `rocketboard_16`: Fix mismatched LUT sizes ([#15997](https://github.com/qmk/qmk_firmware/pull/15997))
* Fix erroneous SRC for Clueboard 66 hotswap ([#16007](https://github.com/qmk/qmk_firmware/pull/16007))
* Fix handwired/ms_sculpt_mobile default keymap ([#16032](https://github.com/qmk/qmk_firmware/pull/16032))
* Re-org Hillside folders as new model prep. Fix default keymap. ([#16128](https://github.com/qmk/qmk_firmware/pull/16128))
* Fix up default folder locations. Again. ([#16135](https://github.com/qmk/qmk_firmware/pull/16135))
* Sol3 rgb fix ([#16157](https://github.com/qmk/qmk_firmware/pull/16157))
* Add missing `BOOTLOADER` for a handful of boards ([#16225](https://github.com/qmk/qmk_firmware/pull/16225))
* Remove half implemented micronucleus bootloader support ([#16252](https://github.com/qmk/qmk_firmware/pull/16252))
* Fixup bootloaders. ([#16256](https://github.com/qmk/qmk_firmware/pull/16256))
* Fix idobao/id80/v3 compilation errors ([#16280](https://github.com/qmk/qmk_firmware/pull/16280))
* Remove parent-relative paths from keyboards. ([#16282](https://github.com/qmk/qmk_firmware/pull/16282))
* Bodge for helix build failures ([#16376](https://github.com/qmk/qmk_firmware/pull/16376))

Others:
* Add a clarification to an error message ([#15207](https://github.com/qmk/qmk_firmware/pull/15207))
* Clang-format tweaks ([#15906](https://github.com/qmk/qmk_firmware/pull/15906))
* Add example implementations for compatible MCUs list ([#15935](https://github.com/qmk/qmk_firmware/pull/15935))
* Add version.h to gitignore ([#16222](https://github.com/qmk/qmk_firmware/pull/16222))
* Update keyboard mapping for all moved boards this cycle ([#16312](https://github.com/qmk/qmk_firmware/pull/16312))
* Align docs to new-keyboard behaviour ([#16357](https://github.com/qmk/qmk_firmware/pull/16357))
* Align new-keyboard with recent schema updates ([#16378](https://github.com/qmk/qmk_firmware/pull/16378))

Bugs:
* Fixes potential wpm sampling overflow, along with code comment fixes ([#15277](https://github.com/qmk/qmk_firmware/pull/15277))
* Add missing define for unicode common ([#15416](https://github.com/qmk/qmk_firmware/pull/15416))
* Fix for SPI write timing in PMW3360 driver ([#15519](https://github.com/qmk/qmk_firmware/pull/15519))
* Documentation Typo fix ([#15538](https://github.com/qmk/qmk_firmware/pull/15538))
* fix a typo ([#15557](https://github.com/qmk/qmk_firmware/pull/15557))
* Fix avr serial compile ([#15589](https://github.com/qmk/qmk_firmware/pull/15589))
* More AVR GPIO compilation fixes. ([#15592](https://github.com/qmk/qmk_firmware/pull/15592))
* Fix bug and code regression for Split Common ([#15603](https://github.com/qmk/qmk_firmware/pull/15603))
* Include missing string.h include in split ([#15606](https://github.com/qmk/qmk_firmware/pull/15606))
* Fixes for bootloader refactor build failures ([#15638](https://github.com/qmk/qmk_firmware/pull/15638))
* Update pmw3360 driver after reading the datasheet top to bottom. Fix some outdated refs. ([#15682](https://github.com/qmk/qmk_firmware/pull/15682))
* Fix split pointing for analog joystick ([#15691](https://github.com/qmk/qmk_firmware/pull/15691))
* Fix broken bootloader builds in develop. ([#15880](https://github.com/qmk/qmk_firmware/pull/15880))
* Fix optical sensor firmware upload ([#15919](https://github.com/qmk/qmk_firmware/pull/15919))
* Pass in the keyrecord_t of the dual-role/tapping key when calling per-key tap hold functions ([#15938](https://github.com/qmk/qmk_firmware/pull/15938))
* fixed typo in orange HSV colors decalartion ([#15976](https://github.com/qmk/qmk_firmware/pull/15976))
* Fix hack for chibiOS reset name ([#15984](https://github.com/qmk/qmk_firmware/pull/15984))
* Fix right side ws2812 leds having two indices ([#15985](https://github.com/qmk/qmk_firmware/pull/15985))
* Workaround in Makefile for recursive rule matching ([#15988](https://github.com/qmk/qmk_firmware/pull/15988))
* Fix BACKLIGHT_CAPS_LOCK warning ([#15999](https://github.com/qmk/qmk_firmware/pull/15999))
* Fix compilation issues for led indicators ([#16001](https://github.com/qmk/qmk_firmware/pull/16001))
* ChibiOS timer fixes ([#16017](https://github.com/qmk/qmk_firmware/pull/16017))
* Fix bootloader_jump for certain CTRL boards ([#16026](https://github.com/qmk/qmk_firmware/pull/16026))
* Fix up issue with PROGMEM and hand_swap_config ([#16027](https://github.com/qmk/qmk_firmware/pull/16027))
* Don't make EEPROM size assumptions with dynamic keymaps. ([#16054](https://github.com/qmk/qmk_firmware/pull/16054))
* fix missed .noci in reviung move ([#16107](https://github.com/qmk/qmk_firmware/pull/16107))
* Fix issues with Python Tests ([#16162](https://github.com/qmk/qmk_firmware/pull/16162))
* Fixup multibuild filegen ([#16166](https://github.com/qmk/qmk_firmware/pull/16166))
* Remove old .gitignore entry. Add more macOS junk exclusions. ([#16167](https://github.com/qmk/qmk_firmware/pull/16167))
* Fixup builds so that teensy EEPROM knows which MCU it's targeting. ([#16168](https://github.com/qmk/qmk_firmware/pull/16168))
* Create a build error if no bootloader is specified. ([#16181](https://github.com/qmk/qmk_firmware/pull/16181))
* Ensure `version.h` is recreated each build. ([#16188](https://github.com/qmk/qmk_firmware/pull/16188))
* Add `custom` to list of valid bootloader types in info.json ([#16228](https://github.com/qmk/qmk_firmware/pull/16228))
* Fix `layer_state` restoration at end of dynamic macro feature #16208 ([#16230](https://github.com/qmk/qmk_firmware/pull/16230))
* Minor additions #12795 ([#16276](https://github.com/qmk/qmk_firmware/pull/16276))
* Various fixes for matrix _RIGHT handling ([#16292](https://github.com/qmk/qmk_firmware/pull/16292))
* Fix slashes in build_full_test.mk ([#16300](https://github.com/qmk/qmk_firmware/pull/16300))
* ps2/avr: use the correct file name ([#16316](https://github.com/qmk/qmk_firmware/pull/16316))
* Fix compilation of ChibiOS UART driver ([#16348](https://github.com/qmk/qmk_firmware/pull/16348))
* Various fixes for new-keyboard ([#16358](https://github.com/qmk/qmk_firmware/pull/16358))
* Allow NO_PIN within data driven configuration ([#16359](https://github.com/qmk/qmk_firmware/pull/16359))
