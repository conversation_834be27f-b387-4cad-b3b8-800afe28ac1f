# QMK Breaking Changes - 2025 February 23 Changelog

## Notable Features

### Community Modules ([#24848](https://github.com/qmk/qmk_firmware/pull/24848))

Community Modules are a feature within QMK which allows code to be implemented by third parties, making it available for other people to import into their own builds.

These modules can provide implementations which override or enhance normal QMK processing; initialization, key processing, suspend, and shutdown are some of the provided hooks which modules may currently implement.

See the [Community Modules documentation](../features/community_modules) for more information, including the full list of available hooks.

First-class support for [External Userspace](../newbs_external_userspace) is included out of the box, so there's even more reason to take the plunge and convert your keymap builds to a userspace repository!

::: tip
An example with a new keycode and some debugging information in the QMK repository [lives here](https://github.com/qmk/qmk_firmware/tree/master/modules/qmk/hello_world), and a community module port of [get<PERSON><PERSON>'s SOCD Cleaner](https://getreuer.info/posts/keyboards/socd-cleaner/) can be found in [tzar<PERSON>'s modules repo](https://github.com/tzarc/qmk_modules).
:::

### Chordal Hold ([#24560](https://github.com/qmk/qmk_firmware/pull/24560))

Chordal Hold implements, by default, an "opposite hands" rule. Suppose a tap-hold key is pressed and then, before the tapping term, another key is pressed. With Chordal Hold, the tap-hold key is settled as tapped if the two keys are on the same hand.

Chordal Hold may be useful to avoid accidental modifier activation with mod-taps, particularly in rolled keypresses when using home row mods.

See the [Chordal Hold documentation](../tap_hold#chordal-hold) for more information.

## Changes Requiring User Action

### Updated Keyboard Codebases

| Old Keyboard Name       | New Keyboard Name           |
|-------------------------|-----------------------------|
| cxt_studio              | cxt_studio/12e4             |
| ergodox_stm32           | handwired/ergodox_stm32     |
| ploopyco/mouse          | ploopyco/mouse/rev1_002     |
| ploopyco/trackball/rev1 | ploopyco/trackball/rev1_004 |
| ymdk/id75               | ymdk/id75/f103              |

## Deprecation Notices

In line with the [notice period](../support_deprecation_policy#how-much-advance-notice-will-be-given), deprecation notices for larger items are listed here.

### DEFAULT_FOLDER removal ([#24836](https://github.com/qmk/qmk_firmware/pull/24836))

`DEFAULT_FOLDER` was originally introduced to work around limitations within the build system.
Parent folders containing common configuration would create invalid build targets.

With the introduction of [`keyboard.json`](./20240526#keyboard-json) as a configuration file, the build system now has a consistent method to detect build targets.
The `DEFAULT_FOLDER` functionality is now redundant and the intent is for `rules.mk` to become pure configuration.

Backwards compatibility of build targets has been maintained where possible.

## Full changelist

Core:
* Chaining OSL and MO ([#23065](https://github.com/qmk/qmk_firmware/pull/23065))
* Add extra keymap for EurKEY layout ([#24241](https://github.com/qmk/qmk_firmware/pull/24241))
* Add leader_add_user callback ([#24266](https://github.com/qmk/qmk_firmware/pull/24266))
* always return audio pin to 0 on ARM ([#24503](https://github.com/qmk/qmk_firmware/pull/24503))
* Update Starlight matrix effects ([#24521](https://github.com/qmk/qmk_firmware/pull/24521))
* Refactor Hue Breathing matrix effect with runner ([#24525](https://github.com/qmk/qmk_firmware/pull/24525))
* Ensure timer_read() is safe to call from interrupt handlers on ARM ([#24529](https://github.com/qmk/qmk_firmware/pull/24529))
* Update Raindrops effect to respect LED range limits ([#24531](https://github.com/qmk/qmk_firmware/pull/24531))
* Add Chordal Hold, an "opposite hands rule" tap-hold option similar to Achordion, Bilateral Combinations. ([#24560](https://github.com/qmk/qmk_firmware/pull/24560))
* Azoteq - improve I2C behaviour while polling. ([#24611](https://github.com/qmk/qmk_firmware/pull/24611))
* macOS install: remove bad ARM toolchains ([#24637](https://github.com/qmk/qmk_firmware/pull/24637))
* small refactoring of TIMER_DIFF ([#24678](https://github.com/qmk/qmk_firmware/pull/24678))
* Subscript alef correction ([#24707](https://github.com/qmk/qmk_firmware/pull/24707))
* Created SH1107 driver for quantum painter ([#24724](https://github.com/qmk/qmk_firmware/pull/24724))
* [CI] Regenerate Files ([#24772](https://github.com/qmk/qmk_firmware/pull/24772))
* Patch up issue for inverted complementary output on Backlight ([#24794](https://github.com/qmk/qmk_firmware/pull/24794))
* Patch up issue when compile with APA102 driver ([#24800](https://github.com/qmk/qmk_firmware/pull/24800))
* Consolidate send_string implementations. ([#24817](https://github.com/qmk/qmk_firmware/pull/24817))
* Consolidate timer_elapsed implementations ([#24830](https://github.com/qmk/qmk_firmware/pull/24830))
* `i2c_master`: remove deprecated functions ([#24832](https://github.com/qmk/qmk_firmware/pull/24832))
* Resolve keyboard_aliases when processing keyboard make targets ([#24834](https://github.com/qmk/qmk_firmware/pull/24834))
* LED drivers: remove deprecated defines ([#24837](https://github.com/qmk/qmk_firmware/pull/24837))
* `ferris/0_1`: update I2C API usage ([#24839](https://github.com/qmk/qmk_firmware/pull/24839))
* Unify i2c_master headers ([#24846](https://github.com/qmk/qmk_firmware/pull/24846))
* Community modules ([#24848](https://github.com/qmk/qmk_firmware/pull/24848))
* Relocate base WS2812 code ([#24850](https://github.com/qmk/qmk_firmware/pull/24850))
* Unify UART headers ([#24855](https://github.com/qmk/qmk_firmware/pull/24855))
* Unify spi_master headers ([#24857](https://github.com/qmk/qmk_firmware/pull/24857))
* Invoke `process_record_via` after `_user`/`_kb` have a chance to handle it. ([#24879](https://github.com/qmk/qmk_firmware/pull/24879))

CLI:
* Extend lint to reject 'blank' files ([#23994](https://github.com/qmk/qmk_firmware/pull/23994))
* `qmk docs`: restore `--port` and `--browser` arguments ([#24623](https://github.com/qmk/qmk_firmware/pull/24623))
* Update via2json layout macro searching ([#24640](https://github.com/qmk/qmk_firmware/pull/24640))
* Change `new-keymap` keymap name prompt ([#24701](https://github.com/qmk/qmk_firmware/pull/24701))
* default_keyboard.h generation tweaks ([#24715](https://github.com/qmk/qmk_firmware/pull/24715))
* Ensure `qmk flash` rejects invalid files for uf2 compatible bootloaders ([#24802](https://github.com/qmk/qmk_firmware/pull/24802))
* Reject readme dummy content ([#24913](https://github.com/qmk/qmk_firmware/pull/24913))

Submodule updates:
* chibios: usb_main: remove OTG sof workaround ([#24259](https://github.com/qmk/qmk_firmware/pull/24259))
* Update ChibiOS to latest stable branch. ([#24651](https://github.com/qmk/qmk_firmware/pull/24651))
* Update ChibiOS `stable_21.11.x`. ([#24714](https://github.com/qmk/qmk_firmware/pull/24714))
* Update ChibiOS-Contrib. ([#24803](https://github.com/qmk/qmk_firmware/pull/24803))

Keyboards:
* refactor: move ymdk/id75 to revision ([#24590](https://github.com/qmk/qmk_firmware/pull/24590))
* skyloong/gk61: Remove overriding of core keycode behaviour ([#24655](https://github.com/qmk/qmk_firmware/pull/24655))
* moky/moky88: Remove use of deprecated defines ([#24656](https://github.com/qmk/qmk_firmware/pull/24656))
* Updating Promenade keyboard (Missing keys in matrix, other minor changes to keymap) ([#24705](https://github.com/qmk/qmk_firmware/pull/24705))
* Moving cxt_studio keyboard to own folder ([#24748](https://github.com/qmk/qmk_firmware/pull/24748))
* Add CXT Studio 12E3 keyboard ([#24749](https://github.com/qmk/qmk_firmware/pull/24749))
* Add Silakka54 keyboard ([#24757](https://github.com/qmk/qmk_firmware/pull/24757))
* Add more layout for skiller_sgk50_s4 ([#24784](https://github.com/qmk/qmk_firmware/pull/24784))
* Add watchdog service to RGBKB Sol 3 ([#24786](https://github.com/qmk/qmk_firmware/pull/24786))
* Migrate some DEFAULT_FOLDER to keyboard_aliases.hjson ([#24835](https://github.com/qmk/qmk_firmware/pull/24835))
* Remove DEFAULT_FOLDER where keyboard aliases already exists ([#24838](https://github.com/qmk/qmk_firmware/pull/24838))
* Migrate some DEFAULT_FOLDER to keyboard_aliases.hjson ([#24845](https://github.com/qmk/qmk_firmware/pull/24845))
* Update for 'A-JAZZ AKC084' ('A-JAZZ AKP846') ([#24868](https://github.com/qmk/qmk_firmware/pull/24868))
* handwired/xealous - Remove DEFAULT_FOLDER ([#24877](https://github.com/qmk/qmk_firmware/pull/24877))
* Updates to Ploopy Classic, Mouse, and Thumb for RP2040 hardware upgrade ([#24880](https://github.com/qmk/qmk_firmware/pull/24880))
* Move Ergodox STM32 to handwired folder ([#24903](https://github.com/qmk/qmk_firmware/pull/24903))
* Remove readme dummy content ([#24912](https://github.com/qmk/qmk_firmware/pull/24912))
* Migrate some DEFAULT_FOLDER to keyboard_aliases.hjson ([#24915](https://github.com/qmk/qmk_firmware/pull/24915))
* Migrate some DEFAULT_FOLDER to keyboard_aliases.hjson ([#24938](https://github.com/qmk/qmk_firmware/pull/24938))

Keyboard fixes:
* Fix up CI with `DEFAULT_FOLDER`. ([#24842](https://github.com/qmk/qmk_firmware/pull/24842))
* rgbkb/pan - Remove invalid build target ([#24844](https://github.com/qmk/qmk_firmware/pull/24844))

Others:
* Formally deprecate DEFAULT_FOLDER ([#24836](https://github.com/qmk/qmk_firmware/pull/24836))
* Correct I2C API reference ([#24840](https://github.com/qmk/qmk_firmware/pull/24840))

Bugs:
* Retro Tapping Re-Write; Key Roll Fix ([#23641](https://github.com/qmk/qmk_firmware/pull/23641))
* Fix Quantum Painter compiliation issues with heavy optimization ([#24667](https://github.com/qmk/qmk_firmware/pull/24667))
* Bugfix and update for AT32F415 ([#24807](https://github.com/qmk/qmk_firmware/pull/24807))
* Fix for Chordal Hold: stuck mods when mod-taps are pressed in a stuttered sequence. ([#24878](https://github.com/qmk/qmk_firmware/pull/24878))
* fix EEPROM driver for STM32L0/1 cat.1 devices ([#24928](https://github.com/qmk/qmk_firmware/pull/24928))
