# QMK Breaking Changes - 2021 August 28 Changelog

## Notable Features {#notable-features}

### Combo processing improvements ([#8591](https://github.com/qmk/qmk_firmware/pull/8591)) {#combo-processing-improvements}

Combo processing has been reordered with respect to keypress handling, allowing for much better compatibility with mod taps.

It is also now possible to define combos that have keys overlapping with other combos, triggering only one. For example, a combo of `A`, `B` can coexist with a longer combo of `A`, `B`, `C` -- previous functionality would trigger both combos if all three keys were pressed.

### Key Overrides ([#11422](https://github.com/qmk/qmk_firmware/pull/11422)) {#key-overrides}

QMK now has a new feature: [key overrides](../features/key_overrides). This feature allows for overriding the output of key combinations involving modifiers. As an example, pressing <kbd>Shift+2</kbd> normally results in an <kbd>@</kbd> on US-ANSI keyboard layouts -- the new key overrides allow for adding similar functionality, but for any <kbd>modifier + key</kbd> press.

To illustrate, it's now possible to use the key overrides feature to translate <kbd>Shift + Backspace</kbd> into <kbd>Delete</kbd> -- an often-requested example of where this functionality comes in handy.

There's far more to describe that what lives in this changelog, so head over to the [key overrides documentation](../features/key_overrides) for more examples and info.

### Digitizer support ([#12851](https://github.com/qmk/qmk_firmware/pull/12851))

QMK gained the ability to pretend to be a digitizer device -- much like a tablet device. A mouse uses delta-coordinates -- move up, move right -- but a digitizer works with absolute coordinates -- top left, bottom right.

## Changes Requiring User Action {#changes-requiring-user-action}

### Updated Keyboard Codebases {#updated-keyboard-codebases}

The following keyboards have had their source moved within QMK:

Old Keyboard Name             | New Keyboard Name
------------------------------|---------------------------------------------------------
aeboards/constellation        | aeboards/constellation/rev1, aeboards/constellation/rev2
bakeneko65                    | bakeneko65/rev2, bakeneko65/rev3
bm16a                         | kprepublic/bm16a
bm16s                         | kprepublic/bm16s
bm40hsrgb                     | kprepublic/bm40hsrgb
bm43a                         | kprepublic/bm43a
bm60poker                     | kprepublic/bm60poker
bm60rgb                       | kprepublic/bm60rgb
bm60rgb_iso                   | kprepublic/bm60rgb_iso
bm68rgb                       | kprepublic/bm68rgb
clawsome/gamebuddy            | clawsome/gamebuddy/v1_0, clawsome/gamebuddy/v1_m
cospad                        | kprepublic/cospad
custommk/genesis              | custommk/genesis/rev1, custommk/genesis/rev2
daisy                         | ktec/daisy
durgod/k320                   | durgod/k3x0/k320
dztech/volcano660             | ilumkb/volcano660
ergodone                      | ktec/ergodone
gmmk/pro                      | gmmk/pro/ansi, gmmk/pro/iso
handwired/p1800fl             | team0110/p1800fl
jj40                          | kprepublic/jj40
jj4x4                         | kprepublic/jj4x4
jj50                          | kprepublic/jj50
kyria                         | splitkb/kyria
lazydesigners/the60           | lazydesigners/the60/rev1, lazydesigners/the60/rev2
matrix/m12og                  | matrix/m12og/rev1, matrix/m12og/rev2
mechlovin/hannah65/mechlovin9 | mechlovin/mechlovin9/rev1, mechlovin/mechlovin9/rev2
peiorisboards/ixora           | coarse/ixora
ramonimbao/mona               | ramonimbao/mona/v1, ramonimbao/mona/v1_1
staryu                        | ktec/staryu
tokyo60                       | tokyokeyboard/tokyo60
vinta                         | coarse/vinta
xd002                         | xiudi/xd002
xd004                         | xiudi/xd004
xd60                          | xiudi/xd60
xd68                          | xiudi/xd68
xd75                          | xiudi/xd75
xd84                          | xiudi/xd84
xd84pro                       | xiudi/xd84pro
xd87                          | xiudi/xd87
xd96                          | xiudi/xd96

### Bootmagic Full Removal ([#13846](https://github.com/qmk/qmk_firmware/pull/13846)) {#bootmagic-full-removal}

As noted during last breaking changes cycle, QMK has decided to deprecate the full Bootmagic feature and leave Bootmagic Lite as the only remaining option.

This pull request changes the behavior of `BOOTMAGIC_ENABLE` such that specifying `full` results in an error, allowing only `no`, `yes`, or `lite`.

Currently `lite` is the equivalent of `yes` in `rules.mk`. Next cycle the use of the `lite` keyword will be prevented in favour of `yes` -- any new submissions should now be using `yes` or `no` to minimise disruption.

#### Bootmagic Full Deprecation Schedule

This is the current roadmap for the behavior of `BOOTMAGIC_ENABLE`:

- (done) From 2021 May 29, setting `BOOTMAGIC_ENABLE = yes` will enable Bootmagic Lite instead of full Bootmagic.
- (now) From 2021 Aug 28, `BOOTMAGIC_ENABLE` must be either `yes`, `lite`, or `no` – setting `BOOTMAGIC_ENABLE = full` will cause compilation to fail.
- (next) From 2021 Nov 27, `BOOTMAGIC_ENABLE` must be either `yes` or `no` – setting `BOOTMAGIC_ENABLE = lite` will cause compilation to fail.

### DIP switch callbacks are now boolean ([#13399](https://github.com/qmk/qmk_firmware/pull/13399)) {#dip-switch-boolean}

To match the encoder change last breaking changes cycle, DIP switch callbacks now return `bool`, too.

Example code before change:

```c
void dip_switch_update_kb(uint8_t index, bool active) {
    dip_switch_update_user(index, active);
}

void dip_switch_update_user(uint8_t index, bool active) {
    switch (index) {
        case 0:
            if(active) { audio_on(); } else { audio_off(); }
            break;
    }
}

void dip_switch_update_mask_kb(uint32_t state) { 
    dip_switch_update_mask_user(state);
}

void dip_switch_update_mask_user(uint32_t state) {
    if (state & (1UL<<0) && state & (1UL<<1)) {
        layer_on(_ADJUST); // C on esc
    } else {
        layer_off(_ADJUST);
    }
}
```

Example code after change:

```c
bool dip_switch_update_kb(uint8_t index, bool active) {
    if !(dip_switch_update_user(index, active)) { return false; }
    return true;
}

bool dip_switch_update_user(uint8_t index, bool active) {
    switch (index) {
        case 0:
            if(active) { audio_on(); } else { audio_off(); }
            break;
    }
    return true; // Returning true allows keyboard code to execute, false will tell the keyboard code "I've already handled it".
}

bool dip_switch_update_mask_kb(uint32_t state) {
    if (!dip_switch_update_mask_user(state)) { return false; }
    return true;
}

bool dip_switch_update_mask_user(uint32_t state) {
    if (state & (1UL<<0) && state & (1UL<<1)) {
        layer_on(_ADJUST); // C on esc
    } else {
        layer_off(_ADJUST);
    }
    return true; // Returning true allows keyboard code to execute, false will tell the keyboard code "I've already handled it".
}
```

## Notable core changes {#notable-core}

### Split transport improvements {#split-transport-improvements}

Split keyboards gained a significant amount of improvements during this breaking changes cycle, specifically:

* Extensible split data sync ([#11930](https://github.com/qmk/qmk_firmware/pull/11930)) -- rewritten data sharing between sides, allowing for data transfer only when required, as well as enabling keyboards and keymaps to define their own shared data.
* Full-duplex ARM USART split ([#13081](https://github.com/qmk/qmk_firmware/pull/13081)) -- adds to the previous half-duplex driver and now allows for full-duplex support on ARM.
* Make solo half of split keyboards (more) usable. ([#13523](https://github.com/qmk/qmk_firmware/pull/13523)) -- allows the slave to be disconnected, enabling one-handed use.
* Switch split_common to CRC subsystem ([#13418](https://github.com/qmk/qmk_firmware/pull/13418))

::: warning
If you're updating your split keyboard, you will need to flash both sides of the split with the your firmware.
:::

### Teensy 4.x support ([#13056](https://github.com/qmk/qmk_firmware/pull/13056), [#13076](https://github.com/qmk/qmk_firmware/pull/13076), [#13077](https://github.com/qmk/qmk_firmware/pull/13077)) {#teensy-4-x-support}

Updated ChibiOS and ChibiOS-Contrib, which brought in support for Teensy 4.x dev boards, running NXP i.MX1062.

### Data Driven Improvements ([#13366](https://github.com/qmk/qmk_firmware/pull/13366))

QMK's pursuit of data-driven keyboards has progressed, allowing substantially more configurable options to be specified in `info.json`.

#### Tags

Tags will let you categorize your keyboard, and will be used in the future to allow browsing and sorting through keyboards in QMK. Tags are free-form text identifiers that identify attributes about your keyboard. To add tags you simply add a `tags` key to your `info.json`:

    "tags": ["tkl", "backlight", "encoder"]

#### Dot Notation

With this release we are moving towards using JSON dot notation in more places. For example, when using `qmk info -f text`:

```
$ qmk info -f text -kb clueboard/card
  bootloader: atmel-dfu
  debounce: 20
  diode_direction: ROW2COL
  features.audio: True
  features.backlight: True
  features.bluetooth: False
  features.bootmagic: False
  features.command: True
  features.console: True
  features.extrakey: True
  features.lto: True
  features.midi: False
  features.mousekey: True
  features.nkro: False
  features.rgblight: True
  features.unicode: False
  height: 8
  keyboard_folder: clueboard/card
  keyboard_name: Cluecard
  layout_aliases.LAYOUT: LAYOUT_all
  layouts: LAYOUT_all
  maintainer: skullydazed
  manufacturer: Clueboard
  matrix_pins.cols: F1, F6, F7
  matrix_pins.rows: B4, F0, F4, F5
  platform: unknown
  processor: atmega32u4
  processor_type: avr
  protocol: LUFA
  rgblight.brightness_steps: 17
  rgblight.hue_steps: 10
  rgblight.led_count: 4
  rgblight.pin: E6
  rgblight.saturation_steps: 17
  split.transport.protocol: serial
  usb.device_ver: 0x0001
  usb.pid: 0x2330
  usb.vid: 0xC1ED
  width: 10
```

#### New configuration keys

We've added dozens of new keys to `info.json` so that you can configure more than ever without writing a single line of code. A quick overview of the new items you can configure:

* `audio.pins`, `audio.voices`
* `backlight.breathing`, `backlight.breathing_period`, `backlight.levels`, `backlight.pin`,
* `bluetooth.driver`, `bluetooth.lto`
* `bootloader_instructions`
* `build.debounce_type`, `build.firmware_format`, `build.lto`
* `combo.count`, `combo.term`
* `leader_key.timing`, `leader_key.strict_processing`, `leader_key.timeout`
* `matrix.custom`, `matrix.custom_lite`, `matrix.ghost`, `matrix.io_delay`
* `mouse_key.enabled`, `mouse_key.delay`, `mouse_key.interval`, `mouse_key.max_speed`, `mouse_key.time_to_max`, `mouse_key.wheel_delay`
* `oneshot.tap_toggle`, `oneshot.timeout`
* `rgblight.layers.blink`, `rgblight.layers.enabled`, `rgblight.layers.max`, `rgblight.layers.override_rgb`, `rgblight.rgbw`
* `split.enabled`, `split.matrix_grid`, `split.matrix_pins`, `split.main`, `split.soft_serial_pin`, `split.soft_serial_speed`, `split.transport.protocol`, `split.transport.sync_matrix_state`, `split.transport.sync_modifiers`, `split.usb_detect`
* `tapping.force_hold`, `tapping.force_hold_per_key`, `tapping.ignore_mod_tap_interrupt`, `tapping.ignore_mod_tap_interrupt_per_key`, `tapping.permissive_hold`, `tapping.permissive_hold_per_key`, `tapping.retro`, `tapping.retro_per_key`, `tapping.term`, `tapping.term_per_key`, `tapping.toggle`
* `usb.force_nkro`, `usb.max_power`, `usb.no_startup_check`, `usb.polling_interval`, `usb.shared_endpoint.keyboard`, `usb.shared_endpoint.mouse`, `usb.suspend_wakeup_delay`, `usb.wait_for`
* `qmk.keys_per_scan`, `qmk.tap_keycode_delay`, `qmk.tap_capslock_delay`

### Codebase restructure and cleanup {#codebase-restructure}

QMK was originally based on TMK, and has grown in size considerably since its first inception. To keep moving things forward, restructure of some of the core areas of the code is needed to support new concepts and new hardware, and progress is happening along those lines:

* Move RGBLight code into its own folder ([#13312](https://github.com/qmk/qmk_firmware/pull/13312))
* Migrate platform independent code from tmk_core -> quantum ([#13673](https://github.com/qmk/qmk_firmware/pull/13673))
* matrix_scan_x -> x_task ([#13748](https://github.com/qmk/qmk_firmware/pull/13748))
* Move some led drivers to common folder ([#13749](https://github.com/qmk/qmk_firmware/pull/13749))
* Move chibios board files to allow tmk_core platform migration ([#13777](https://github.com/qmk/qmk_firmware/pull/13777))
* Begin to carve out platform/protocol API - Single main loop ([#13843](https://github.com/qmk/qmk_firmware/pull/13843))
* Relocate platform specific drivers ([#13894](https://github.com/qmk/qmk_firmware/pull/13894))
* Move all the flash logic from tmk_core ([#13927](https://github.com/qmk/qmk_firmware/pull/13927))
* Move USB Host Shield and Arduino core to `lib/` ([#13973](https://github.com/qmk/qmk_firmware/pull/13973))
* Unify behaviour of wait on AVR ([#14025](https://github.com/qmk/qmk_firmware/pull/14025))
* Move nix folder alongside vagrant ([#14132](https://github.com/qmk/qmk_firmware/pull/14132))
* Align some quantum sub-directories ([#14134](https://github.com/qmk/qmk_firmware/pull/14134))

---

## Full changelist

Core:
* Arm ps2 mouse interrupt ([#6490](https://github.com/qmk/qmk_firmware/pull/6490))
* Process combos earlier & overlapping combos ([#8591](https://github.com/qmk/qmk_firmware/pull/8591))
* Swap buttons on PS2 Mouse/Trackball ([#9205](https://github.com/qmk/qmk_firmware/pull/9205))
* Add HOLD_ON_OTHER_KEY_PRESS option for dual-role keys ([#9404](https://github.com/qmk/qmk_firmware/pull/9404))
* add yaml_build_options target ([#10533](https://github.com/qmk/qmk_firmware/pull/10533))
* Warn when building a board that uses arm_atsam ([#10904](https://github.com/qmk/qmk_firmware/pull/10904))
* Key Overrides ([#11422](https://github.com/qmk/qmk_firmware/pull/11422))
* Refactor `quantum/command.{c,h}` for code size & {read,maintain}ability ([#11842](https://github.com/qmk/qmk_firmware/pull/11842))
* Extensible split data sync ([#11930](https://github.com/qmk/qmk_firmware/pull/11930))
* Move print/debug files to quantum ([#12069](https://github.com/qmk/qmk_firmware/pull/12069))
* Unconditionally call led_init_ports ([#12116](https://github.com/qmk/qmk_firmware/pull/12116))
* Support using a timer for wait_us() on ChibiOS-based boards ([#12211](https://github.com/qmk/qmk_firmware/pull/12211))
* Add support for NO_PIN to all matrix types ([#12238](https://github.com/qmk/qmk_firmware/pull/12238))
* Avoid 8-bit timer overflows in debounce algorithms ([#12240](https://github.com/qmk/qmk_firmware/pull/12240))
* Add Per Key exclusions for Haptic Feedback ([#12386](https://github.com/qmk/qmk_firmware/pull/12386))
* Steno combinedkeys ([#12538](https://github.com/qmk/qmk_firmware/pull/12538))
* eeprom_stm32: implement high density wear leveling ([#12567](https://github.com/qmk/qmk_firmware/pull/12567))
* eeprom_i2c driver: added EXTERNAL_EEPROM_WP_PIN configuration option. ([#12617](https://github.com/qmk/qmk_firmware/pull/12617))
* Add CRC8 calculation subsystem to quantum ([#12641](https://github.com/qmk/qmk_firmware/pull/12641))
* Limit saturation for RGB_MATRIX_JELLYBEAN_RAINDROPS ([#12669](https://github.com/qmk/qmk_firmware/pull/12669))
* Add asym_eager_defer_pk debounce type ([#12689](https://github.com/qmk/qmk_firmware/pull/12689))
* Include lib8tion.c into RGB/LED matrix build list ([#12699](https://github.com/qmk/qmk_firmware/pull/12699))
* Add readPort() and some API to 'tmk_core/common/*/gpio.h' ([#12754](https://github.com/qmk/qmk_firmware/pull/12754))
* add wait_cpuclock() macro for AVR and CPU_CLOCK macro ([#12755](https://github.com/qmk/qmk_firmware/pull/12755))
* Trigger a wakeup after USB Reset on ChibiOS. ([#12831](https://github.com/qmk/qmk_firmware/pull/12831))
* Add sync_timer support over serial_link (i.e. Ergodox Infinity) ([#12845](https://github.com/qmk/qmk_firmware/pull/12845))
* Digitizer HID interface : absolute coordinates for mouse cursor ([#12851](https://github.com/qmk/qmk_firmware/pull/12851))
* Add config.h and rules.mk support for data driven keymaps ([#12859](https://github.com/qmk/qmk_firmware/pull/12859))
* Add alternate ldscript for STM32duino (F103xB) ([#12914](https://github.com/qmk/qmk_firmware/pull/12914))
* `keymap_extras`: Remove deprecated defines ([#12949](https://github.com/qmk/qmk_firmware/pull/12949))
* Retain brightness with lighting layers ([#13025](https://github.com/qmk/qmk_firmware/pull/13025))
* Move optical sensor code to drivers folder ([#13044](https://github.com/qmk/qmk_firmware/pull/13044))
* Change the prototype of matrix_output_unselect_delay() ([#13045](https://github.com/qmk/qmk_firmware/pull/13045))
* Add weak refs on reading rows/cols. ([#13062](https://github.com/qmk/qmk_firmware/pull/13062))
* Use single memcmp to determine if matrix changed. ([#13064](https://github.com/qmk/qmk_firmware/pull/13064))
* Improve layer mask handling ([#13065](https://github.com/qmk/qmk_firmware/pull/13065))
* mousekey: expose current report to users ([#13069](https://github.com/qmk/qmk_firmware/pull/13069))
* ChibiOS SVN mirror script. ([#13070](https://github.com/qmk/qmk_firmware/pull/13070))
* Added right vs left specific pin assignments for dip switch ([#13074](https://github.com/qmk/qmk_firmware/pull/13074))
* make RESET key work with Teensy 4.x ([#13076](https://github.com/qmk/qmk_firmware/pull/13076))
* wire up flash make target for Teensy 4.x ([#13077](https://github.com/qmk/qmk_firmware/pull/13077))
* bump USB spec version in device descriptor to 2.0 ([#13078](https://github.com/qmk/qmk_firmware/pull/13078))
* Unite half-duplex and full-duplex serial drivers ([#13081](https://github.com/qmk/qmk_firmware/pull/13081))
* Add ST7565 LCD driver ([#13089](https://github.com/qmk/qmk_firmware/pull/13089))
* `spi_master` Kinetis support ([#13098](https://github.com/qmk/qmk_firmware/pull/13098))
* GMMK Pro RGB Support ([#13147](https://github.com/qmk/qmk_firmware/pull/13147))
* Remove dfu-util arguments from mcu_selection ([#13150](https://github.com/qmk/qmk_firmware/pull/13150))
* Add subcommand to generate version.h ([#13151](https://github.com/qmk/qmk_firmware/pull/13151))
* Add oled_invert ([#13172](https://github.com/qmk/qmk_firmware/pull/13172))
* ST7565 invert ([#13237](https://github.com/qmk/qmk_firmware/pull/13237))
* RGB Matrix eeprom write limiting ([#13238](https://github.com/qmk/qmk_firmware/pull/13238))
* Temporary disable of CRC ([#13252](https://github.com/qmk/qmk_firmware/pull/13252))
* Move LED/RGB Matrix code into their own directories ([#13257](https://github.com/qmk/qmk_firmware/pull/13257))
* Skip EEPROM writes once done. ([#13293](https://github.com/qmk/qmk_firmware/pull/13293))
* Remove rgblight stubs ([#13302](https://github.com/qmk/qmk_firmware/pull/13302))
* Allow settable SPI divisor for AW20216 driver, set default to 4 ([#13309](https://github.com/qmk/qmk_firmware/pull/13309))
* Move RGBLight code into its own folder ([#13312](https://github.com/qmk/qmk_firmware/pull/13312))
* Unify matrix for split common and regular matrix ([#13330](https://github.com/qmk/qmk_firmware/pull/13330))
* Relocate RGB/HSV color defs to a more fitting place ([#13377](https://github.com/qmk/qmk_firmware/pull/13377))
* Adds support for STM32L412xB, STM32L422xB. ([#13383](https://github.com/qmk/qmk_firmware/pull/13383))
* Convert Dip Switch callbacks to boolean functions ([#13399](https://github.com/qmk/qmk_firmware/pull/13399))
* Use string literals for `SERIAL_NUMBER` ([#13403](https://github.com/qmk/qmk_firmware/pull/13403))
* Switch split_common to CRC subsystem ([#13418](https://github.com/qmk/qmk_firmware/pull/13418))
* Improve 'show_build_options' target ([#13425](https://github.com/qmk/qmk_firmware/pull/13425))
* AW20216 use register increment for framebuffer flushes ([#13430](https://github.com/qmk/qmk_firmware/pull/13430))
* Allow invert of SPLIT_HAND_PIN logic ([#13433](https://github.com/qmk/qmk_firmware/pull/13433))
* chibios: bootloader: use integer pointers as volatile ([#13450](https://github.com/qmk/qmk_firmware/pull/13450))
* Refactor OLED to allow easy addition of other types ([#13454](https://github.com/qmk/qmk_firmware/pull/13454))
* Dual RGB Matrix IS31FL3737 driver support to address #13442 ([#13457](https://github.com/qmk/qmk_firmware/pull/13457))
* Enable g_is31_leds PROGMEM for RGB Matrix IS31FL3737 driver ([#13480](https://github.com/qmk/qmk_firmware/pull/13480))
* Switch Ergodox Infinity over to split_common ([#13481](https://github.com/qmk/qmk_firmware/pull/13481))
* Make solo half of split keyboards (more) usable. ([#13523](https://github.com/qmk/qmk_firmware/pull/13523))
* Enable sync of OLED/ST7565 display on/off state on Splits ([#13542](https://github.com/qmk/qmk_firmware/pull/13542))
* Revert "Add rgblight to RGB Matrix VPATH" ([#13559](https://github.com/qmk/qmk_firmware/pull/13559))
* Move `SENDSTRING_BELL` code to `send_string.h` ([#13566](https://github.com/qmk/qmk_firmware/pull/13566))
* Migrate platform independent code from tmk_core -> quantum ([#13673](https://github.com/qmk/qmk_firmware/pull/13673))
* Avoid LTO conficts on arm_atsam ([#13676](https://github.com/qmk/qmk_firmware/pull/13676))
* Allow for removal of hysteresis on 4x encoders ([#13698](https://github.com/qmk/qmk_firmware/pull/13698))
* Port new_keyboard.sh to CLI ([#13706](https://github.com/qmk/qmk_firmware/pull/13706))
* Align AW20216 driver ([#13712](https://github.com/qmk/qmk_firmware/pull/13712))
* Haptic: driver-> feature ([#13713](https://github.com/qmk/qmk_firmware/pull/13713))
* Add support for STM32F407x MCUs. ([#13718](https://github.com/qmk/qmk_firmware/pull/13718))
* Remove legacy BACKLIGHT_CUSTOM_DRIVER option ([#13731](https://github.com/qmk/qmk_firmware/pull/13731))
* Minor tidy up of key overrides ([#13747](https://github.com/qmk/qmk_firmware/pull/13747))
* matrix_scan_x -> x_task ([#13748](https://github.com/qmk/qmk_firmware/pull/13748))
* Move some led drivers to common folder ([#13749](https://github.com/qmk/qmk_firmware/pull/13749))
* Allow for higher USB Polling rate on ATSAM boards ([#13755](https://github.com/qmk/qmk_firmware/pull/13755))
* Rgb matrix/enable modes explicitly ([#13758](https://github.com/qmk/qmk_firmware/pull/13758))
* Move chibios board files to allow tmk_core platform migration ([#13777](https://github.com/qmk/qmk_firmware/pull/13777))
* __flash? ([#13799](https://github.com/qmk/qmk_firmware/pull/13799))
* `--parallel` improvements ([#13800](https://github.com/qmk/qmk_firmware/pull/13800))
* Speed up pimoroni trackball driver ([#13823](https://github.com/qmk/qmk_firmware/pull/13823))
* Add a toggle key for GUI On/Off in Magic feature ([#13830](https://github.com/qmk/qmk_firmware/pull/13830))
* Begin to carve out platform/protocol API - Single main loop ([#13843](https://github.com/qmk/qmk_firmware/pull/13843))
* Remove Full Bootmagic ([#13846](https://github.com/qmk/qmk_firmware/pull/13846))
* Remove backwards compatibility of debounce names ([#13877](https://github.com/qmk/qmk_firmware/pull/13877))
* Relocate platform specific drivers ([#13894](https://github.com/qmk/qmk_firmware/pull/13894))
* Remove ONEHAND_ENABLE ([#13920](https://github.com/qmk/qmk_firmware/pull/13920))
* Move all the flash logic from tmk_core ([#13927](https://github.com/qmk/qmk_firmware/pull/13927))
* adding uf2 flash support for blackpill 401 ([#13968](https://github.com/qmk/qmk_firmware/pull/13968))
* Unify behaviour of wait on AVR ([#14025](https://github.com/qmk/qmk_firmware/pull/14025))
* Add qmk-hid bootloader detection support to `qmk console` ([#14038](https://github.com/qmk/qmk_firmware/pull/14038))
* Align DIP_SWITCH_PINS_RIGHT implementation with encoders ([#14079](https://github.com/qmk/qmk_firmware/pull/14079))
* Tidy up quantum.c now some of tmk_core has been merged ([#14083](https://github.com/qmk/qmk_firmware/pull/14083))
* Improve pmw3360 sensor and make it more hardware agnostic ([#14097](https://github.com/qmk/qmk_firmware/pull/14097))
* Move nix folder alongside vagrant ([#14132](https://github.com/qmk/qmk_firmware/pull/14132))
* Align some quantum sub-directories ([#14134](https://github.com/qmk/qmk_firmware/pull/14134))
* Revert 14083 && 14144 ([#14150](https://github.com/qmk/qmk_firmware/pull/14150))

CLI:
* allow LINE_PINxx for Teensy 4.x pins ([#13247](https://github.com/qmk/qmk_firmware/pull/13247))
* Remove the redundant pin name validation ([#13251](https://github.com/qmk/qmk_firmware/pull/13251))
* Move all our CLI file formatters to the format dir ([#13296](https://github.com/qmk/qmk_firmware/pull/13296))
* Refactor doctor.py into a directory ([#13298](https://github.com/qmk/qmk_firmware/pull/13298))
* Add git and venv info to doctor's output ([#13405](https://github.com/qmk/qmk_firmware/pull/13405))
* Matrix consistency check ([#13470](https://github.com/qmk/qmk_firmware/pull/13470))
* Remove references to info.json `width` and `height` in CLI ([#13728](https://github.com/qmk/qmk_firmware/pull/13728))
* Make `qmk doctor` more lenient about system config ([#13804](https://github.com/qmk/qmk_firmware/pull/13804))
* Defer the expensive search for layout macros until info.json has been processed ([#14007](https://github.com/qmk/qmk_firmware/pull/14007))

Submodule updates:
* Update ChibiOS, ChibiOS-Contrib. ([#13056](https://github.com/qmk/qmk_firmware/pull/13056))
* Update LUFA (18-07-2021) and add QMK-HID Bootloader support ([#13588](https://github.com/qmk/qmk_firmware/pull/13588))
* Update LUFA Submodule (2021-07-30) ([#13819](https://github.com/qmk/qmk_firmware/pull/13819))
* Bump gtest ([#13885](https://github.com/qmk/qmk_firmware/pull/13885))
* Update ChibiOS-Contrib, mirroring script. ([#13896](https://github.com/qmk/qmk_firmware/pull/13896))
* Move USB Host Shield and Arduino core to `lib/` ([#13973](https://github.com/qmk/qmk_firmware/pull/13973))

Keyboards:
* Migrate keyboards using uGFX to LED_MATRIX ([#9657](https://github.com/qmk/qmk_firmware/pull/9657))
* Remove MIDI Configuration boilerplate ([#11151](https://github.com/qmk/qmk_firmware/pull/11151))
* manyboard macro ([#11896](https://github.com/qmk/qmk_firmware/pull/11896))
* Moved tokyo60/ into tokyokeyboard/tokyo60/. ([#12023](https://github.com/qmk/qmk_firmware/pull/12023))
* Organize KPrepublic, K.T.E.C, xiudi boards into directories ([#12159](https://github.com/qmk/qmk_firmware/pull/12159))
* Add Durgod Taurus K310 keyboard ([#12314](https://github.com/qmk/qmk_firmware/pull/12314))
* add support for m65 and simple 5x13 ortholinear ([#12315](https://github.com/qmk/qmk_firmware/pull/12315))
* Relocalize and Update p1800fl ([#12425](https://github.com/qmk/qmk_firmware/pull/12425))
* GameBuddy v1.M ([#12637](https://github.com/qmk/qmk_firmware/pull/12637))
* Add mechlovin9 rev2 PCB ([#12767](https://github.com/qmk/qmk_firmware/pull/12767))
* Add RGB matrix support for Kyria ([#12789](https://github.com/qmk/qmk_firmware/pull/12789))
* RGB Matrix working for Sofle RGB ([#12861](https://github.com/qmk/qmk_firmware/pull/12861))
* Add Durgod Hades, Galaxy and Venus Keyboards ([#12893](https://github.com/qmk/qmk_firmware/pull/12893))
* kint36: set correct EEPROM size ([#12946](https://github.com/qmk/qmk_firmware/pull/12946))
* Updated encoder_update_user on my keymap to follow the new signature on quantum ([#13152](https://github.com/qmk/qmk_firmware/pull/13152))
* Add Creator Pro by SergioPoverony ([#13154](https://github.com/qmk/qmk_firmware/pull/13154))
* Use the new ST7565 driver on Ergodox Infinity ([#13165](https://github.com/qmk/qmk_firmware/pull/13165))
* Refactor atom47 and add rev4 and rev5 ([#13201](https://github.com/qmk/qmk_firmware/pull/13201))
* Add Bakeneko65 V3 and revision folders ([#13228](https://github.com/qmk/qmk_firmware/pull/13228))
* Keyboards/RGBKB/Mün ([#13239](https://github.com/qmk/qmk_firmware/pull/13239))
* Optimize our jsonschema by using refs ([#13271](https://github.com/qmk/qmk_firmware/pull/13271))
* Handwired/Stream_Cheap/2x4: Add via support ([#13297](https://github.com/qmk/qmk_firmware/pull/13297))
* ez_maker/directpins for easy one-offs in qmk_configurator ([#13321](https://github.com/qmk/qmk_firmware/pull/13321))
* add kinT kinesis keyboard controller (kint41 variant) ([#13333](https://github.com/qmk/qmk_firmware/pull/13333))
* Error log cleanup ([#13349](https://github.com/qmk/qmk_firmware/pull/13349))
* Drashna's split updates ([#13350](https://github.com/qmk/qmk_firmware/pull/13350))
* Migrate SHIFT_ESC and RGB `fn_actions` to Grave Escape and RGB keycodes ([#13360](https://github.com/qmk/qmk_firmware/pull/13360))
* Add a lot more data to info.json ([#13366](https://github.com/qmk/qmk_firmware/pull/13366))
* Remove `API_SYSEX_ENABLE`s from rules.mk ([#13389](https://github.com/qmk/qmk_firmware/pull/13389))
* gmmk/pro/mike1808 keymap ([#13398](https://github.com/qmk/qmk_firmware/pull/13398))
* Remove deprecated callbacks for encoders and dip switches ([#13404](https://github.com/qmk/qmk_firmware/pull/13404))
* first pass: matrix consistency improvements ([#13471](https://github.com/qmk/qmk_firmware/pull/13471))
* Migrate more `fn_actions` stuff ([#13502](https://github.com/qmk/qmk_firmware/pull/13502))
* add simple gmmk pro macos keymap with rgb ([#13504](https://github.com/qmk/qmk_firmware/pull/13504))
* move volcano660 to ilumkb folder ([#13550](https://github.com/qmk/qmk_firmware/pull/13550))
* Valor Rev 2 ([#13551](https://github.com/qmk/qmk_firmware/pull/13551))
* Split GMMK Pro PCBs into separate revisions ([#13570](https://github.com/qmk/qmk_firmware/pull/13570))
* Remove the vision_division keyboard ([#13571](https://github.com/qmk/qmk_firmware/pull/13571))
* Develop - Change uint32_t to layer_state_t ([#13596](https://github.com/qmk/qmk_firmware/pull/13596))
* Develop - DC01 left ([#13597](https://github.com/qmk/qmk_firmware/pull/13597))
* Created "paddlegame" keymap ([#13629](https://github.com/qmk/qmk_firmware/pull/13629))
* Add timer_avr to includes for broken builds ([#13641](https://github.com/qmk/qmk_firmware/pull/13641))
* Disable console by default on all Keebio boards ([#13649](https://github.com/qmk/qmk_firmware/pull/13649))
* Enable LTO by default on BastardKB Scylla ([#13664](https://github.com/qmk/qmk_firmware/pull/13664))
* Reduce compile size for dz60rgb v2.1 ([#13680](https://github.com/qmk/qmk_firmware/pull/13680))
* Clean up remaining RGB_DISABLE_WHEN_USB_SUSPENDED defines ([#13689](https://github.com/qmk/qmk_firmware/pull/13689))
* Remove some legacy files ([#13715](https://github.com/qmk/qmk_firmware/pull/13715))
* [Keyboard Update] Change to L422 ([#13717](https://github.com/qmk/qmk_firmware/pull/13717))
* Update kyria make path example ([#13720](https://github.com/qmk/qmk_firmware/pull/13720))
* Drashna's Defaults cleanup ([#13722](https://github.com/qmk/qmk_firmware/pull/13722))
* Reduce firmware size in prep for #12670 ([#13724](https://github.com/qmk/qmk_firmware/pull/13724))
* Tidy up rgbkb/mun ([#13801](https://github.com/qmk/qmk_firmware/pull/13801))
* Make default keymap for GMMK Pro reflect stock ([#13850](https://github.com/qmk/qmk_firmware/pull/13850))
* Rework as per 9824 ([#13898](https://github.com/qmk/qmk_firmware/pull/13898))
* Remove console from keebio via keyboards ([#13901](https://github.com/qmk/qmk_firmware/pull/13901))
* Drashna split transport improvement ([#13905](https://github.com/qmk/qmk_firmware/pull/13905))
* Copy GMMK Pro screw specs to ISO readme ([#13908](https://github.com/qmk/qmk_firmware/pull/13908))
* Clean up remaining RGB_DISABLE_WHEN_USB_SUSPENDED defines Part 2 ([#13912](https://github.com/qmk/qmk_firmware/pull/13912))
* Add andrebrait layout for GMMK Pro ([#13932](https://github.com/qmk/qmk_firmware/pull/13932))
* Updated RGB Matrix suspend define part 3 ([#13954](https://github.com/qmk/qmk_firmware/pull/13954))
* Improve andrebrait keymap ([#13985](https://github.com/qmk/qmk_firmware/pull/13985))
* Drashna's Improve OLEDs and custom Split code ([#14063](https://github.com/qmk/qmk_firmware/pull/14063))
* Kyria default reformat ([#14080](https://github.com/qmk/qmk_firmware/pull/14080))
* Feature rich keymap for GMMK Pro (ANSI) ([#14120](https://github.com/qmk/qmk_firmware/pull/14120))

Keyboard fixes:
* Fix LED mapping for GMMK Pro ([#13189](https://github.com/qmk/qmk_firmware/pull/13189))
* Fix up SplitKB keyboards ([#13511](https://github.com/qmk/qmk_firmware/pull/13511))
* Keyboards/sol rev2 fix ([#13533](https://github.com/qmk/qmk_firmware/pull/13533))
* Fix MATRIX_COLS for aeboards/constellation/rev2 ([#13633](https://github.com/qmk/qmk_firmware/pull/13633))
* Fix errors with matrix_output_unselect_delay function calls ([#13645](https://github.com/qmk/qmk_firmware/pull/13645))
* Fix default keymap for 0xCB 1337 keyboard ([#13646](https://github.com/qmk/qmk_firmware/pull/13646))
* Fix Matrix Row number for ggkeyboards/genisis ([#13647](https://github.com/qmk/qmk_firmware/pull/13647))
* Fix matrix issues with Promethium ([#13648](https://github.com/qmk/qmk_firmware/pull/13648))
* Fix dc01/left so that it doesn't throw a warning ([#13653](https://github.com/qmk/qmk_firmware/pull/13653))
* Remove broken, unmaintained converter/ibm_5291 ([#13658](https://github.com/qmk/qmk_firmware/pull/13658))
* Quick hack to fix Astro65 board ([#13665](https://github.com/qmk/qmk_firmware/pull/13665))
* Fix symmetric70_proto build break on develop branch ([#13667](https://github.com/qmk/qmk_firmware/pull/13667))
* Fix matrix delay on Drop boards ([#13671](https://github.com/qmk/qmk_firmware/pull/13671))
* Fix split matrix for sekigon grs 70ec ([#13672](https://github.com/qmk/qmk_firmware/pull/13672))
* Fix type on pandora via keymap ([#13681](https://github.com/qmk/qmk_firmware/pull/13681))
* Fix & clean up tronguylabs/m122_3270 ([#13684](https://github.com/qmk/qmk_firmware/pull/13684))
* Fix up xd002 rgb keymaps ([#13685](https://github.com/qmk/qmk_firmware/pull/13685))
* Dactyl Manuform cleanup ([#13686](https://github.com/qmk/qmk_firmware/pull/13686))
* Fix Q1 change dip switch to bool ([#13687](https://github.com/qmk/qmk_firmware/pull/13687))
* Fix compile size for the Merge UM70 via keymap ([#13690](https://github.com/qmk/qmk_firmware/pull/13690))
* Fix compile size for the Lets Split Sockets via keymap ([#13691](https://github.com/qmk/qmk_firmware/pull/13691))
* Fix Compile size on ungodly Launch Pad ([#13692](https://github.com/qmk/qmk_firmware/pull/13692))
* dirty fix ([#13695](https://github.com/qmk/qmk_firmware/pull/13695))
* Fix compile size for the Vitamins Included via keymap ([#13696](https://github.com/qmk/qmk_firmware/pull/13696))
* Fix typo in Dactyl Manuform ([#13740](https://github.com/qmk/qmk_firmware/pull/13740))
* Fix compile issues due to LED changes ([#13821](https://github.com/qmk/qmk_firmware/pull/13821))
* Fix SRC include for matrix/m20add issi driver ([#13826](https://github.com/qmk/qmk_firmware/pull/13826))
* fix develop branch move file ([#13832](https://github.com/qmk/qmk_firmware/pull/13832))
* Fix knops keymaps ([#13872](https://github.com/qmk/qmk_firmware/pull/13872))
* Switch Draculad to using WPM char hack ([#13886](https://github.com/qmk/qmk_firmware/pull/13886))
* Fix up builds after #8591 ([#13900](https://github.com/qmk/qmk_firmware/pull/13900))
* Fix matrix_output_unselect_delay for handwired/xealousbrown ([#13913](https://github.com/qmk/qmk_firmware/pull/13913))
* Fixup rgb matrix config for KBD67 mkII boards ([#13931](https://github.com/qmk/qmk_firmware/pull/13931))
* Fix compliation for ferris 0.2 bling ([#13937](https://github.com/qmk/qmk_firmware/pull/13937))
* Fix some additional bootmagic settings ([#13979](https://github.com/qmk/qmk_firmware/pull/13979))
* Fix default keymap for GMMK Pro Iso ([#13980](https://github.com/qmk/qmk_firmware/pull/13980))
* Fixup Ungodly Launch Pad config ([#13992](https://github.com/qmk/qmk_firmware/pull/13992))
* Fix errors that have cropped up in develop ([#14005](https://github.com/qmk/qmk_firmware/pull/14005))
* Fix wait_us overflow in matrix for dactyl based boards ([#14039](https://github.com/qmk/qmk_firmware/pull/14039))
* Fixup Neson Design N6 ISSI includes ([#14045](https://github.com/qmk/qmk_firmware/pull/14045))
* Fixup `massdrop/alt`, `cest73/tkm`. ([#14048](https://github.com/qmk/qmk_firmware/pull/14048))
* fix helix:fraanrosi compile error caused by #13677. ([#14061](https://github.com/qmk/qmk_firmware/pull/14061))
* Fix compile issues for Tractyl Manuform ([#14105](https://github.com/qmk/qmk_firmware/pull/14105))
* Disable Console on Keebio Quefrency ([#14108](https://github.com/qmk/qmk_firmware/pull/14108))
* Fixed GMMK Pro -> stickandgum keymap readme.md ([#14123](https://github.com/qmk/qmk_firmware/pull/14123))
* Drashna keymap fixups ([#14140](https://github.com/qmk/qmk_firmware/pull/14140))
* fix ([#14142](https://github.com/qmk/qmk_firmware/pull/14142))
* Fix merge artifacts ([#14146](https://github.com/qmk/qmk_firmware/pull/14146))
* Update readme files ([#14172](https://github.com/qmk/qmk_firmware/pull/14172))

Others:
* Add examples to RGB Matrix Indicators docs ([#12797](https://github.com/qmk/qmk_firmware/pull/12797))

Bugs:
* Fix Indicator LED issues ([#12097](https://github.com/qmk/qmk_firmware/pull/12097))
* Fixing incorrect keymap build when switching between multiple keymap.jsons ([#12632](https://github.com/qmk/qmk_firmware/pull/12632))
* Fix LED Hit Counter for LED/RGB Matrix ([#12674](https://github.com/qmk/qmk_firmware/pull/12674))
* ChibiOS fix O3 and LTO breakage of extra keys and joystick ([#12819](https://github.com/qmk/qmk_firmware/pull/12819))
* Remove the #10088 hotfix for Teensy 3.1-like Input:Club keyboards ([#12870](https://github.com/qmk/qmk_firmware/pull/12870))
* Fix firmware size check with avr-libc 1:2.0.0+Atmel3.6.2-1.1 (Debian bullseye) ([#12951](https://github.com/qmk/qmk_firmware/pull/12951))
* Fix RGB/LED Suspend defines ([#13146](https://github.com/qmk/qmk_firmware/pull/13146))
* Fix overrun in st7565_write_raw when not at (0, 0) ([#13209](https://github.com/qmk/qmk_firmware/pull/13209))
* Upgrades Vagrant box to Debian 10 to fix Docker build error on Debian 9. ([#13236](https://github.com/qmk/qmk_firmware/pull/13236))
* Fix issues with VIA EEPROM init and bring in line with eeconfig functionality ([#13243](https://github.com/qmk/qmk_firmware/pull/13243))
* Fix CRC for AVR and enable again. ([#13253](https://github.com/qmk/qmk_firmware/pull/13253))
* Fix linker error when rgblight and RGB Matrix are both enabled ([#13304](https://github.com/qmk/qmk_firmware/pull/13304))
* Fix building layouts from JSON ([#13310](https://github.com/qmk/qmk_firmware/pull/13310))
* Add rgblight to RGB Matrix VPATH ([#13371](https://github.com/qmk/qmk_firmware/pull/13371))
* Fix two out of bounds accesses from #13330. ([#13525](https://github.com/qmk/qmk_firmware/pull/13525))
* Fixes for clang not being able to run unit tests ([#13546](https://github.com/qmk/qmk_firmware/pull/13546))
* Fixup Audio startup and add to documents ([#13606](https://github.com/qmk/qmk_firmware/pull/13606))
* CLI/Docs: Fix the format commands' name ([#13668](https://github.com/qmk/qmk_firmware/pull/13668))
* Disables rgblight twinkle by default. ([#13677](https://github.com/qmk/qmk_firmware/pull/13677))
* Fix typo in dip switch example ([#13688](https://github.com/qmk/qmk_firmware/pull/13688))
* docs/cli_commands: fix typo ([#13697](https://github.com/qmk/qmk_firmware/pull/13697))
* Include gpio.h in solenoid driver for GPIO Control functions ([#13716](https://github.com/qmk/qmk_firmware/pull/13716))
* Fix pimoroni trackball read address ([#13810](https://github.com/qmk/qmk_firmware/pull/13810))
* Fix Key Override includes ([#13831](https://github.com/qmk/qmk_firmware/pull/13831))
* Fix alignment of USB out report buffer 2 -> 4 ([#13838](https://github.com/qmk/qmk_firmware/pull/13838))
* Fix compilation issue. ([#13926](https://github.com/qmk/qmk_firmware/pull/13926))
* Fix `combo_disable` ([#13988](https://github.com/qmk/qmk_firmware/pull/13988))
* Fix pmw3360 code to only output debug info if mouse debugging is enabled ([#13993](https://github.com/qmk/qmk_firmware/pull/13993))
* Fix ifdefs for OLED split sync code ([#14017](https://github.com/qmk/qmk_firmware/pull/14017))
* Various fixes from reorg of files ([#14051](https://github.com/qmk/qmk_firmware/pull/14051))
* Fixup atsam builds. ([#14052](https://github.com/qmk/qmk_firmware/pull/14052))
* Fix RGB/LED Matrix Suspend code ([#14084](https://github.com/qmk/qmk_firmware/pull/14084))
* Fix issues with recent keymap.json changes ([#14089](https://github.com/qmk/qmk_firmware/pull/14089))
* Fix LED Matrix suspend code ([#14090](https://github.com/qmk/qmk_firmware/pull/14090))
* Fix up compilation issues. ([#14095](https://github.com/qmk/qmk_firmware/pull/14095))
* Fix copypasta issue with pmw3360 sensor config ([#14106](https://github.com/qmk/qmk_firmware/pull/14106))
* Fix typo ([#14118](https://github.com/qmk/qmk_firmware/pull/14118))
* Fix bootloadHID comments breaking :flash ([#14133](https://github.com/qmk/qmk_firmware/pull/14133))
* Fix Mouse Shared EP functionality ([#14136](https://github.com/qmk/qmk_firmware/pull/14136))
* Short term bodge for firmware size bloat ([#14144](https://github.com/qmk/qmk_firmware/pull/14144))
* Move to correct location ([#14171](https://github.com/qmk/qmk_firmware/pull/14171))
