# QMK Breaking Changes - 2024 February 25 Changelog

## Notable Features {#notable-features}

_0.24.0_ is mainly a maintenance release of QMK Firmware -- as per last few breaking changes cycles, there have been a lot of behind-the-scenes changes, mainly:

* continued purge of user keymaps
* migration of RGB matrix configuration into `info.json` files
* standardisation of `LAYOUT` naming
* keyboard relocations
* addressing technical debt

## Changes Requiring User Action {#changes-requiring-user-action}

### Windows Driver Changes ([QMK Toolbox 0.3.0 Release](https://github.com/qmk/qmk_toolbox/releases/tag/0.3.0))

Flashing keyboards that target `atmel-dfu` or `qmk-dfu` on Windows using `qmk flash` or QMK Toolbox have traditionally used _libusb_ for access to the DFU USB device. Since QMK Toolbox 0.3.0, this has changed to WinUSB.

If you update QMK Toolbox or update QMK MSYS, you may find that flashing Atmel DFU keyboards no longer functions as intended. If you strike such issues when flashing new firmware, you will need to replace the _libusb_ driver with _WinUSB_ using Zadig. You can follow the [Recovering from Installation to Wrong Device](../driver_installation_zadig#recovering-from-installation-to-wrong-device) instructions to replace the driver associated with the Atmel DFU bootloader, skipping the section about removal as Zadig will safely replace the driver instead. Please ensure your keyboard is in bootloader mode and has _libusb_ as the existing driver before attempting to use Zadig to replace the driver. If instead you see _HidUsb_ you're not in bootloader mode and should not continue with driver replacement.

### Updated Keyboard Codebases {#updated-keyboard-codebases}

One note with updated keyboard names -- historical keyboard names are still considered valid when using [External Userspace](../newbs_external_userspace) for builds. If you're already using External Userspace, you do not need to move your keymap inside your repository.

| Old Keyboard Name       | New Keyboard Name               |
|-------------------------|---------------------------------|
| enter67                 | kezewa/enter67                  |
| enter80                 | kezewa/enter80                  |
| epoch80                 | kbdfans/epoch80                 |
| eu_isolation            | p3d/eu_isolation                |
| flygone60/rev3          | shandoncodes/flygone60/rev3     |
| hub16                   | joshajohnson/hub16              |
| hub20                   | joshajohnson/hub20              |
| jm60                    | kbdfans/jm60                    |
| kira75                  | kira/kira75                     |
| kira80                  | kira/kira80                     |
| kmac                    | kbdmania/kmac                   |
| kmac_pad                | kbdmania/kmac_pad               |
| kudox/columner          | kumaokobo/kudox/columner        |
| kudox/rev1              | kumaokobo/kudox/rev1            |
| kudox/rev2              | kumaokobo/kudox/rev2            |
| kudox/rev3              | kumaokobo/kudox/rev3            |
| kudox_full/rev1         | kumaokobo/kudox_full/rev1       |
| kudox_game              | kumaokobo/kudox_game            |
| kudox_game/rev1         | kumaokobo/kudox_game/rev1       |
| kudox_game/rev2         | kumaokobo/kudox_game/rev2       |
| laser_ninja/pumpkin_pad | laser_ninja/pumpkinpad          |
| late9/rev1              | rookiebwoy/late9/rev1           |
| lefty                   | smoll/lefty                     |
| lefty/rev1              | smoll/lefty/rev1                |
| lefty/rev2              | smoll/lefty/rev2                |
| lpad                    | laneware/lpad                   |
| lw67                    | laneware/lw67                   |
| lw75                    | laneware/lw75                   |
| macro1                  | laneware/macro1                 |
| macro3                  | handwired/macro3                |
| miniaxe                 | kagizaraya/miniaxe              |
| mino/hotswap            | shandoncodes/mino/hotswap       |
| mino_plus/hotswap       | shandoncodes/mino_plus/hotswap  |
| mino_plus/soldered      | shandoncodes/mino_plus/soldered |
| mnk1800s                | monokei/mnk1800s                |
| mnk50                   | monokei/mnk50                   |
| mnk75                   | monokei/mnk75                   |
| moonlander              | zsa/moonlander                  |
| neopad/rev1             | rookiebwoy/neopad/rev1          |
| pico/65keys             | kumaokobo/pico/65keys           |
| pico/70keys             | kumaokobo/pico/70keys           |
| pw88                    | smoll/pw88                      |
| q4z                     | p3d/q4z                         |
| raindrop                | laneware/raindrop               |
| redox_w                 | redox/wireless                  |
| riot_pad                | shandoncodes/riot_pad           |
| spacey                  | p3d/spacey                      |
| synapse                 | p3d/synapse                     |
| tw40                    | p3d/tw40                        |
| w1_at                   | geonworks/w1_at                 |
| z12                     | zigotica/z12                    |
| z34                     | zigotica/z34                    |

## Notable core changes {#notable-core}

### Renaming Arduino-style GPIO pin functions ([#23085](https://github.com/qmk/qmk_firmware/pull/23085), [#23093](https://github.com/qmk/qmk_firmware/pull/23093)) {#gpio-rename}

QMK has long used Arduino-style GPIO naming conventions. This has been confusing for users, as over time they've had new variations added, as well as users mistakenly thinking that QMK supports the rest of the Arduino ecosystem.

The decision was made to rename the GPIO manipulation functions with ones matching QMK Firmware's code styling.

| Old                          | New                                   |
|------------------------------|---------------------------------------|
| `setPinInput(pin)`           | `gpio_set_pin_input(pin)`             |
| `setPinInputHigh(pin)`       | `gpio_set_pin_input_high(pin)`        |
| `setPinInputLow(pin)`        | `gpio_set_pin_input_low(pin)`         |
| `setPinOutput(pin)`          | `gpio_set_pin_output(pin)`            |
| `setPinOutputPushPull(pin)`  | `gpio_set_pin_output_push_pull(pin)`  |
| `setPinOutputOpenDrain(pin)` | `gpio_set_pin_output_open_drain(pin)` |
| `writePinHigh(pin)`          | `gpio_write_pin_high(pin)`            |
| `writePinLow(pin)`           | `gpio_write_pin_low(pin)`             |
| `writePin(pin, level)`       | `gpio_write_pin(pin, level)`          |
| `readPin(pin)`               | `gpio_read_pin(pin)`                  |
| `togglePin(pin)`             | `gpio_toggle_pin(pin)`                |

### I2C driver API Changes ([#22905](https://github.com/qmk/qmk_firmware/pull/22905))

Much like the GPIO refactoring, I2C APIs were also updated to conform to QMK naming standards. This is largely irrelevant to people using subsystem abstractions such as touchpads or RGB lighting, and only affects people manually communicating with other peripherals.

| Old API            | New API                  |
|--------------------|--------------------------|
| `i2c_readReg()`    | `i2c_read_register()`    |
| `i2c_readReg16()`  | `i2c_read_register16()`  |
| `i2c_writeReg()`   | `i2c_write_register()`   |
| `i2c_writeReg16()` | `i2c_write_register16()` |

### Renaming _Bootmagic Lite_ => _Bootmagic_ ([#22970](https://github.com/qmk/qmk_firmware/pull/22970), [#22979](https://github.com/qmk/qmk_firmware/pull/22979)) {#bootmagic-rename}

Bootmagic "Lite" had no real meaning once the historical Bootmagic "Full" was deprecated and removed. Any references to _Bootmagic Lite_ should now just refer to _Bootmagic_. We hope we got the majority of the code and the documentation, so if you find any more, let us know!

### Threshold for automatic mouse layer activation ([#21398](https://github.com/qmk/qmk_firmware/pull/21398)) {#auto-mouse-layer}

In some cases, accidental automatic activation of the mouse layer made it difficult to continue typing, such as when brushing across a trackball. `AUTO_MOUSE_THRESHOLD` is now a configurable option in `config.h` which allows for specifying what the movement threshold is before automatically activating the mouse layer.

### DIP Switch Mapping ([#22543](https://github.com/qmk/qmk_firmware/pull/22543)) {#dip-switch-map}

Much like Encoder Mapping, DIP Switch Mapping allows for specifying a table of actions to execute when a DIP switch state changes. See the [DIP Switch Documentation](../features/dip_switch#dip-switch-map) for more information.

```c
#if defined(DIP_SWITCH_MAP_ENABLE)
const uint16_t PROGMEM dip_switch_map[NUM_DIP_SWITCHES][NUM_DIP_STATES] = {
    DIP_SWITCH_OFF_ON(DF(0), DF(1)),
    DIP_SWITCH_OFF_ON(EC_NORM, EC_SWAP)
};
#endif
```

### Quantum Painter updates ([#18521](https://github.com/qmk/qmk_firmware/pull/18521), [#20645](https://github.com/qmk/qmk_firmware/pull/20645), [#22358](https://github.com/qmk/qmk_firmware/pull/22358)) {#qp-updates}

Quantum Painter picked up support for the following:

* ILI9486 displays
* SSD1306 displays, including smaller OLEDs
* Native panel pixel format support for fonts

Quantum Painter now supports the majority of common OLED panels supported by the basic OLED driver, so if you're using an ARM-based board you may find Quantum Painter a much more feature-rich API in comparison.

## Full changelist {#full-changelist}

Core:
* [Driver] ILI9486 on Quantum Painter ([#18521](https://github.com/qmk/qmk_firmware/pull/18521))
* Insert delay between shifted chars in send_string_with_delay ([#19280](https://github.com/qmk/qmk_firmware/pull/19280))
* [QP] Native palette support for fonts ([#20645](https://github.com/qmk/qmk_firmware/pull/20645))
* I2C driver cleanup ([#21273](https://github.com/qmk/qmk_firmware/pull/21273))
* Add option for auto mouse movement threshold ([#21398](https://github.com/qmk/qmk_firmware/pull/21398))
* Add Canadian French input locale ([#21456](https://github.com/qmk/qmk_firmware/pull/21456))
* Add encoder abstraction. ([#21548](https://github.com/qmk/qmk_firmware/pull/21548))
* Converted RGB matrix to use last_input_activity_elapsed(). ([#21687](https://github.com/qmk/qmk_firmware/pull/21687))
* Ignore space cadet key release when caps word is active ([#21721](https://github.com/qmk/qmk_firmware/pull/21721))
* Add OS detection callbacks ([#21777](https://github.com/qmk/qmk_firmware/pull/21777))
* joystick weights ([#21883](https://github.com/qmk/qmk_firmware/pull/21883))
* Add RGB matrix & LED Matrix support for IS31FL3729 ([#21944](https://github.com/qmk/qmk_firmware/pull/21944))
* dac_additive: Decouple the buffer length from the waveform length ([#22276](https://github.com/qmk/qmk_firmware/pull/22276))
* Add missing rgb matrix default parameters ([#22281](https://github.com/qmk/qmk_firmware/pull/22281))
* Remove console out endpoint ([#22304](https://github.com/qmk/qmk_firmware/pull/22304))
* Add ADC support STM32L4xx and STM32G4xx series MCUs ([#22341](https://github.com/qmk/qmk_firmware/pull/22341))
* Add QP support for smaller OLED displays and SSD1306 ([#22358](https://github.com/qmk/qmk_firmware/pull/22358))
* Add Imera converter ([#22419](https://github.com/qmk/qmk_firmware/pull/22419))
* LED drivers: refactor page selection ([#22518](https://github.com/qmk/qmk_firmware/pull/22518))
* Rework RGBLight driver system ([#22529](https://github.com/qmk/qmk_firmware/pull/22529))
* Add `APA102_LED_COUNT` define ([#22530](https://github.com/qmk/qmk_firmware/pull/22530))
* Add latam spanish headers ([#22542](https://github.com/qmk/qmk_firmware/pull/22542))
* Keymap introspection for Dip Switches ([#22543](https://github.com/qmk/qmk_firmware/pull/22543))
* Add basic presence check for cirque trackpad. ([#22546](https://github.com/qmk/qmk_firmware/pull/22546))
* Rename `RGBLED_NUM` -> `RGBLIGHT_LED_COUNT` ([#22570](https://github.com/qmk/qmk_firmware/pull/22570))
* LED drivers: change "TWI" to "I2C" ([#22617](https://github.com/qmk/qmk_firmware/pull/22617))
* LED drivers: extract IS31FL3742A from IS31COMMON ([#22620](https://github.com/qmk/qmk_firmware/pull/22620))
* Align Dip Switch feature ([#22625](https://github.com/qmk/qmk_firmware/pull/22625))
* LED/RGB Matrix: add header for drivers ([#22628](https://github.com/qmk/qmk_firmware/pull/22628))
* LED drivers: extract IS31FL3743A from IS31COMMON ([#22635](https://github.com/qmk/qmk_firmware/pull/22635))
* LED drivers: extract IS31FL3745 from IS31COMMON ([#22636](https://github.com/qmk/qmk_firmware/pull/22636))
* LED drivers: extract IS31FL3746A from IS31COMMON ([#22637](https://github.com/qmk/qmk_firmware/pull/22637))
* Update keyboard LED driver configs ([#22638](https://github.com/qmk/qmk_firmware/pull/22638))
* Solid reactive: improve fading effect ([#22656](https://github.com/qmk/qmk_firmware/pull/22656))
* Remove redundant RGB/LED matrix eeconfig init ([#22673](https://github.com/qmk/qmk_firmware/pull/22673))
* Remove redundant rgblight eeconfig init ([#22674](https://github.com/qmk/qmk_firmware/pull/22674))
* Remove redundant steno eeconfig init ([#22680](https://github.com/qmk/qmk_firmware/pull/22680))
* Rename `LED_DISABLE_WHEN_USB_SUSPENDED` -> `LED_MATRIX_SLEEP` ([#22681](https://github.com/qmk/qmk_firmware/pull/22681))
* Rename `RGB_DISABLE_WHEN_USB_SUSPENDED` -> `RGB_MATRIX_SLEEP` ([#22682](https://github.com/qmk/qmk_firmware/pull/22682))
* Align VUSB suspend protocol logic ([#22688](https://github.com/qmk/qmk_firmware/pull/22688))
* [Audio] Enable Complementary output for PWM Hardware driver ([#22726](https://github.com/qmk/qmk_firmware/pull/22726))
* Remove redundant audio eeconfig init ([#22736](https://github.com/qmk/qmk_firmware/pull/22736))
* Align location of tap dance keycode ([#22742](https://github.com/qmk/qmk_firmware/pull/22742))
* Align `SPLIT_HAND_MATRIX_GRID` left/right logic with `SPLIT_HAND_PIN` ([#22775](https://github.com/qmk/qmk_firmware/pull/22775))
* [CI] Regenerate Files ([#22795](https://github.com/qmk/qmk_firmware/pull/22795))
* Remove IS31FLCOMMON code ([#22800](https://github.com/qmk/qmk_firmware/pull/22800))
* Cirque reachable calibration aide ([#22803](https://github.com/qmk/qmk_firmware/pull/22803))
* LED drivers: rename "simple" to "mono" ([#22814](https://github.com/qmk/qmk_firmware/pull/22814))
* is31fl3733: change `write_register()` return type to `void` ([#22824](https://github.com/qmk/qmk_firmware/pull/22824))
* snled27351: change `write_register()` return type to `void` ([#22825](https://github.com/qmk/qmk_firmware/pull/22825))
* apa102: cleanups ([#22826](https://github.com/qmk/qmk_firmware/pull/22826))
* Remove PWM advanced check for WS2812 driver ([#22830](https://github.com/qmk/qmk_firmware/pull/22830))
* Allow ChibiOS `SIO` driver for `UART` driver ([#22839](https://github.com/qmk/qmk_firmware/pull/22839))
* LED drivers: more formatting ([#22865](https://github.com/qmk/qmk_firmware/pull/22865))
* LED drivers: change `write_pwm_buffer()` return type to `void` ([#22869](https://github.com/qmk/qmk_firmware/pull/22869))
* [CI] Regenerate Files ([#22872](https://github.com/qmk/qmk_firmware/pull/22872))
* LED drivers: switch to i2c_writeReg() ([#22878](https://github.com/qmk/qmk_firmware/pull/22878))
* LED drivers: remove `write_pwm_buffer()` from public API ([#22884](https://github.com/qmk/qmk_firmware/pull/22884))
* i2c: rename read/write register functions ([#22905](https://github.com/qmk/qmk_firmware/pull/22905))
* LED drivers: update I2C API usage ([#22951](https://github.com/qmk/qmk_firmware/pull/22951))
* LED drivers: create structs to hold PWM/scaling buffers ([#22955](https://github.com/qmk/qmk_firmware/pull/22955))
* Migrate and remove deprecated debug utils ([#22961](https://github.com/qmk/qmk_firmware/pull/22961))
* Remove call to removed i2c function in azoteq driver ([#22966](https://github.com/qmk/qmk_firmware/pull/22966))
* Tidy up print/debug logging headers ([#22969](https://github.com/qmk/qmk_firmware/pull/22969))
* Begin removal of bootmagic lite terminology ([#22970](https://github.com/qmk/qmk_firmware/pull/22970))
* LED drivers: place I2C addresses into an array ([#22975](https://github.com/qmk/qmk_firmware/pull/22975))
* Removal of bootmagic lite terminology ([#22979](https://github.com/qmk/qmk_firmware/pull/22979))
* Init pins for Analog Joystick sensor ([#22985](https://github.com/qmk/qmk_firmware/pull/22985))
* Workaround for G431 eeprom emulation ([#23002](https://github.com/qmk/qmk_firmware/pull/23002))
* is31fl3741: split PWM and scaling buffers ([#23049](https://github.com/qmk/qmk_firmware/pull/23049))
* LED drivers: update PWM register defines for `g_<driver>_leds` ([#23052](https://github.com/qmk/qmk_firmware/pull/23052))
* LED drivers: add support for shutdown pin ([#23058](https://github.com/qmk/qmk_firmware/pull/23058))
* AW20216S: combine EN pin defines ([#23067](https://github.com/qmk/qmk_firmware/pull/23067))
* Update naming convention for GPIO control macros ([#23085](https://github.com/qmk/qmk_firmware/pull/23085))
* Update GPIO macro usages in core ([#23093](https://github.com/qmk/qmk_firmware/pull/23093))
* OS Detection - Entire file should not be wrapped with ifdef ([#23108](https://github.com/qmk/qmk_firmware/pull/23108))
* IS31FL3729 updates ([#23109](https://github.com/qmk/qmk_firmware/pull/23109))
* Nix shell updates (Nixpkgs 2024-02-23, QMK CLI 1.1.5) ([#23143](https://github.com/qmk/qmk_firmware/pull/23143))

CLI:
* [Refactor] `qmk find` ([#21096](https://github.com/qmk/qmk_firmware/pull/21096))
* [Refactor] Break `QGFImageFile`'s `_save` function into smaller pieces ([#21124](https://github.com/qmk/qmk_firmware/pull/21124))
* [Enhancement] Prepare for `SyntaxWarning` ([#22562](https://github.com/qmk/qmk_firmware/pull/22562))
* Flag invalid keyboard features during lint ([#22832](https://github.com/qmk/qmk_firmware/pull/22832))

Submodule updates:
* chore(chibios-contrib): sync with chibios-21.11.x ([#22560](https://github.com/qmk/qmk_firmware/pull/22560))

Keyboards:
* Move `redox_w` into `redox` ([#21448](https://github.com/qmk/qmk_firmware/pull/21448))
* null ST110R2.1 (SaikouType) basic support with layouts ([#21623](https://github.com/qmk/qmk_firmware/pull/21623))
* New keyboard addition: Orthograph ([#21770](https://github.com/qmk/qmk_firmware/pull/21770))
* Add Olly JF Rev.2 ([#21775](https://github.com/qmk/qmk_firmware/pull/21775))
* Cleanup Satisfaction75 Firmware and add new revisions ([#22082](https://github.com/qmk/qmk_firmware/pull/22082))
* Migrate dynamic_keymap.layer_count < 4 where requried ([#22091](https://github.com/qmk/qmk_firmware/pull/22091))
* Bastard Keyboards: Add support for Dilemma v2 (3x5+3) ([#22185](https://github.com/qmk/qmk_firmware/pull/22185))
* Karn: correct layout data ([#22201](https://github.com/qmk/qmk_firmware/pull/22201))
* zk3mod : added OLED ([#22303](https://github.com/qmk/qmk_firmware/pull/22303))
* Adds support for the Iron180 V2 PCBs ([#22314](https://github.com/qmk/qmk_firmware/pull/22314))
* Add 5x13 and 6x13 ortho community layouts ([#22315](https://github.com/qmk/qmk_firmware/pull/22315))
* Cipulot refactoring ([#22368](https://github.com/qmk/qmk_firmware/pull/22368))
* Remove era/klein ([#22384](https://github.com/qmk/qmk_firmware/pull/22384))
* consolidate firmware folder in smoll parent folder ([#22401](https://github.com/qmk/qmk_firmware/pull/22401))
* `keycapsss/plaid_pad`: switch to encoder map ([#22474](https://github.com/qmk/qmk_firmware/pull/22474))
* Add EE-AT and move W1-AT under geonworks ([#22526](https://github.com/qmk/qmk_firmware/pull/22526))
* refactor: projectcain/vault35 ([#22558](https://github.com/qmk/qmk_firmware/pull/22558))
* Update Q5 ([#22575](https://github.com/qmk/qmk_firmware/pull/22575))
* Update Q7 ([#22577](https://github.com/qmk/qmk_firmware/pull/22577))
* Update Q8 ([#22578](https://github.com/qmk/qmk_firmware/pull/22578))
* Update Q9 ([#22579](https://github.com/qmk/qmk_firmware/pull/22579))
* Remove "empty" files ([#22603](https://github.com/qmk/qmk_firmware/pull/22603))
* Rename Pumpkin Pad to Pumkinpad ([#22651](https://github.com/qmk/qmk_firmware/pull/22651))
* Noodlepad Additions and Updates ([#22701](https://github.com/qmk/qmk_firmware/pull/22701))
* Refactor: move miniaxe into kagizaraya ([#22708](https://github.com/qmk/qmk_firmware/pull/22708))
* Refactor: move keyboards into zigotica folder ([#22709](https://github.com/qmk/qmk_firmware/pull/22709))
* Refactor: move keyboards into laneware folder ([#22710](https://github.com/qmk/qmk_firmware/pull/22710))
* Refactor: move keyboards into kezewa ([#22712](https://github.com/qmk/qmk_firmware/pull/22712))
* Refactor: move keyboards into kbdmania folder ([#22714](https://github.com/qmk/qmk_firmware/pull/22714))
* Refactor: move keyboards into monokei folder ([#22715](https://github.com/qmk/qmk_firmware/pull/22715))
* Refactor: move keyboards into kumaokobo ([#22719](https://github.com/qmk/qmk_firmware/pull/22719))
* Updating NCC1701KB and adding via support ([#22721](https://github.com/qmk/qmk_firmware/pull/22721))
* Move Moonlander to ZSA folder ([#22740](https://github.com/qmk/qmk_firmware/pull/22740))
* Refactor: group shandoncodes keyboards ([#22743](https://github.com/qmk/qmk_firmware/pull/22743))
* Refactor: group rookiebwoy keyboards ([#22745](https://github.com/qmk/qmk_firmware/pull/22745))
* Default folder correction for kumaokobo ([#22750](https://github.com/qmk/qmk_firmware/pull/22750))
* Default folder correction for rookiebwoy ([#22753](https://github.com/qmk/qmk_firmware/pull/22753))
* Refactor: move macro3 into handwired folder ([#22759](https://github.com/qmk/qmk_firmware/pull/22759))
* Refactor: group kira keyboards ([#22760](https://github.com/qmk/qmk_firmware/pull/22760))
* Refactor: group hub keyboards ([#22762](https://github.com/qmk/qmk_firmware/pull/22762))
* Refactor: move p3d keyboards ([#22763](https://github.com/qmk/qmk_firmware/pull/22763))
* Refactor: group kbdfans keyboards ([#22764](https://github.com/qmk/qmk_firmware/pull/22764))
* Remove incorrect use of WS2812_PIO_USE_PIO1 ([#22771](https://github.com/qmk/qmk_firmware/pull/22771))
* Migrate LED Matrix config to info.json ([#22792](https://github.com/qmk/qmk_firmware/pull/22792))
* Migrate RGB Matrix config to info.json - [0-9] ([#22797](https://github.com/qmk/qmk_firmware/pull/22797))
* Migrate RGB Matrix config to info.json - A ([#22798](https://github.com/qmk/qmk_firmware/pull/22798))
* Late9 keymaps update, added VIA support ([#22801](https://github.com/qmk/qmk_firmware/pull/22801))
* Migrate RGB Matrix config to info.json - B ([#22806](https://github.com/qmk/qmk_firmware/pull/22806))
* Migrate RGB Matrix config to info.json - C ([#22807](https://github.com/qmk/qmk_firmware/pull/22807))
* Migrate RGB Matrix config to info.json - EF ([#22808](https://github.com/qmk/qmk_firmware/pull/22808))
* Migrate RGB Matrix config to info.json - D ([#22811](https://github.com/qmk/qmk_firmware/pull/22811))
* H87g2 updates ([#22819](https://github.com/qmk/qmk_firmware/pull/22819))
* WT boards: extract `g_is31fl3736_leds` from wt_mono_backlight ([#22823](https://github.com/qmk/qmk_firmware/pull/22823))
* Migrate RGB Matrix config to info.json - G ([#22859](https://github.com/qmk/qmk_firmware/pull/22859))
* Use existing columns for 3x5 layout ([#22860](https://github.com/qmk/qmk_firmware/pull/22860))
* Migrate RGB Matrix config to info.json - H ([#22861](https://github.com/qmk/qmk_firmware/pull/22861))
* Migrate RGB Matrix config to info.json - J ([#22862](https://github.com/qmk/qmk_firmware/pull/22862))
* Migrate RGB Matrix config to info.json - I ([#22863](https://github.com/qmk/qmk_firmware/pull/22863))
* Migrate RGB Matrix config to info.json - L ([#22864](https://github.com/qmk/qmk_firmware/pull/22864))
* Migrate RGB Matrix config to info.json - NOPQ ([#22866](https://github.com/qmk/qmk_firmware/pull/22866))
* Migrate RGB Matrix config to info.json - XZY ([#22879](https://github.com/qmk/qmk_firmware/pull/22879))
* Zed65/no_backlight/cor65 correct data layout ([#22898](https://github.com/qmk/qmk_firmware/pull/22898))
* Migrate RGB Matrix config to info.json - M ([#22908](https://github.com/qmk/qmk_firmware/pull/22908))
* Migrate RGB Matrix config to info.json - RS ([#22909](https://github.com/qmk/qmk_firmware/pull/22909))
* Migrate RGB Matrix config to info.json - TUVW ([#22910](https://github.com/qmk/qmk_firmware/pull/22910))
* Migrate RGB Matrix config to info.json - K ([#22911](https://github.com/qmk/qmk_firmware/pull/22911))
* Remove `LAYOUTS_HAS_RGB` ([#22917](https://github.com/qmk/qmk_firmware/pull/22917))
* Migrate lighting defaults to info.json ([#22920](https://github.com/qmk/qmk_firmware/pull/22920))
* Ensure LTO is enabled as a `info.json` build config option ([#22932](https://github.com/qmk/qmk_firmware/pull/22932))
* refactor(keyboard): quokka ([#22942](https://github.com/qmk/qmk_firmware/pull/22942))
* Sango Keyboard ([#22971](https://github.com/qmk/qmk_firmware/pull/22971))
* Add FS streampad ([#22991](https://github.com/qmk/qmk_firmware/pull/22991))
* Remove always enabled effects from lighting animation list ([#22992](https://github.com/qmk/qmk_firmware/pull/22992))
* Migrate RGB Matrix config to info.json - keychron ([#22998](https://github.com/qmk/qmk_firmware/pull/22998))
* Migrate RGB Matrix config to info.json - Misc ([#23000](https://github.com/qmk/qmk_firmware/pull/23000))
* Remove ee_hands config from ferris/sweep firmware ([#23029](https://github.com/qmk/qmk_firmware/pull/23029))
* Migrate dip switch config to info.json - keychron ([#23037](https://github.com/qmk/qmk_firmware/pull/23037))
* [unicorne] Add a layout alias ([#23056](https://github.com/qmk/qmk_firmware/pull/23056))
* nacly/sodium62: Update vid, pid, and add via keymap ([#23063](https://github.com/qmk/qmk_firmware/pull/23063))
* LED drivers: update keyboard LED configs ([#23073](https://github.com/qmk/qmk_firmware/pull/23073))
* Remove invalid keyboard level features ([#23074](https://github.com/qmk/qmk_firmware/pull/23074))
* Migrate WEAR_LEVELING_* to info.json ([#23077](https://github.com/qmk/qmk_firmware/pull/23077))
* [Keymap Removal] keyboard with most keymaps ([#23081](https://github.com/qmk/qmk_firmware/pull/23081))
* Remove obvious user keymaps, keyboards/{v,x,y,z}* edition. ([#23083](https://github.com/qmk/qmk_firmware/pull/23083))
* Remove obvious user keymaps, keyboards/{s,t}* edition. ([#23084](https://github.com/qmk/qmk_firmware/pull/23084))
* [Keymap Removal] keyboard with most keymaps ([#23092](https://github.com/qmk/qmk_firmware/pull/23092))
* Fiuxup takashicompany/heavy_left ([#23094](https://github.com/qmk/qmk_firmware/pull/23094))
* Remove obvious user keymaps, keyboards/{i,j,k}* edition ([#23102](https://github.com/qmk/qmk_firmware/pull/23102))
* Manual user keymap removal ([#23104](https://github.com/qmk/qmk_firmware/pull/23104))
* Manual user keymap removal ([#23119](https://github.com/qmk/qmk_firmware/pull/23119))
* Migrate `RGBLED_NUM` -> `RGBLIGHT_LED_COUNT` in remaining non-user keymaps ([#23128](https://github.com/qmk/qmk_firmware/pull/23128))

Keyboard fixes:
* Fix VID and PID for AnnePro2 ([#22263](https://github.com/qmk/qmk_firmware/pull/22263))
* fix(kikoslab/kl90): Fix firmware to support encoder knobs properly ([#22649](https://github.com/qmk/qmk_firmware/pull/22649))
* fix: improper usage of keyboard/user-level functions ([#22652](https://github.com/qmk/qmk_firmware/pull/22652))
* Temporary fix for mechlovin/olly/octagon ([#22796](https://github.com/qmk/qmk_firmware/pull/22796))
* Keychron Q11 usb poweron fix ([#22799](https://github.com/qmk/qmk_firmware/pull/22799))
* capsunlocked/cu80/v2: Fix invalid RGB matrix config ([#22873](https://github.com/qmk/qmk_firmware/pull/22873))
* Fix typo in Redox config ([#22899](https://github.com/qmk/qmk_firmware/pull/22899))
* Fixup doio/kb16 ([#22921](https://github.com/qmk/qmk_firmware/pull/22921))
* Fixup takashicompany/minizone ([#22922](https://github.com/qmk/qmk_firmware/pull/22922))
* Fixup sofle ([#22934](https://github.com/qmk/qmk_firmware/pull/22934))
* Fix Issue with RGB Matrix not understanding the split keyboard ([#22997](https://github.com/qmk/qmk_firmware/pull/22997))
* Fixup sawnsprojects/krush60 ([#23095](https://github.com/qmk/qmk_firmware/pull/23095))
* Fixup kbd67/rev1 ([#23096](https://github.com/qmk/qmk_firmware/pull/23096))
* Fixup boardsource/equals ([#23106](https://github.com/qmk/qmk_firmware/pull/23106))
* Fixup inett_studio/sq80 ([#23121](https://github.com/qmk/qmk_firmware/pull/23121))
* Add LED/RGB Matrix drivers to info.json schema ([#23127](https://github.com/qmk/qmk_firmware/pull/23127))
* Fix for multiple AMUX usage ([#23155](https://github.com/qmk/qmk_firmware/pull/23155))

Bugs:
* MIDI sustain effect fix on qmk 0.22.2 ([#22114](https://github.com/qmk/qmk_firmware/pull/22114))
* Prevent `qmk migrate` processing unparsed info.json values ([#22374](https://github.com/qmk/qmk_firmware/pull/22374))
* Remove redundant backlight eeconfig init ([#22675](https://github.com/qmk/qmk_firmware/pull/22675))
* pointing_device ifdef indentation fix ([#22802](https://github.com/qmk/qmk_firmware/pull/22802))
* Ensure LED config is extracted when feature is disabled ([#22809](https://github.com/qmk/qmk_firmware/pull/22809))
* Generate true/false for _DEFAULT_ON options ([#22829](https://github.com/qmk/qmk_firmware/pull/22829))
* is31fl3733: fix driver sync backwards compatibility defines ([#22851](https://github.com/qmk/qmk_firmware/pull/22851))
* LED drivers: misc formatting and typos ([#22857](https://github.com/qmk/qmk_firmware/pull/22857))
* Allow generation of both LED and RGB Matrix config ([#22896](https://github.com/qmk/qmk_firmware/pull/22896))
* LED drivers: remove PWM register offsets ([#22897](https://github.com/qmk/qmk_firmware/pull/22897))
* `qmk format-json`: Force Unix line endings and ensure LF at EOF ([#22901](https://github.com/qmk/qmk_firmware/pull/22901))
* Fix cirque connected check ([#22948](https://github.com/qmk/qmk_firmware/pull/22948))
* Fix joystick initialization ([#22953](https://github.com/qmk/qmk_firmware/pull/22953))
* Workaround for `make test:all DEBUG=1` ([#23047](https://github.com/qmk/qmk_firmware/pull/23047))
* Fix unit test execution ([#23048](https://github.com/qmk/qmk_firmware/pull/23048))
* Fix git-submodule running in wrong location ([#23059](https://github.com/qmk/qmk_firmware/pull/23059))
* WS2812 bitbang: prefix for `NOP_FUDGE` define ([#23110](https://github.com/qmk/qmk_firmware/pull/23110))
* Fix make clean test:os_detection ([#23112](https://github.com/qmk/qmk_firmware/pull/23112))
* Fix pmw33xx sensor corruption on get-cpi call ([#23116](https://github.com/qmk/qmk_firmware/pull/23116))
* Ensure `qmk generate-compilation-database` copies to userspace as well. ([#23129](https://github.com/qmk/qmk_firmware/pull/23129))
