# QMK Breaking Changes - 2023 February 26 Changelog

## Changes Requiring User Action {#changes-requiring-user-action}

### `IGNORE_MOD_TAP_INTERRUPT` behaviour changes ([#15741](https://github.com/qmk/qmk_firmware/pull/15741)) {#i-m-t-i}

`IGNORE_MOD_TAP_INTERRUPT_PER_KEY` has been removed and `IGNORE_MOD_TAP_INTERRUPT` deprecated as a stepping stone towards making `IGNORE_MOD_TAP_INTERRUPT` the new default behavior for mod-taps in the future.

In place of the now removed `IGNORE_MOD_TAP_INTERRUPT_PER_KEY`, one must use the pre-existing `HOLD_ON_OTHER_KEY_PRESS` option.

In most cases, updating `get_ignore_mod_tap_interrupt` to `get_hold_on_other_key_press` is simply a matter of renaming the function and swapping every `true` by `false` and vice versa. The one subtlety you may need to look out for is that the `get_ignore_mod_tap_interrupt` was only ever called with mod-taps passed in as the `keycode` argument, while the `keycode` argument of `get_hold_on_other_key_press` can be any dual-role key. This includes not only mod-taps, but also layer-taps, one shot keys, `TT(layer)` and more. This has an impact on the effect of the `default` case in a typical per-key configuration making use of a `switch(keycode)` statement.

To illustrate, let's take the example of a configuration where we'd want all mod-taps to activate the modifier if another key is pressed while held with the exception of `LCTL_T(KC_A)`, which should ignore keys pressed while it is held and activate the modifier only if it has been held for longer than the tapping term. In addition, we would like to keep the default "ignore-interrupt" behavior of layer taps.

An old way to do this would be via the following code:

```c
bool get_ignore_mod_tap_interrupt(uint16_t keycode, keyrecord_t *record) {
    switch(keycode) {
        case LCTL_T(KC_A):
            return true;
        default:
            return false;
    }
}
```

The correct way to update this code without accidentally changing how the layer-taps work would be the following:

```c
bool get_hold_on_other_key_press(uint16_t keycode, keyrecord_t *record) {
    switch(keycode) {
        // Capture all mod-tap keycodes.
        case QK_MOD_TAP ... QK_MOD_TAP_MAX:
            if (keycode == LCTL_T(KC_A)) {
                // Disable HOLD_ON_OTHER_KEY_PRESS for LCTL_T(KC_A)
                // aka enable IGNORE_MOD_TAP_INTERRUPT for LCTL_T(KC_A).
                return false;
            } else {
                // Enable HOLD_ON_OTHER_KEY_PRESS for every other mod-tap keycode.
                return true;
            }
        default:
            return false;
    }
}
```

For more information, you are invited to read the sections on [IGNORE_MOD_TAP_INTERRUPT](../tap_hold#ignore-mod-tap-interrupt) and [HOLD_ON_OTHER_KEY_PRESS](../tap_hold#hold-on-other-key-press) in the page on [Tap-Hold configuration options](../tap_hold).

### `TAPPING_FORCE_HOLD` => `QUICK_TAP_TERM` ([#17007](https://github.com/qmk/qmk_firmware/pull/17007)) {#quick-tap-term}

`TAPPING_FORCE_HOLD` feature is now replaced by `QUICK_TAP_TERM`. Instead of turning off auto-repeat completely, user will have the option to configure a `QUICK_TAP_TERM` in milliseconds. When the user holds a tap-hold key after tapping it within `QUICK_TAP_TERM`, QMK will send the tap keycode to the host, enabling auto-repeat.

Its value is set to `TAPPING_TERM` by default and it can be reduced to match typing habits to avoid false triggers. To disable auto-repeat completely, set `QUICK_TAP_TERM` to zero.

`TAPPING_FORCE_HOLD_PER_KEY` is also deprecated and replaced by `QUICK_TAP_TERM_PER_KEY`. The old granular control function for tapping force hold is:

```c
bool get_tapping_force_hold(uint16_t keycode, keyrecord_t *record) {
    switch (keycode) {
        case LT(1, KC_BSPC):
            return true;
        default:
            return false;
    }
}
```

That function can be replaced with:

```c
uint16_t get_quick_tap_term(uint16_t keycode, keyrecord_t *record) {
    switch (keycode) {
        case SFT_T(KC_SPC):
            return 0;
        default:
            return QUICK_TAP_TERM;
    }
}
```

For more details, please read the updated documentation section on [Quick Tap Term](../tap_hold#quick-tap-term).

### Leader Key Rework {#leader-key-rework ([#19632](https://github.com/qmk/qmk_firmware/pull/19632))}

The Leader Key feature API has been significantly improved, along with some bugfixes and added tests.

Instead of defining your leader sequences in `matrix_scan_user()`, they are now handled in the `leader_end_user()` callback, and the `LEADER_EXTERNS()`/`LEADER_DICTIONARY()` macros are no longer needed:

```c
void leader_end_user(void) {
    if (leader_sequence_one_key(KC_F)) {
        // Leader, f => Types the below string
        SEND_STRING("QMK is awesome.");
    } else if (leader_sequence_two_keys(KC_D, KC_D)) {
        // Leader, d, d => Ctrl+A, Ctrl+C
        SEND_STRING(SS_LCTL("a") SS_LCTL("c"));
    } else if (leader_sequence_three_keys(KC_D, KC_D, KC_S)) {
        // Leader, d, d, s => Types the below string
        SEND_STRING("https://start.duckduckgo.com\n");
    } else if (leader_sequence_two_keys(KC_A, KC_S)) {
        // Leader, a, s => GUI+S
        tap_code16(LGUI(KC_S));
    }
}
```

For more information please see the [Leader Key documentation](../features/leader_key).

### Updated Keyboard Codebases {#updated-keyboard-codebases}

The following keyboards have had their source moved within QMK:

| Old Keyboard Name           | New Keyboard Name        |
|-----------------------------|--------------------------|
| ramonimbao/aelith           |  rmi_kb/aelith           |
| ramonimbao/herringbone/pro  |  rmi_kb/herringbone/pro  |
| ramonimbao/herringbone/v1   |  rmi_kb/herringbone/v1   |
| ramonimbao/mona/v1_1        |  rmi_kb/mona/v1_1        |
| ramonimbao/mona/v1          |  rmi_kb/mona/v1          |
| ramonimbao/mona/v32a        |  rmi_kb/mona/v32a        |
| ramonimbao/squishy65        |  rmi_kb/squishy65        |
| ramonimbao/squishytkl       |  rmi_kb/squishytkl       |
| ramonimbao/tkl_ff           |  rmi_kb/tkl_ff           |
| ramonimbao/tkl_ff/v1        |  rmi_kb/tkl_ff/v1        |
| ramonimbao/tkl_ff/v2        |  rmi_kb/tkl_ff/v2        |
| ramonimbao/wete/v1          |  rmi_kb/wete/v1          |
| ramonimbao/wete/v2          |  rmi_kb/wete/v2          |
| the_uni                     |  stenothe_uni            |
| xelus/xs60                  |  xelus/xs60/soldered     |

## Notable core changes {#notable-core}

As per last breaking changes cycle, there has been _a lot_ of emphasis on behind-the-scenes changes, mainly around consolidation of core subsystems and constant values, as well as addressing tech debt. Whilst not outwardly visible, this cleanup and refactoring should start paying dividends as it simplifies future development and maintenance.

A handful of examples:

* Standardised the lower/raise/adjust layer change pattern with explicit keycodes and configurable target layers
* Cleaned up a lot of Makefile logic to simplify and speed up builds
* Automated tooling to regenerate keycode values has been hooked into the PR pipeline and will trigger failures if they're incorrect
* Many more configuration options have moved into `info.json`, such as backlight, encoders
* Additional unit tests to ensure keycode behaviours don't accidentally change

## Full changelist {#full-changelist}

Core:
* Remove IGNORE_MOD_TAP_INTERRUPT_PER_KEY in favour of HOLD_ON_OTHER_KEY_PRESS_PER_KEY ([#15741](https://github.com/qmk/qmk_firmware/pull/15741))
* Add combo hook to allow per layer combo reference layers. ([#16699](https://github.com/qmk/qmk_firmware/pull/16699))
* Replace Tapping Force Hold feature with Quick Tap Term ([#17007](https://github.com/qmk/qmk_firmware/pull/17007))
* [Test] Reset timer for every unit test and provide timestamps for log messages ([#17028](https://github.com/qmk/qmk_firmware/pull/17028))
* Bug17281 - Retain momentary layers until the end of tapping ([#17282](https://github.com/qmk/qmk_firmware/pull/17282))
* Detect host OS based on USB fingerprint ([#18463](https://github.com/qmk/qmk_firmware/pull/18463))
* allow locking the matrix state ([#18852](https://github.com/qmk/qmk_firmware/pull/18852))
* Initial DD keymap_extras migration ([#19031](https://github.com/qmk/qmk_firmware/pull/19031))
* Support inverted scan logic for optical switches ([#19053](https://github.com/qmk/qmk_firmware/pull/19053))
* Corrections to uart driver for Chibios platform ([#19075](https://github.com/qmk/qmk_firmware/pull/19075))
* Remaining DD keymap_extras migration ([#19110](https://github.com/qmk/qmk_firmware/pull/19110))
* Add udev rule for the WB32 DFU bootloader ([#19135](https://github.com/qmk/qmk_firmware/pull/19135))
* Add Michi MCU Converter support ([#19163](https://github.com/qmk/qmk_firmware/pull/19163))
* Add Split support for Haptic feedback ([#19203](https://github.com/qmk/qmk_firmware/pull/19203))
* Allow mod-tap hold action on one shot layer ([#19214](https://github.com/qmk/qmk_firmware/pull/19214))
* Remove RGBLIGHT_ANIMATIONS from core (+cleanup) ([#19216](https://github.com/qmk/qmk_firmware/pull/19216))
* Revert WB32 ISO workaround ([#19224](https://github.com/qmk/qmk_firmware/pull/19224))
* Prevent dynamic keymaps from processing layers that don't exist ([#19225](https://github.com/qmk/qmk_firmware/pull/19225))
* Add `*_RIGHT` configuration for PMW33XX driver ([#19243](https://github.com/qmk/qmk_firmware/pull/19243))
* Remove deprecated led_set_kb ([#19273](https://github.com/qmk/qmk_firmware/pull/19273))
* Tests that caps word stays active after use of OSL ([#19303](https://github.com/qmk/qmk_firmware/pull/19303))
* Allow overriding of keymap/encodermap layer count. ([#19325](https://github.com/qmk/qmk_firmware/pull/19325))
* guard action related debug messages ([#19348](https://github.com/qmk/qmk_firmware/pull/19348))
* use `IS_EVENT` macro instead of `!IS_NOEVENT` ([#19366](https://github.com/qmk/qmk_firmware/pull/19366))
* [Test] Introduce VERIFY_AND_CLEAR shorthand ([#19370](https://github.com/qmk/qmk_firmware/pull/19370))
* Add RGB565 and RGB888 color support to Quantum Painter ([#19382](https://github.com/qmk/qmk_firmware/pull/19382))
* Initial DD keycode regen workflow ([#19400](https://github.com/qmk/qmk_firmware/pull/19400))
* Update RGB matrix reactive gradient timer scale ([#19415](https://github.com/qmk/qmk_firmware/pull/19415))
* De-obfuscate random8 functions ([#19416](https://github.com/qmk/qmk_firmware/pull/19416))
* Use random8 for jellybean effect ([#19418](https://github.com/qmk/qmk_firmware/pull/19418))
* Align definition of unicode_map ([#19452](https://github.com/qmk/qmk_firmware/pull/19452))
* Add analog support for RP2040 ([#19453](https://github.com/qmk/qmk_firmware/pull/19453))
* [CI] Regenerate Files ([#19463](https://github.com/qmk/qmk_firmware/pull/19463))
* Build warning when not valid work-tree ([#19475](https://github.com/qmk/qmk_firmware/pull/19475))
* Migrate 'make git-submodule' to CLI command ([#19479](https://github.com/qmk/qmk_firmware/pull/19479))
* Remove cmp checks from Makefile ([#19480](https://github.com/qmk/qmk_firmware/pull/19480))
* Replace list_keyboards.sh with CLI calls ([#19485](https://github.com/qmk/qmk_firmware/pull/19485))
* Remove unused Makefile paths ([#19487](https://github.com/qmk/qmk_firmware/pull/19487))
* Migrate submodule dirty check to CLI ([#19488](https://github.com/qmk/qmk_firmware/pull/19488))
* Remove `make all-<platform>` build targets ([#19496](https://github.com/qmk/qmk_firmware/pull/19496))
* Relax converter validation within keymap schema ([#19544](https://github.com/qmk/qmk_firmware/pull/19544))
* De-duplicate platform detection ([#19545](https://github.com/qmk/qmk_firmware/pull/19545))
* Add alias support for converters ([#19563](https://github.com/qmk/qmk_firmware/pull/19563))
* Revert "De-duplicate platform detection" ([#19564](https://github.com/qmk/qmk_firmware/pull/19564))
* Add mmoskal/uf2-stm32f103 bootloader support ([#19594](https://github.com/qmk/qmk_firmware/pull/19594))
* usb_main.c: remove `CH_KERNEL_MAJOR` check ([#19597](https://github.com/qmk/qmk_firmware/pull/19597))
* Use the correct keycode when updating WPM ([#19599](https://github.com/qmk/qmk_firmware/pull/19599))
* De-duplicate platform detection ([#19603](https://github.com/qmk/qmk_firmware/pull/19603))
* Refactor rain pixel function ([#19606](https://github.com/qmk/qmk_firmware/pull/19606))
* ChibiOS: Consolidate report sending code ([#19607](https://github.com/qmk/qmk_firmware/pull/19607))
* Add f303 to tinyuf2 bootloader support ([#19620](https://github.com/qmk/qmk_firmware/pull/19620))
* Refactor Leader key feature ([#19632](https://github.com/qmk/qmk_firmware/pull/19632))
* Split out mcu_selection to platform ([#19701](https://github.com/qmk/qmk_firmware/pull/19701))
* Move MIDI code out of tmk_core ([#19704](https://github.com/qmk/qmk_firmware/pull/19704))
* Remove deprecated Quantum keycodes ([#19712](https://github.com/qmk/qmk_firmware/pull/19712))
* QP: Correct rotation and offset when using LVGL ([#19713](https://github.com/qmk/qmk_firmware/pull/19713))
* Remove usages of config_common.h from config.h files. ([#19714](https://github.com/qmk/qmk_firmware/pull/19714))
* Relocate diode direction definitions ([#19715](https://github.com/qmk/qmk_firmware/pull/19715))
* Normalise Swap Hands keycodes ([#19720](https://github.com/qmk/qmk_firmware/pull/19720))
* Strip out more of config_common ([#19722](https://github.com/qmk/qmk_firmware/pull/19722))
* Remove `IS_HOST_LED_ON` and migrate usages ([#19753](https://github.com/qmk/qmk_firmware/pull/19753))
* Move more unicode ranges to DD ([#19755](https://github.com/qmk/qmk_firmware/pull/19755))
* Tidy up use of keycode range helpers ([#19756](https://github.com/qmk/qmk_firmware/pull/19756))
* Tri Layer Keys ([#19795](https://github.com/qmk/qmk_firmware/pull/19795))
* Remove matrix_init_quantum/matrix_scan_quantum ([#19806](https://github.com/qmk/qmk_firmware/pull/19806))
* Tidy up use of keycode range helpers ([#19813](https://github.com/qmk/qmk_firmware/pull/19813))
* Remove `config.h` include from quantum files ([#19817](https://github.com/qmk/qmk_firmware/pull/19817))
* Add rp2040_ce and add elite-pi and helios as alias ([#19830](https://github.com/qmk/qmk_firmware/pull/19830))
* Add swap hands status function ([#19831](https://github.com/qmk/qmk_firmware/pull/19831))
* Align sequencer keycodes ([#19875](https://github.com/qmk/qmk_firmware/pull/19875))
* Align magic keycodes ([#19877](https://github.com/qmk/qmk_firmware/pull/19877))
* Move `KC_MISSION_CONTROL`/`KC_LAUNCHPAD` keycodes to core ([#19884](https://github.com/qmk/qmk_firmware/pull/19884))
* Reallocate user/kb keycode ranges ([#19907](https://github.com/qmk/qmk_firmware/pull/19907))
* Reallocate SAFE_RANGE ([#19909](https://github.com/qmk/qmk_firmware/pull/19909))
* Hide hex output when building uf2 ([#19940](https://github.com/qmk/qmk_firmware/pull/19940))

CLI:
* Automate "Data Driven" migrations? ([#17820](https://github.com/qmk/qmk_firmware/pull/17820))
* Generate encodermap output from keymap.json. ([#18915](https://github.com/qmk/qmk_firmware/pull/18915))
* Publish keymap.json to API ([#19167](https://github.com/qmk/qmk_firmware/pull/19167))
* Apply suggested workaround for #18371 ([#19226](https://github.com/qmk/qmk_firmware/pull/19226))
* Align new-keymap with new-keyboard ([#19229](https://github.com/qmk/qmk_firmware/pull/19229))
* Validate keyboard name before accepting further input ([#19394](https://github.com/qmk/qmk_firmware/pull/19394))
* Implement XAP style merge semantics for DD keycodes ([#19397](https://github.com/qmk/qmk_firmware/pull/19397))
* Allow CLI to flash .uf2 files ([#19462](https://github.com/qmk/qmk_firmware/pull/19462))
* Report submodule status when not valid work-tree ([#19474](https://github.com/qmk/qmk_firmware/pull/19474))
* `qmk compile`/`qmk flash` - Validate keymap argument ([#19530](https://github.com/qmk/qmk_firmware/pull/19530))
* Add commit info to `version.h` ([#19542](https://github.com/qmk/qmk_firmware/pull/19542))
* Remove CLI commands: `multibuild`, `cformat`, `fileformat`, `pyformat`. ([#19629](https://github.com/qmk/qmk_firmware/pull/19629))
* Print distro in doctor output ([#19633](https://github.com/qmk/qmk_firmware/pull/19633))
* Reduce false positives in layout name validation ([#19646](https://github.com/qmk/qmk_firmware/pull/19646))
* Add `mass-compile` ability to filter by key existence. ([#19885](https://github.com/qmk/qmk_firmware/pull/19885))

Submodule updates:
* Update ChibiOS[-Contrib], SIO driver, configs ([#17915](https://github.com/qmk/qmk_firmware/pull/17915))
* Quantum Painter - LVGL Integration ([#18499](https://github.com/qmk/qmk_firmware/pull/18499))
* [RP2040] update i2c drivers to reflect peripheral number ([#19277](https://github.com/qmk/qmk_firmware/pull/19277))
* Update pico-sdk to 1.5.0 ([#19829](https://github.com/qmk/qmk_firmware/pull/19829))

Keyboards:
* Refactor entire Handwired K552 keyboard ([#18066](https://github.com/qmk/qmk_firmware/pull/18066))
* Moonlander: Add RGB LED layout map macro ([#18745](https://github.com/qmk/qmk_firmware/pull/18745))
* Add the Ortho60 v2 Keyboard to QMK ([#18890](https://github.com/qmk/qmk_firmware/pull/18890))
* Refactor xs60 with soldered and hotswap version ([#19049](https://github.com/qmk/qmk_firmware/pull/19049))
* [GMMK Pro] Change DEBOUNCE_TYPE to sym_eager_pk to reduce latency ([#19153](https://github.com/qmk/qmk_firmware/pull/19153))
* Add KPrepublic BM16A v2 ([#19194](https://github.com/qmk/qmk_firmware/pull/19194))
* Add Rama Works M60-B ([#19248](https://github.com/qmk/qmk_firmware/pull/19248))
* Revert RESET-> QK_BOOT in Read Me files where applicable ([#19262](https://github.com/qmk/qmk_firmware/pull/19262))
* Remove broken keymap/userspace ([#19271](https://github.com/qmk/qmk_firmware/pull/19271))
* The Uni change folder location ([#19326](https://github.com/qmk/qmk_firmware/pull/19326))
* New keymap for ID75 - paryz ([#19350](https://github.com/qmk/qmk_firmware/pull/19350))
* Remove useless line continuations ([#19399](https://github.com/qmk/qmk_firmware/pull/19399))
* Add The Uni Utility Belt Keymap ([#19411](https://github.com/qmk/qmk_firmware/pull/19411))
* Migrate `MCU` and `BOOTLOADER` to data-driven ([#19529](https://github.com/qmk/qmk_firmware/pull/19529))
* Migrate `LAYOUTS` to data driven ([#19541](https://github.com/qmk/qmk_firmware/pull/19541))
* Tidy up use of CTPC ([#19570](https://github.com/qmk/qmk_firmware/pull/19570))
* Remove matrix size defines ([#19581](https://github.com/qmk/qmk_firmware/pull/19581))
* keebio/iris document LED matrix ([#19588](https://github.com/qmk/qmk_firmware/pull/19588))
* Add support for current/voltage measurement on Ghoul. ([#19630](https://github.com/qmk/qmk_firmware/pull/19630))
* Rename ramonimbao folder to rmi_kb ([#19699](https://github.com/qmk/qmk_firmware/pull/19699))
* Remove commented out backlight config & stray "backlight levels" ([#19703](https://github.com/qmk/qmk_firmware/pull/19703))
* Clean up Force NKRO in config.h ([#19718](https://github.com/qmk/qmk_firmware/pull/19718))
* Remove unused `MATRIX_HAS_GHOST` from config.h ([#19726](https://github.com/qmk/qmk_firmware/pull/19726))
* Debounce defines cleanup ([#19742](https://github.com/qmk/qmk_firmware/pull/19742))
* Remove unused `LOCKING_SUPPORT_ENABLE` from config.h ([#19748](https://github.com/qmk/qmk_firmware/pull/19748))
* Remove `DEBOUNCE` macro usage ([#19750](https://github.com/qmk/qmk_firmware/pull/19750))
* Remove unused `GRAVE_ESC_CTRL_OVERRIDE` from config.h ([#19752](https://github.com/qmk/qmk_firmware/pull/19752))
* Remove unused Bootmagic row/col defines from config.h ([#19761](https://github.com/qmk/qmk_firmware/pull/19761))
* Remove unused `SOFT_SERIAL_PIN` from config.h ([#19768](https://github.com/qmk/qmk_firmware/pull/19768))
* Remove `SOFT_SERIAL_PIN` for non-split boards ([#19774](https://github.com/qmk/qmk_firmware/pull/19774))
* implement missing layouts + DD migration for wilba_tech/wt60_d ([#19777](https://github.com/qmk/qmk_firmware/pull/19777))
* Move LED indicator config to data driven ([#19800](https://github.com/qmk/qmk_firmware/pull/19800))
* Migrate `DIRECT_PINS` to data driven ([#19826](https://github.com/qmk/qmk_firmware/pull/19826))
* Remove lingering `I2CD2` usages w/ RP2040 ([#19833](https://github.com/qmk/qmk_firmware/pull/19833))
* Brick ([#19851](https://github.com/qmk/qmk_firmware/pull/19851))
* Remove unused RGBLight defines from config.h ([#19859](https://github.com/qmk/qmk_firmware/pull/19859))
* Move Bootmagic config to data driven ([#19860](https://github.com/qmk/qmk_firmware/pull/19860))
* Move `SOFT_SERIAL_PIN` to data driven ([#19863](https://github.com/qmk/qmk_firmware/pull/19863))
* Move layouts for direct_pins boards to data driven ([#19872](https://github.com/qmk/qmk_firmware/pull/19872))
* Move QMK LUFA bootloader config to data driven ([#19879](https://github.com/qmk/qmk_firmware/pull/19879))
* Move backlight config to data driven, part 1 ([#19887](https://github.com/qmk/qmk_firmware/pull/19887))
* Add license headers to all default layout keymaps ([#19888](https://github.com/qmk/qmk_firmware/pull/19888))
* Migrate some more layouts to data driven ([#19889](https://github.com/qmk/qmk_firmware/pull/19889))
* Remove magic bodges from via keymaps ([#19890](https://github.com/qmk/qmk_firmware/pull/19890))
* Refactor more `KC_MISSION_CONTROL`/`KC_LAUNCHPAD` usages ([#19891](https://github.com/qmk/qmk_firmware/pull/19891))
* Remove default and unused `BACKLIGHT_LEVELS` ([#19898](https://github.com/qmk/qmk_firmware/pull/19898))
* Move backlight config to data driven ([#19910](https://github.com/qmk/qmk_firmware/pull/19910))
* Remove VIA specific use of `MACRO0*` ([#19918](https://github.com/qmk/qmk_firmware/pull/19918))
* Use standard magic keycodes in `yandrstudio` keymaps ([#19919](https://github.com/qmk/qmk_firmware/pull/19919))
* Move encoder config to data driven ([#19923](https://github.com/qmk/qmk_firmware/pull/19923))

Keyboard fixes:
* Partially revert #18940 for Ploopy Thumb Trackball ([#18943](https://github.com/qmk/qmk_firmware/pull/18943))
* Fix up Info.Json files that weren't parsing correctly ([#19275](https://github.com/qmk/qmk_firmware/pull/19275))
* Fix DZTECH Tofu II v1 i2c config ([#19306](https://github.com/qmk/qmk_firmware/pull/19306))
* Fixup build failures. ([#19332](https://github.com/qmk/qmk_firmware/pull/19332))
* Fixup horrortroll/handwired_k552 ([#19447](https://github.com/qmk/qmk_firmware/pull/19447))
* Ignore defaults.hjson values if already set ([#19511](https://github.com/qmk/qmk_firmware/pull/19511))
* Fix mk0_avr_extra PIN_COMPATIBLE lint warning ([#19640](https://github.com/qmk/qmk_firmware/pull/19640))
* fix pegasushoof caps light, add via keymap ([#19649](https://github.com/qmk/qmk_firmware/pull/19649))
* Fixup handwired/jscotto/scotto40 ([#19675](https://github.com/qmk/qmk_firmware/pull/19675))
* Clean up remaining rules.mk `MCU`/`BOOTLOADER`s ([#19778](https://github.com/qmk/qmk_firmware/pull/19778))
* Fix errors flagged by generate-api ([#19784](https://github.com/qmk/qmk_firmware/pull/19784))
* Fix merge error with fave84 board ([#19808](https://github.com/qmk/qmk_firmware/pull/19808))
* Fixup ek65 -- add processor & bootloader in `info.json` ([#19815](https://github.com/qmk/qmk_firmware/pull/19815))
* Fixup durgod/dgk6x (scroll lock mis-defined as num lock) ([#19864](https://github.com/qmk/qmk_firmware/pull/19864))
* Fix API generation ([#19866](https://github.com/qmk/qmk_firmware/pull/19866))
* Fixup for_science ([#19867](https://github.com/qmk/qmk_firmware/pull/19867))
* Fix more build failures ([#19869](https://github.com/qmk/qmk_firmware/pull/19869))
* Fixup pegasushoof VIA keymap ([#19874](https://github.com/qmk/qmk_firmware/pull/19874))
* Fixup cannonkeys/satisfaction75 (readd `backlight.breathing_period`) ([#19901](https://github.com/qmk/qmk_firmware/pull/19901))
* Add some missing `#pragma once`s ([#19902](https://github.com/qmk/qmk_firmware/pull/19902))
* `keebio/kbo5000`: fix encoder config ([#19941](https://github.com/qmk/qmk_firmware/pull/19941))

Others:
* KC_GESC -> QK_GESC for cn and ja Docs ([#19024](https://github.com/qmk/qmk_firmware/pull/19024))
* Update files changed action ([#19172](https://github.com/qmk/qmk_firmware/pull/19172))
* DD bootmagic config ([#19201](https://github.com/qmk/qmk_firmware/pull/19201))
* Rework input_pressed_state docs ([#19267](https://github.com/qmk/qmk_firmware/pull/19267))
* Change log for Quick Tap Term ([#19341](https://github.com/qmk/qmk_firmware/pull/19341))
* Promote CTPC warning to error ([#19565](https://github.com/qmk/qmk_firmware/pull/19565))
* Run format-text on keyboard PRs ([#19656](https://github.com/qmk/qmk_firmware/pull/19656))
* Change defines by enums ([#19793](https://github.com/qmk/qmk_firmware/pull/19793))
* [Doc]Remove depracted extension links in vscode guide ([#19842](https://github.com/qmk/qmk_firmware/pull/19842))

Bugs:
* Make Magic handling more consistent in Action Keycode handling ([#9126](https://github.com/qmk/qmk_firmware/pull/9126))
* Fix functions when `NO_ACTION_TAPPING` is defined ([#11528](https://github.com/qmk/qmk_firmware/pull/11528))
* Return USB HID GET_REPORT requests ([#14814](https://github.com/qmk/qmk_firmware/pull/14814))
* Fixed NKRO issue caused by HID_SET_PROTOCOL on Chibios platform ([#17588](https://github.com/qmk/qmk_firmware/pull/17588))
* kint36: do not restart USB stack after wakeup ([#19077](https://github.com/qmk/qmk_firmware/pull/19077))
* Fixes to source generation [mostly typographic] ([#19160](https://github.com/qmk/qmk_firmware/pull/19160))
* Teensy 3.5: do not restart USB stack after wakeup ([#19269](https://github.com/qmk/qmk_firmware/pull/19269))
* Fixing PMW3389.c so it can compile ([#19301](https://github.com/qmk/qmk_firmware/pull/19301))
* UCIS: remove `qk_` prefix ([#19302](https://github.com/qmk/qmk_firmware/pull/19302))
* Leader: remove `qk_` prefix ([#19304](https://github.com/qmk/qmk_firmware/pull/19304))
* Tap Dance: remove `qk_` prefix ([#19313](https://github.com/qmk/qmk_firmware/pull/19313))
* Revert changes to keymap_steno.h ([#19412](https://github.com/qmk/qmk_firmware/pull/19412))
* Use unique name for regen PR branches ([#19464](https://github.com/qmk/qmk_firmware/pull/19464))
* Restore packing of midi note keycodes ([#19468](https://github.com/qmk/qmk_firmware/pull/19468))
* Fix 'Need at least one layout defined in info.json' check ([#19537](https://github.com/qmk/qmk_firmware/pull/19537))
* `qmk doctor` - Handle permission issues while checking udev ([#19548](https://github.com/qmk/qmk_firmware/pull/19548))
* `qmk doctor` - Handle timeouts while checking binaries ([#19549](https://github.com/qmk/qmk_firmware/pull/19549))
* Fix CLI community detection ([#19562](https://github.com/qmk/qmk_firmware/pull/19562))
* Fix joystick build for ChibiOS ([#19602](https://github.com/qmk/qmk_firmware/pull/19602))
* Fix converter alias after 19603 ([#19644](https://github.com/qmk/qmk_firmware/pull/19644))
* Fix functions with empty params ([#19647](https://github.com/qmk/qmk_firmware/pull/19647))
* rp2040: fix timer wrap deadlock in ws2812 vendor driver ([#19652](https://github.com/qmk/qmk_firmware/pull/19652))
* analog.c: Fix `pinToMux()` for STM32F0xx ([#19658](https://github.com/qmk/qmk_firmware/pull/19658))
* Fix quantum ring_buffer for ChibiOS ([#19683](https://github.com/qmk/qmk_firmware/pull/19683))
* Regen keycode_table for unit tests ([#19721](https://github.com/qmk/qmk_firmware/pull/19721))
* Fix midi after recent refactoring ([#19723](https://github.com/qmk/qmk_firmware/pull/19723))
* Fix build failures with `OPT = 0` due to inline functions ([#19767](https://github.com/qmk/qmk_firmware/pull/19767))
* Fix tri layer compiler issue if NO_ACTION_LAYER is defined ([#19821](https://github.com/qmk/qmk_firmware/pull/19821))
* Fixup `develop` compiles. ([#19828](https://github.com/qmk/qmk_firmware/pull/19828))
* Fix Layer Mod mishandling of right-handed mods, a mixup of 5-bit vs. 8-bit mods representation. ([#19845](https://github.com/qmk/qmk_firmware/pull/19845))
* Fix compilation issue for Key Overrides ([#19856](https://github.com/qmk/qmk_firmware/pull/19856))
* Fix regen script for macOS ([#19857](https://github.com/qmk/qmk_firmware/pull/19857))
* Fix compilation error when defining QUICK_TAP_TERM_PER_KEY ([#19893](https://github.com/qmk/qmk_firmware/pull/19893))
* VIA Protocol 12 + fixes ([#19916](https://github.com/qmk/qmk_firmware/pull/19916))
