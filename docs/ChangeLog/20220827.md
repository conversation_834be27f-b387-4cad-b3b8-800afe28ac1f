# QMK Breaking Changes - 2022 August 27 Changelog

## Notable Features {#notable-features}

### Add Raspberry Pi RP2040 support ([#14877](https://github.com/qmk/qmk_firmware/pull/14877), [#17514](https://github.com/qmk/qmk_firmware/pull/17514), [#17516](https://github.com/qmk/qmk_firmware/pull/17516), [#17519](https://github.com/qmk/qmk_firmware/pull/17519), [#17612](https://github.com/qmk/qmk_firmware/pull/17612), [#17512](https://github.com/qmk/qmk_firmware/pull/17512), [#17557](https://github.com/qmk/qmk_firmware/pull/17557), [#17817](https://github.com/qmk/qmk_firmware/pull/17817), [#17839](https://github.com/qmk/qmk_firmware/pull/17839), [#18100](https://github.com/qmk/qmk_firmware/pull/18100)) {#rp2040-support}

QMK _finally_ picked up support for RP2040-based boards, such as the Raspberry Pi Pico, the Sparkfun Pro Micro RP2040, and the Adafruit KB2040. One of QMK's newest collaborators, _@KarlK90_, effectively did `/micdrop` with RP2040, with a massive set of changes to both QMK and the repository QMK uses for the base platform support, ChibiOS[-Contrib]. There has been a flurry of development this breaking changes cycle related to RP2040 from a large number of contributors -- so much so that almost all standard QMK hardware subsystems are supported.

Check the [RP2040 platform development page](../platformdev_rp2040) for all supported peripherals and other hardware implementation details.

### Allow `qmk flash` to use prebuilt firmware binaries ([#16584](https://github.com/qmk/qmk_firmware/pull/16584)) {#cli-flash-binaries}

A long-requested capability of the QMK CLI has been the ability to flash binaries directly, without needing to build a firmware. QMK provides prebuilt `develop`-based default firmwares on our [CI page](https://qmk.tzarc.io/) -- normally people would need [QMK Toolbox](https://github.com/qmk/qmk_toolbox/releases/latest) to flash them. This new functionality written by _@Erovia_ allows `qmk flash` to be provided the prebuilt file instead, simplifying the workflow for people who haven't got Toolbox available.

## Changes Requiring User Action {#changes-requiring-user-action}

### Default layers dropped from 32 to 16 ([#15286](https://github.com/qmk/qmk_firmware/pull/15286))

QMK allows for controlling the maximum number of layers it supports through `LAYER_STATE_(8|16|32)BIT`. Each definition allows for the same number of maximum layers -- `LAYER_STATE_8BIT` => 8 layers. There is also a corresponding firmware size decrease that goes along with smaller numbers -- given the vast majority of users don't use more than 16 layers the default has been swapped to 16. AVR users who were not previously specifying their max layer count may see some space freed up as a result.

### `RESET` => `QK_BOOT` ([#17940](https://github.com/qmk/qmk_firmware/pull/17940)) {#reset-2-qk_boot}

Following the last breaking changes cycle, QMK has been migrating usages of `RESET` to `QK_BOOT` due to naming collisions with our upstream board support packages. [#17940](https://github.com/qmk/qmk_firmware/pull/17940) converts user keymaps across to use the new keycode name. `RESET` should also move to `QK_BOOT`.

### Updated Keyboard Codebases {#updated-keyboard-codebases}

The following keyboards have had their source moved within QMK:

| Old Keyboard Name      | New Keyboard Name        |
|------------------------|--------------------------|
| gentleman65            | jkeys_design/gentleman65 |
| handwired/hillside/0_1 | handwired/hillside/48    |
| idobao/id80/v1/ansi    | idobao/id80/v2/ansi      |
| idobao/id80/v1/iso     | idobao/id80/v2/iso       |

### Data-driven USB IDs Refactoring ([#18152](https://github.com/qmk/qmk_firmware/pull/18152)) {#usb-ids-Refactoring}

QMK has decided to deprecate the specification of USB IDs inside `config.h` in favour of `info.json`, eventually leaving data-driven as the only method to specify USB information.

A significant number of keyboards have already been changed on `master` in a like-for-like fashion, and [#18152](https://github.com/qmk/qmk_firmware/pull/18152) performs the same transformations for keyboards already on `develop`.

Previously in `config.h`:
```c
#define VENDOR_ID    0x1234
#define PRODUCT_ID   0x5678
#define DEVICE_VER   0x0001
#define MANUFACTURER Me
#define PRODUCT      MyKeyboard
```

Replaced by `info.json`:
```json
{
    "keyboard_name": "MyKeyboard",
    "manufacturer": "Me",
    "usb": {
        "vid": "0x1234",
        "pid": "0x5678",
        "device_version": "0.0.1"
    },
    // ... layouts, etc. ...
}
```

#### Deprecation Schedule

- From 2022 Aug 27, specifying USB information in `config.h` will produce warnings during build but will still function as previously.
- From 2022 Nov 26, specifying USB information in `config.h` will cause compilation to fail.

## Notable core changes {#notable-core}

### Board converters ([#17514](https://github.com/qmk/qmk_firmware/pull/17514), [#17603](https://github.com/qmk/qmk_firmware/pull/17603), [#17711](https://github.com/qmk/qmk_firmware/pull/17711), [#17827](https://github.com/qmk/qmk_firmware/pull/17827), [#17593](https://github.com/qmk/qmk_firmware/pull/17593), [#17652](https://github.com/qmk/qmk_firmware/pull/17652), [#17595](https://github.com/qmk/qmk_firmware/pull/17595)) {#board-converters}

Historically QMK had a `CONVERT_TO_PROTON_C` directive for `rules.mk` to allow people to replace an AVR-based Pro Micro with a QMK Proton C. Global parts shortages have prompted people to create their own pin-compatible boards -- QMK has made this conversion generic and now allows for drop-in replacements for a lot more boards. see the [Converters Feature](../feature_converters) documentation for the full list of supported replacement boards -- in this breaking changes cycle we've gone from 1 to 7.

### Add cli command to import keyboard|keymap|kbfirmware ([#16668](https://github.com/qmk/qmk_firmware/pull/16668)) {#cli-import}

To help with importing keyboards and keymaps from other sources, _@zvecr_ added [#16668](https://github.com/qmk/qmk_firmware/pull/16668) which adds a new set of commands to the CLI to automatically import keyboards (`qmk import-keyboard -h`), keymaps (`qmk import-keymap -h`), and kbfirmware definitions (`qmk import-kbfirmware -h`) into QMK.

The now-EOL kbfirmware allowed people who aren't set up with QMK the ability to create keyboard firmwares without requiring a full installation of QMK. Unfortunately, it targets a 7-year-old version of QMK -- adding frustration for users who want the newest features, as well as for QMK maintainers who have to spend time explaining why QMK can't just accept a drive-by code drop from kbfirmware. With any luck, this new command helps both camps!

### Generic wear-leveling for EEPROM emulation ([#16996](https://github.com/qmk/qmk_firmware/pull/16996), [#17376](https://github.com/qmk/qmk_firmware/pull/17376), [#18102](https://github.com/qmk/qmk_firmware/pull/18102)) {#wear-leveling}

QMK has had the ability to write to internal MCU flash in order to emulate EEPROM for some time now, but it was only limited to a small number of MCUs. The base HAL used by QMK for a large number of ARM devices provides a "proper" embedded MCU flash driver, so _@tzarc_ decoupled the wear-leveling algorithm from the old flash writing code, improved it, wrote some tests, and enabled its use for a much larger number of other devices... including RP2040's XIP flash, and external SPI NOR Flash.

See the [EEPROM Driver](../drivers/eeprom) documentation for more information.

### Pointing Device Improvements ([#16371](https://github.com/qmk/qmk_firmware/pull/16371), [#17111](https://github.com/qmk/qmk_firmware/pull/17111), [#17176](https://github.com/qmk/qmk_firmware/pull/17176), [#17482](https://github.com/qmk/qmk_firmware/pull/17482), [#17776](https://github.com/qmk/qmk_firmware/pull/17776), [#17613](https://github.com/qmk/qmk_firmware/pull/17613)) {#pointing-device-improvements}

Ever since Pointing Device Driver support and Split Pointing Device support were added by _@drashna_ and _@daskygit_, there has been increased interest in the development of the pointing device subsystem and its associated code.

Both the PMW33xx and the Cirque Pinnacle implementations have seen a lot of improvement to their code, as has the mouse code in general. Features like circular/edge scrolling for the Cirque, and Kinetic movement for any sensor with "lift detection" ([#17482](https://github.com/qmk/qmk_firmware/pull/17482)). Additionally, for those that make fast motions with their pointing devices, support for much larger mouse movement reports has been added ([#16371](https://github.com/qmk/qmk_firmware/pull/16371)).

Other related changes:

* Add support for large Mouse Reports ([#16371](https://github.com/qmk/qmk_firmware/pull/16371))
* Improve PS/2 mouse performance ([#17111](https://github.com/qmk/qmk_firmware/pull/17111))
* Mouse key kinetic mode fix ([#17176](https://github.com/qmk/qmk_firmware/pull/17176))
* Circular scroll, inertial cursor ([#17482](https://github.com/qmk/qmk_firmware/pull/17482))
* Create generic Pointing Device Pin defines ([#17776](https://github.com/qmk/qmk_firmware/pull/17776))
* PMW33XX drivers overhaul ([#17613](https://github.com/qmk/qmk_firmware/pull/17613))

---

## Full changelist {#full-changelist}

Core:
* Tentative Teensy 3.5 support ([#14420](https://github.com/qmk/qmk_firmware/pull/14420))
* Make default layer size 16-bit ([#15286](https://github.com/qmk/qmk_firmware/pull/15286))
* Process all changed keys in one scan loop, deprecate `QMK_KEYS_PER_SCAN` ([#15292](https://github.com/qmk/qmk_firmware/pull/15292))
* Do not enable PERMISSIVE_HOLD when TAPPING_TERM exceeds 500ms ([#15674](https://github.com/qmk/qmk_firmware/pull/15674))
* Allow usage of ChibiOS's SIO driver for split keyboards ([#15907](https://github.com/qmk/qmk_firmware/pull/15907))
* [Controller] Added board config for custom controller STeMCell ([#16287](https://github.com/qmk/qmk_firmware/pull/16287))
* PoC: Swap Escape and Caps ([#16336](https://github.com/qmk/qmk_firmware/pull/16336))
* Add support for large Mouse Reports ([#16371](https://github.com/qmk/qmk_firmware/pull/16371))
* tap-dance: Restructure code and document in more detail ([#16394](https://github.com/qmk/qmk_firmware/pull/16394))
* Teaching the CLI to flash binaries ([#16584](https://github.com/qmk/qmk_firmware/pull/16584))
* Split ChibiOS usart split driver in protocol and hardware driver part ([#16669](https://github.com/qmk/qmk_firmware/pull/16669))
* Added Wait time to sending each Keys for Dynamic Macros function ([#16800](https://github.com/qmk/qmk_firmware/pull/16800))
* Added Delay time to sending each Keys for VIA Macros function feature ([#16810](https://github.com/qmk/qmk_firmware/pull/16810))
* Improve avr wait_us() ([#16879](https://github.com/qmk/qmk_firmware/pull/16879))
* Improve ENCODER_DEFAULT_POS to recognize lost ticks ([#16932](https://github.com/qmk/qmk_firmware/pull/16932))
* Added emacs as an "operating system" for input mode. ([#16949](https://github.com/qmk/qmk_firmware/pull/16949))
* 24LC32A EEPROM addition ([#16990](https://github.com/qmk/qmk_firmware/pull/16990))
* Refactor steno and add `STENO_PROTOCOL = [all|txbolt|geminipr]` ([#17065](https://github.com/qmk/qmk_firmware/pull/17065))
* improvements for Cirque Pinnacle trackpads ([#17091](https://github.com/qmk/qmk_firmware/pull/17091))
* Use TAP_HOLD_CAPS_DELAY for KC_LOCKING_CAPS_LOCK ([#17099](https://github.com/qmk/qmk_firmware/pull/17099))
* Improve PS/2 mouse performance ([#17111](https://github.com/qmk/qmk_firmware/pull/17111))
* Update C standard to GNU11, C++ to GNU++14 ([#17114](https://github.com/qmk/qmk_firmware/pull/17114))
* Added ws2812_pwm support for WB32 MCU. ([#17142](https://github.com/qmk/qmk_firmware/pull/17142))
* Added ws2812_spi support for WB32 MCU ([#17143](https://github.com/qmk/qmk_firmware/pull/17143))
* Make bootloader_jump for dualbank STM32 respect STM32_BOOTLOADER_DUAL_BANK_DELAY ([#17178](https://github.com/qmk/qmk_firmware/pull/17178))
* Expose the time of the last change to the LED state ([#17222](https://github.com/qmk/qmk_firmware/pull/17222))
* [Code] Add solid reactive gradient mode ([#17228](https://github.com/qmk/qmk_firmware/pull/17228))
* Add keymap wrappers for introspection into the keymap. ([#17229](https://github.com/qmk/qmk_firmware/pull/17229))
* Ensure eeconfig initialised before reading EEPROM handedness. ([#17256](https://github.com/qmk/qmk_firmware/pull/17256))
* Add uf2-split-* make targets. ([#17257](https://github.com/qmk/qmk_firmware/pull/17257))
* Removes terminal from QMK. ([#17258](https://github.com/qmk/qmk_firmware/pull/17258))
* Make SPI Mode configurable for AW20216 and change default mode to 3 ([#17263](https://github.com/qmk/qmk_firmware/pull/17263))
* Move SPLIT_HAND_PIN setup to split_pre_init ([#17271](https://github.com/qmk/qmk_firmware/pull/17271))
* Allow larger SPLIT_USB_TIMEOUT with default SPLIT_USB_TIMEOUT_POLL ([#17272](https://github.com/qmk/qmk_firmware/pull/17272))
* Feature-ify Send String ([#17275](https://github.com/qmk/qmk_firmware/pull/17275))
* Rework paths for eeprom locations. ([#17326](https://github.com/qmk/qmk_firmware/pull/17326))
* Pca9505/6 driver ([#17333](https://github.com/qmk/qmk_firmware/pull/17333))
* Cirque Attenuation Setting ([#17342](https://github.com/qmk/qmk_firmware/pull/17342))
* Scale brigthness for VIA ([#17352](https://github.com/qmk/qmk_firmware/pull/17352))
* Ensure that rgb+via compiles in all cases ([#17355](https://github.com/qmk/qmk_firmware/pull/17355))
* Wear-leveling EEPROM drivers: `embedded_flash`, `spi_flash`, `legacy` ([#17376](https://github.com/qmk/qmk_firmware/pull/17376))
* In honor of king terry ([#17387](https://github.com/qmk/qmk_firmware/pull/17387))
* tap-dance: Rename tests so that tap_dance is used consistently ([#17396](https://github.com/qmk/qmk_firmware/pull/17396))
* IS31FL3737 Global Current Setting ([#17420](https://github.com/qmk/qmk_firmware/pull/17420))
* [QP] Add ILI9488 support. ([#17438](https://github.com/qmk/qmk_firmware/pull/17438))
* Mark GD32VF103 as ChibiOS-Contrib ([#17444](https://github.com/qmk/qmk_firmware/pull/17444))
* ISSI Drivers Global Current Option ([#17448](https://github.com/qmk/qmk_firmware/pull/17448))
* [Split] pointing transport check ([#17481](https://github.com/qmk/qmk_firmware/pull/17481))
* Cirque trackpad features: circular scroll, inertial cursor ([#17482](https://github.com/qmk/qmk_firmware/pull/17482))
* RGB heatmap skip NO_LED ([#17488](https://github.com/qmk/qmk_firmware/pull/17488))
* Add kb2040 and sparkfun rp2040 converters ([#17514](https://github.com/qmk/qmk_firmware/pull/17514))
* [style] rp2040 stage2 formatting ([#17516](https://github.com/qmk/qmk_firmware/pull/17516))
* Also check /run/media/ for uf2 drives ([#17517](https://github.com/qmk/qmk_firmware/pull/17517))
* RP2040 emulated EEPROM. ([#17519](https://github.com/qmk/qmk_firmware/pull/17519))
* Make debounce algorithms signal matrix changes ([#17554](https://github.com/qmk/qmk_firmware/pull/17554))
* Update PM2040 I2C pins ([#17578](https://github.com/qmk/qmk_firmware/pull/17578))
* Added implementation of WB32 MCU wear_leveling_efl. ([#17579](https://github.com/qmk/qmk_firmware/pull/17579))
* Use Pro Micro SDA/SCL pinout for PM2040 ([#17595](https://github.com/qmk/qmk_firmware/pull/17595))
* Refactor Pixel Fractal effect ([#17602](https://github.com/qmk/qmk_firmware/pull/17602))
* Add Blok RP2040 converter ([#17603](https://github.com/qmk/qmk_firmware/pull/17603))
* Use polled waiting on ChibiOS platforms that support it ([#17607](https://github.com/qmk/qmk_firmware/pull/17607))
* Stabilize Half-duplex RP2040 PIO split comms ([#17612](https://github.com/qmk/qmk_firmware/pull/17612))
* PMW33XX drivers overhaul ([#17613](https://github.com/qmk/qmk_firmware/pull/17613))
* Include stdint.h in avr/i2c_master.h ([#17639](https://github.com/qmk/qmk_firmware/pull/17639))
* Add led matrix support for CKLED2001 ([#17643](https://github.com/qmk/qmk_firmware/pull/17643))
* `STM32_USB_USE_OTG1` => `USB_ENDPOINTS_ARE_REORDERABLE` ([#17647](https://github.com/qmk/qmk_firmware/pull/17647))
* Allow MCU-specific overrides for SPI flags. ([#17650](https://github.com/qmk/qmk_firmware/pull/17650))
* Update LED/RGB Matrix flag function behavior ([#17651](https://github.com/qmk/qmk_firmware/pull/17651))
* Cirque circular scroll: Support POINTING_DEVICE_COMBINED ([#17654](https://github.com/qmk/qmk_firmware/pull/17654))
* Add support for PAW3204 Optical Sensor ([#17669](https://github.com/qmk/qmk_firmware/pull/17669))
* Add LED limits call ([#17679](https://github.com/qmk/qmk_firmware/pull/17679))
* Move Pointing Device code to a subdirectory ([#17684](https://github.com/qmk/qmk_firmware/pull/17684))
* Avoid OOB in dynamic_keymap_reset ([#17695](https://github.com/qmk/qmk_firmware/pull/17695))
* Allow dynamic keymap to compile without `via.h` ([#17703](https://github.com/qmk/qmk_firmware/pull/17703))
* Use correct angle tune range of +/-127 on PMW33XX ([#17708](https://github.com/qmk/qmk_firmware/pull/17708))
* Add Bonsai C4 converter ([#17711](https://github.com/qmk/qmk_firmware/pull/17711))
* VIA Encoder Map Support ([#17734](https://github.com/qmk/qmk_firmware/pull/17734))
* Move Pointing Device Initialization to after Split Post Initialization ([#17740](https://github.com/qmk/qmk_firmware/pull/17740))
* Add ability to enter bootloader mode from `QK_MAKE` ([#17745](https://github.com/qmk/qmk_firmware/pull/17745))
* Add `tap_code16_delay` ([#17748](https://github.com/qmk/qmk_firmware/pull/17748))
* Implement relative mode for Cirque trackpad ([#17760](https://github.com/qmk/qmk_firmware/pull/17760))
* Create generic Pointing Device Pin defines ([#17776](https://github.com/qmk/qmk_firmware/pull/17776))
* Constrain Cirque Pinnacle coordinates ([#17803](https://github.com/qmk/qmk_firmware/pull/17803))
* Refactor/rename postprocess_steno_user → post_process_steno_user ([#17823](https://github.com/qmk/qmk_firmware/pull/17823))
* Add Bit-C PRO converter ([#17827](https://github.com/qmk/qmk_firmware/pull/17827))
* guard RPC invocation by checking RPC info against crc checksum ([#17840](https://github.com/qmk/qmk_firmware/pull/17840))
* Add ST7735 driver to Quantum Painter ([#17848](https://github.com/qmk/qmk_firmware/pull/17848))
* Add minimal STM32F103C6 support ([#17853](https://github.com/qmk/qmk_firmware/pull/17853))
* Remove legacy AVR ssd1306 driver ([#17864](https://github.com/qmk/qmk_firmware/pull/17864))
* Remove tmk_core 'serial' code ([#17866](https://github.com/qmk/qmk_firmware/pull/17866))
* Use LT_ZCAR in place of LT_PLUS for modded kc definitions of keymap_lithuanian_qwerty.h ([#18000](https://github.com/qmk/qmk_firmware/pull/18000))
* Remove invisible variation selector-15 from keymap_japanese.h ([#18007](https://github.com/qmk/qmk_firmware/pull/18007))
* define CZ_PERC S(CZ_PLUS) → define CZ_PERC S(CZ_EQL) ([#18008](https://github.com/qmk/qmk_firmware/pull/18008))
* KR_DQUO S(KR_COLN) → KR_DQUO S(KR_QUOT) in keymap_korean.h ([#18011](https://github.com/qmk/qmk_firmware/pull/18011))
* Replace ; by : in the shifted symbols ASCII art of keymap_norman ([#18029](https://github.com/qmk/qmk_firmware/pull/18029))
* Add eeprom defaults for tinyuf2 bootloader ([#18042](https://github.com/qmk/qmk_firmware/pull/18042))
* Remove duplicate COMBINING HORN in keymap_us_extended.h ([#18045](https://github.com/qmk/qmk_firmware/pull/18045))
* Nix shell updates for `develop` ([#18131](https://github.com/qmk/qmk_firmware/pull/18131))

CLI:
* Add cli command to import keyboard|keymap|kbfirmware ([#16668](https://github.com/qmk/qmk_firmware/pull/16668))
* Publish data as part of API generation ([#17020](https://github.com/qmk/qmk_firmware/pull/17020))
* Allow encoder config from info.json ([#17295](https://github.com/qmk/qmk_firmware/pull/17295))
* `qmk doctor`: show arch for macOS ([#17356](https://github.com/qmk/qmk_firmware/pull/17356))
* Use --exclude-from=.gitignore in place of --exclude-standard ([#17399](https://github.com/qmk/qmk_firmware/pull/17399))
* Improve importer workflow ([#17707](https://github.com/qmk/qmk_firmware/pull/17707))
* Remove legacy bootmagic cli parsing ([#18099](https://github.com/qmk/qmk_firmware/pull/18099))
* Align CLI requirements ([#18117](https://github.com/qmk/qmk_firmware/pull/18117))

Submodule updates:
* Add Raspberry Pi RP2040 support ([#14877](https://github.com/qmk/qmk_firmware/pull/14877))
* Update mpaland/printf to eyalroz/printf fork ([#16163](https://github.com/qmk/qmk_firmware/pull/16163))
* Generic wear-leveling algorithm ([#16996](https://github.com/qmk/qmk_firmware/pull/16996))
* Update LUFA submodule ([#17368](https://github.com/qmk/qmk_firmware/pull/17368))
* Update V-USB submodule ([#17385](https://github.com/qmk/qmk_firmware/pull/17385))
* Update ChibiOS-Contrib ([#17540](https://github.com/qmk/qmk_firmware/pull/17540))
* Update to latest ChibiOS-Contrib. ([#18016](https://github.com/qmk/qmk_firmware/pull/18016))
* Update LUFA submodule ([#18168](https://github.com/qmk/qmk_firmware/pull/18168))

Keyboards:
* GMMK 2 WBG7 MCU compatibility ([#16436](https://github.com/qmk/qmk_firmware/pull/16436))
* bastardkb: restructure folder hierarchy ([#16778](https://github.com/qmk/qmk_firmware/pull/16778))
* Add Gentleman 65 SE Solderd PCB support ([#16992](https://github.com/qmk/qmk_firmware/pull/16992))
* Move/Rename to Hillside48, simplify default keymap ([#17210](https://github.com/qmk/qmk_firmware/pull/17210))
* IDOBAO ID67 code touch-ups and include factory keymap ([#17231](https://github.com/qmk/qmk_firmware/pull/17231))
* IDOBAO ID87v2 code rewrite and include factory keymap ([#17232](https://github.com/qmk/qmk_firmware/pull/17232))
* IDOBAO ID80v3 code rewrite and include factory keymap ([#17234](https://github.com/qmk/qmk_firmware/pull/17234))
* IDOBAO ID80v1 folder rename ([#17265](https://github.com/qmk/qmk_firmware/pull/17265))
* Fine!40 PCB Support ([#17426](https://github.com/qmk/qmk_firmware/pull/17426))
* Update Charybdis code for Extended Mouse reports ([#17435](https://github.com/qmk/qmk_firmware/pull/17435))
* (develop)AP2: Enable support for WL EEPROM Driver ([#17506](https://github.com/qmk/qmk_firmware/pull/17506))
* (develop)Keychron Q2: Enable support for WL EEPROM Driver ([#17507](https://github.com/qmk/qmk_firmware/pull/17507))
* Add Adafruit Macropad RP2040 ([#17512](https://github.com/qmk/qmk_firmware/pull/17512))
* Add RP2040 config defaults ([#17557](https://github.com/qmk/qmk_firmware/pull/17557))
* Add support keyboard Feker IK75 ([#17611](https://github.com/qmk/qmk_firmware/pull/17611))
* boardsource/holiday/spooky data driven ([#17632](https://github.com/qmk/qmk_firmware/pull/17632))
* boardsource/lulu data driven ([#17638](https://github.com/qmk/qmk_firmware/pull/17638))
* Added support for gmmk pro rev2 keyboard. ([#17655](https://github.com/qmk/qmk_firmware/pull/17655))
* boardsource/microdox data driven ([#17675](https://github.com/qmk/qmk_firmware/pull/17675))
* Remove full bootmagic config from user files ([#17702](https://github.com/qmk/qmk_firmware/pull/17702))
* (develop) Update bootmagic for Adafruit Macropad ([#17755](https://github.com/qmk/qmk_firmware/pull/17755))
* Add a kb2040 version of the onkey keyboard that works with the oled keymap ([#17786](https://github.com/qmk/qmk_firmware/pull/17786))
* Enable mousekeys by default for RGBKB Sol3 ([#17842](https://github.com/qmk/qmk_firmware/pull/17842))
* More glyph transformations for spidey3 userspace ([#17854](https://github.com/qmk/qmk_firmware/pull/17854))
* Default rgblight ([#17855](https://github.com/qmk/qmk_firmware/pull/17855))
* Refactor satt/comet46 to use core OLED driver ([#17856](https://github.com/qmk/qmk_firmware/pull/17856))
* Convert yosino58 to use split common ([#17861](https://github.com/qmk/qmk_firmware/pull/17861))
* Migrate crkbd keymaps to oled driver ([#17863](https://github.com/qmk/qmk_firmware/pull/17863))
* Overhaul uzu42 ([#17868](https://github.com/qmk/qmk_firmware/pull/17868))
* Update ginkgo65hot to allow use of community layouts ([#17911](https://github.com/qmk/qmk_firmware/pull/17911))
* Remove `UNUSED_PINS` ([#17931](https://github.com/qmk/qmk_firmware/pull/17931))
* RESET -> QK_BOOT user keymaps ([#17940](https://github.com/qmk/qmk_firmware/pull/17940))
* Add cursor layer to DMQ Spin ([#17996](https://github.com/qmk/qmk_firmware/pull/17996))
* add new keyboard 'soda/cherish' ([#18057](https://github.com/qmk/qmk_firmware/pull/18057))
* Move keyboard USB IDs and strings to data driven: develop ([#18152](https://github.com/qmk/qmk_firmware/pull/18152))

Keyboard fixes:
* Fixup SPI mode 3 => 0 on tzarc/djinn, `develop`. ([#17440](https://github.com/qmk/qmk_firmware/pull/17440))
* Fixup doio/kb16 ([#17545](https://github.com/qmk/qmk_firmware/pull/17545))
* Adafruit Macropad: Add VIA keymap, fix default km ([#17735](https://github.com/qmk/qmk_firmware/pull/17735))
* Fix compilation issues for Charybdis/Dilemma ([#17791](https://github.com/qmk/qmk_firmware/pull/17791))
* bastardkb: fix info.json changes that got reverted during the last merge from `master` to `develop` ([#17800](https://github.com/qmk/qmk_firmware/pull/17800))
* Fixup uzu42 ([#17867](https://github.com/qmk/qmk_firmware/pull/17867))
* use correct function in Dilemma splinky ([#17923](https://github.com/qmk/qmk_firmware/pull/17923))
* Fix compilation issues for Boardsource Microdox ([#18037](https://github.com/qmk/qmk_firmware/pull/18037))
* Fixup gmmk/pro/rev2 USB Data ([#18056](https://github.com/qmk/qmk_firmware/pull/18056))

Others:
* backlight|led 'on state' for DD configuration ([#17383](https://github.com/qmk/qmk_firmware/pull/17383))
* Dump out the largest symbols in flash and in RAM. ([#17397](https://github.com/qmk/qmk_firmware/pull/17397))
* Re-order user space rules inclusion ([#17459](https://github.com/qmk/qmk_firmware/pull/17459))
* Update feature_split_keyboard.md to add extra detail about left and right matrices. ([#17492](https://github.com/qmk/qmk_firmware/pull/17492))
* Swap F4x1 default board files away from blackpill ([#17522](https://github.com/qmk/qmk_firmware/pull/17522))
* Add converter docs ([#17593](https://github.com/qmk/qmk_firmware/pull/17593))
* Updates to Pointing Device Docs ([#17777](https://github.com/qmk/qmk_firmware/pull/17777))
* Add deprecated check for RGBLIGHT_ANIMATIONS ([#17832](https://github.com/qmk/qmk_firmware/pull/17832))
* Remove OLED driver Split Common warning ([#17862](https://github.com/qmk/qmk_firmware/pull/17862))
* Revert " Re-order user space rules inclusion (#17459)" ([#18032](https://github.com/qmk/qmk_firmware/pull/18032))

Bugs:
* Minor schema fixes ([#14200](https://github.com/qmk/qmk_firmware/pull/14200))
* Fix buffer size for WS2812 PWM driver ([#17046](https://github.com/qmk/qmk_firmware/pull/17046))
* Fix AVR I2C master 1ms timeout ([#17174](https://github.com/qmk/qmk_firmware/pull/17174))
* Mouse key kinetic mode fix ([#17176](https://github.com/qmk/qmk_firmware/pull/17176))
* Fix RGB heatmap to use XY positions and use correct led limits. ([#17184](https://github.com/qmk/qmk_firmware/pull/17184))
* Fix keys being discarded after using the leader key ([#17287](https://github.com/qmk/qmk_firmware/pull/17287))
* Fixup pimoroni trackball ([#17335](https://github.com/qmk/qmk_firmware/pull/17335))
* Fix via builds broken by brightness scaling ([#17354](https://github.com/qmk/qmk_firmware/pull/17354))
* SPI Bugfix for ChibiOS `21.11.1` => `21.11.2` ([#17371](https://github.com/qmk/qmk_firmware/pull/17371))
* Additional schema fixes ([#17414](https://github.com/qmk/qmk_firmware/pull/17414))
* Fix deadlocks on disconnected secondary half ([#17423](https://github.com/qmk/qmk_firmware/pull/17423))
* [Fix] Fix compilation warning for non-split keebs after #17423 ([#17439](https://github.com/qmk/qmk_firmware/pull/17439))
* Fix Caps Word to treat mod-taps more consistently. ([#17463](https://github.com/qmk/qmk_firmware/pull/17463))
* Fix docs regarding `USB_SUSPEND_WAKEUP_DELAY` ([#17501](https://github.com/qmk/qmk_firmware/pull/17501))
* Fixup SSD1351 build after #17438 ([#17533](https://github.com/qmk/qmk_firmware/pull/17533))
* Fixup SPI init procedure, SPI EEPROM sequencing ([#17534](https://github.com/qmk/qmk_firmware/pull/17534))
* Fix Caps Word capitalization when used with Combos + Auto Shift. ([#17549](https://github.com/qmk/qmk_firmware/pull/17549))
* Allow for `keymaps` array to be implemented in a file other than `$(KEYMAP_C)` ([#17559](https://github.com/qmk/qmk_firmware/pull/17559))
* [Fix] printf update aftermath ([#17584](https://github.com/qmk/qmk_firmware/pull/17584))
* Fix rgbkb/sol/rev2 build issues ([#17601](https://github.com/qmk/qmk_firmware/pull/17601))
* More DD encoder fixes ([#17615](https://github.com/qmk/qmk_firmware/pull/17615))
* [Fix] Make ChibiOS `_wait.h` independent of `quantum.h` ([#17645](https://github.com/qmk/qmk_firmware/pull/17645))
* Grammar fixes for docs/feature_converters.md ([#17652](https://github.com/qmk/qmk_firmware/pull/17652))
* Fix compilation issue with Cirque Guestures file ([#17656](https://github.com/qmk/qmk_firmware/pull/17656))
* Fix compile issue with LED Matrix ([#17658](https://github.com/qmk/qmk_firmware/pull/17658))
* Post-bootloader EFL/SPI fixes. ([#17661](https://github.com/qmk/qmk_firmware/pull/17661))
* Fix LED limit loop ([#17678](https://github.com/qmk/qmk_firmware/pull/17678))
* [Fix] Use correct angle tune range of +/-30 on PMW33XX ([#17693](https://github.com/qmk/qmk_firmware/pull/17693))
* Fix AVR compilation of FNV by using standard integer typenames. ([#17716](https://github.com/qmk/qmk_firmware/pull/17716))
* fix syntax error in header file ([#17732](https://github.com/qmk/qmk_firmware/pull/17732))
* Fix custom debug function and sample output ([#17790](https://github.com/qmk/qmk_firmware/pull/17790))
* Fix QK_MAKE's reboot check ([#17795](https://github.com/qmk/qmk_firmware/pull/17795))
* Chibios: Stop I2C peripheral on transaction error ([#17798](https://github.com/qmk/qmk_firmware/pull/17798))
* Fix ChibiOS `i2c_master` error codes ([#17808](https://github.com/qmk/qmk_firmware/pull/17808))
* Update ChibiOS Contrib for RP2040 fixes ([#17817](https://github.com/qmk/qmk_firmware/pull/17817))
* RP2040 disable PIO IRQs on serial timeout ([#17839](https://github.com/qmk/qmk_firmware/pull/17839))
* Fix POINTING_DEVICE_GESTURES_SCROLL_ENABLE typo ([#17850](https://github.com/qmk/qmk_firmware/pull/17850))
* Fixup compilation of printf-like functions with uint32_t args. ([#17904](https://github.com/qmk/qmk_firmware/pull/17904))
* Fix issue with #17904. ([#17905](https://github.com/qmk/qmk_firmware/pull/17905))
* Always run pointing device init ([#17936](https://github.com/qmk/qmk_firmware/pull/17936))
* Align TO() max layers with other keycodes ([#17989](https://github.com/qmk/qmk_firmware/pull/17989))
* Fix Bépo's BP_NNBS (narrow non-breaking space) ([#17999](https://github.com/qmk/qmk_firmware/pull/17999))
* Move Encoder+Encoder Map from generic features ([#18018](https://github.com/qmk/qmk_firmware/pull/18018))
* Fix wrong varaible in encoder block ([#18020](https://github.com/qmk/qmk_firmware/pull/18020))
* Fix LV_CCAR and LV_NCED in keymap_latvian.h ([#18025](https://github.com/qmk/qmk_firmware/pull/18025))
* Use ANSI ASCII art and fix comments for LT_COLN and LT_UNDS in keymap_lithuanian_qwerty.h ([#18028](https://github.com/qmk/qmk_firmware/pull/18028))
* Partially revert some WB32 specific changes ([#18038](https://github.com/qmk/qmk_firmware/pull/18038))
* Fix Emulated EEPROM issue with F466 ([#18039](https://github.com/qmk/qmk_firmware/pull/18039))
* Fix DV_SCLN and DV_COLN in keymap_spanish_dvorak.h ([#18043](https://github.com/qmk/qmk_firmware/pull/18043))
* Fix missing development_board schema entry ([#18050](https://github.com/qmk/qmk_firmware/pull/18050))
* Add key event check to `is_tap_record` and remove `is_tap_key` ([#18063](https://github.com/qmk/qmk_firmware/pull/18063))
* Fix GD32VF103 WS2812 PWM driver ([#18067](https://github.com/qmk/qmk_firmware/pull/18067))
* Fix new-keyboard default for RP2040 bootloader ([#18100](https://github.com/qmk/qmk_firmware/pull/18100))
* Fixup F4xx wear-leveling bootloader check ([#18102](https://github.com/qmk/qmk_firmware/pull/18102))
* Fix PID value for the Keyboardio Atreus 2 bootloader ([#18116](https://github.com/qmk/qmk_firmware/pull/18116))
* Add missing SS_LOPT and SS_ROPT defines ([#18175](https://github.com/qmk/qmk_firmware/pull/18175))
