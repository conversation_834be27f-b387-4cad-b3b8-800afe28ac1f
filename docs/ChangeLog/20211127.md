# QMK Breaking Changes - 2021 November 27 Changelog

## 2000 keyboards! {#qmk-2000th-keyboard}

QMK had it's 2000th keyboard submitted during this breaking changes cycle.... and it only _just_ made the cut-off!

```shell
% qmk list-keyboards | wc -l
2003
```

From the whole QMK team, a major thankyou to the community for embracing QMK as your preferred keyboard firmware!

## Notable Features {#notable-features}

### Expanded Pointing Device support ([#14343](https://github.com/qmk/qmk_firmware/pull/14343)) {#expanded-pointing-device}

Pointing device support has been reworked and reimplemented to allow for easier integration of new peripherals.

Usages of `POINTING_DEVICE_ENABLE = yes` in `rules.mk` files now need to be accompanied by a corresponding `POINTING_DEVICE_DRIVER = ???` line, specifying which driver to use during the build. Existing keyboards have already been migrated across to the new usage pattern, so most likely no change is required by users.

QMK now has core-supplied support for the following pointing device peripherals:

| `rules.mk` line                                | Supported device                        |
|------------------------------------------------|-----------------------------------------|
| `POINTING_DEVICE_DRIVER = analog_joystick`     | Analog joysticks, such as PSP joysticks |
| `POINTING_DEVICE_DRIVER = adns5050`            | ADNS 5050 sensor                        |
| `POINTING_DEVICE_DRIVER = adns9800`            | ADNS 9800 laser sensor                  |
| `POINTING_DEVICE_DRIVER = cirque_pinnacle_i2c` | Cirque touchpad, I2C mode               |
| `POINTING_DEVICE_DRIVER = cirque_pinnacle_spi` | Cirque Touchpad, SPI mode               |
| `POINTING_DEVICE_DRIVER = pimoroni_trackball`  | Pimoroni Trackball                      |
| `POINTING_DEVICE_DRIVER = pmw3360`             | PMW 3360                                |

See the new documentation for the [Pointing Device](../features/pointing_device) feature for more information on specific configuration for each driver.

### Dynamic Tapping Term ([#11036](https://github.com/qmk/qmk_firmware/pull/11036)) {#dynamic-tapping-term}

For people who are starting out with tapping keys, or for people who think tapping keys don't "feel right", it's sometimes quite difficult to determine what duration of tapping term to use to make things seem natural.

If you're in this stage of discovery, you can now add `DYNAMIC_TAPPING_TERM_ENABLE = yes` to your `rules.mk`, which enables the use of the following keycodes in your keymap:

| Key       | Description                                                                   |
|-----------|-------------------------------------------------------------------------------|
| `DT_PRNT` | "Dynamic Tapping Term Print": Types the current tapping term, in milliseconds |
| `DT_UP`   | "Dynamic Tapping Term Up": Increases the current tapping term by 5ms          |
| `DT_DOWN` | "Dynamic Tapping Term Down": Decreases the current tapping term by 5ms        |

Coupled with the use of `qmk console` or QMK Toolbox to show console output from your keyboard, you can tweak the tapping term dynamically in order to narrow down what "feels right" to you. Once you're happy, drop in the resulting number into your keymap's `config.h` and you're good to go!

### Macros in JSON keymaps ([#14374](https://github.com/qmk/qmk_firmware/pull/14374)) {#macros-in-keymap-json}

You can now define up to 32 macros in your `keymap.json` file, as used by [QMK Configurator](../newbs_building_firmware_configurator), and `qmk compile`. You can define these macros in a list under the `macros` keyword, like this:

```json
{
    "keyboard": "handwired/my_macropad",
    "keymap": "my_keymap",
    "macros": [
        [ // first listed is QK_MACRO_0...
            {"action":"down", "keycodes": ["LSFT"]},
            "hello world1",
            {"action": "up","keycodes": ["LSFT"]}
        ],
        [ // ...then QK_MACRO_1...
            {"action":"tap", "keycodes": ["LCTL", "LALT", "DEL"]}
        ],
        [ // ...then QK_MACRO_2...
            "ding!",
            {"action":"beep"}
        ],
        [ // ...and QK_MACRO_3.
            {"action":"tap", "keycodes": ["F1"]},
            {"action":"delay", "duration": "1000"},
            {"action":"tap", "keycodes": ["PGDN"]}
        ]
    ],
    "layout": "LAYOUT_all",
    "layers": [
        ["QK_MACRO_0", "QK_MACRO_1", "QK_MACRO_2", "QK_MACRO_3"]
    ]
}
```

In due course, [QMK Configurator](https://config.qmk.fm/) will pick up support for defining these in its UI, but for now the json is the only way to define macros.

## Changes Requiring User Action {#changes-requiring-user-action}

### Updated Keyboard Codebases {#updated-keyboard-codebases}

The following keyboards have had their source moved within QMK:

| Old Keyboard Name      | New Keyboard Name               |
|------------------------|---------------------------------|
| aozora/hotswap         | aozora                          |
| gskt00                 | kapcave/gskt00                  |
| handwired/dtisaac01    | dtisaac/dtisaac01               |
| kprepublic/bm60poker   | kprepublic/bm60hsrgb_poker/rev1 |
| kprepublic/bm60rgb     | kprepublic/bm60hsrgb/rev1       |
| kprepublic/bm60rgb_iso | kprepublic/bm60hsrgb_iso/rev1   |
| kprepublic/bm65iso     | kprepublic/bm65hsrgb_iso        |
| kprepublic/bm68rgb     | kprepublic/bm68hsrgb            |
| paladin64              | kapcave/paladin64               |
| portal_66              | portal_66/soldered              |
| signum/3_0/elitec      | signum/3_0                      |
| tgr/jane               | tgr/jane/v2                     |

### Squeezing space out of AVR ([#15243](https://github.com/qmk/qmk_firmware/pull/15243)) {#squeezing-space-from-avr}

The AVR platform has been problematic for some time, in the sense that it is severely resource-constrained -- this makes life difficult for anyone attempting to add new functionality such as display panels to their keymap code. The illustrious Drashna has contributed some newer documentation on how to attempt to free up some space on AVR-based keyboards that are in short supply.

Of course, there are much fewer constraints with ARM chips... ;)

### Require explicit enabling of RGB Matrix modes ([#15018](https://github.com/qmk/qmk_firmware/pull/15018)) {#explicit-rgb-modes}

Related to the previous section -- RGB Matrix modes have now been made to be opt-in, rather than opt-out. As these animations are now opt-in, you may find that your keyboard no longer has all the RGB modes you're expecting -- you may need to configure and recompile your firmware and enable your animations of choice... with any luck they'll still fit in the space available.

Most keyboards keep their original functionality, but over time the QMK maintainers have found that removal of animations ends up being the quickest way to free up space... and some keyboards have had animations such as reactive effects disabled by default in order to still fit within the flash space available.

The full list of configurables to turn specific animations back on can be found at on the [RGB Matrix documentation](../features/rgb_matrix#rgb-matrix-effects) page.

### OLED task refactoring ([#14864](https://github.com/qmk/qmk_firmware/pull/14864)) {#oled-task-refactor}

OLED display code was traditionally difficult to override in keymaps as they did not follow the standard pattern of `bool *_kb()` deferring to `bool *_user()` functions, allowing signalling to the higher level that processing had already been done.

This changes the standard OLED drawing function model to allow for a base implementation to be provided by a keyboard, but also still allow for keymap-level overrides without needing to modify the keyboard's code.

The old keymap code went something like this:

```c
void oled_task_user(void) {
    // keymap drawing code
}
```

...but the new keymap code looks like this:
```c
bool oled_task_user(void) {
    // keymap drawing code
    return false;
}
```

Keyboard designers should now structure their keyboard-level drawing routines like the following, in order to allow for keymap overrides:

```c
bool oled_task_kb(void) {
    // Defer to the keymap if they want to override
    if(!oled_task_user()) { return false; }

    // default keyboard drawing code
    return false;
}
```

### Bootmagic Full Removal ([#15002](https://github.com/qmk/qmk_firmware/pull/15002)) {#bootmagic-full-removal}

As noted during previous breaking changes cycles, QMK decided to deprecate the full Bootmagic feature and leave Bootmagic Lite as the only remaining option.

This removal is now complete!

This pull request changes the behavior of `BOOTMAGIC_ENABLE` such that specifying `lite` or `full` results in an error, allowing only `yes` or `no`, with `yes` mirroring historical `lite` functionality.

All use of the `lite` keyword within the repository has been migrated to `yes` -- any new submissions using `lite` will now fail to build and should be updated accordingly.

#### Bootmagic Full Deprecation Schedule: Complete!

This is the historical timeline for the behavior of `BOOTMAGIC_ENABLE`:

- (done) From 2021 May 29, setting `BOOTMAGIC_ENABLE = yes` will enable Bootmagic Lite instead of full Bootmagic.
- (done) From 2021 Aug 28, `BOOTMAGIC_ENABLE` must be either `yes`, `lite`, or `no` – setting `BOOTMAGIC_ENABLE = full` will cause compilation to fail.
- (now) From 2021 Nov 27, `BOOTMAGIC_ENABLE` must be either `yes` or `no` – setting `BOOTMAGIC_ENABLE = lite` will cause compilation to fail.

### Remove QWIIC_DRIVERS ([#14174](https://github.com/qmk/qmk_firmware/pull/14174)) {#remove-qwiic}

Due to minimal QWIIC adoption and other options for similar functionality, the QWIIC drivers were removed from QMK. Existing OLED usages have been migrated across to the normal QMK OLED driver instead.

## Notable core changes {#notable-core}

### New MCU Support {#new-mcu-support}

QMK firmware picked up support for a handful of new MCU families, potentially making it a bit easier to source components.

QMK firmware is now no longer limited to AVR and ARM - it also picked up support for our first RISC-V chip, the GD32VF103.

* Add support for RISC-V builds and GD32VF103 MCU ([#12508](https://github.com/qmk/qmk_firmware/pull/12508))
* Add HT32 support to core ([#14388](https://github.com/qmk/qmk_firmware/pull/14388))
* Westberrytech pr ([#14422](https://github.com/qmk/qmk_firmware/pull/14422))
* Initial pass of F405 support ([#14584](https://github.com/qmk/qmk_firmware/pull/14584))

### EEPROM Changes {#eeprom-changes}

There were a few EEPROM-related changes that landed during this breaking changes cycle, most prominently the long-awaited ability for the Drop boards to gain persistent storage. Any users of the Drop CTRL or Drop ALT should update QMK Toolbox as well -- coupled with a QMK firmware update settings should now be saved.

* massdrop alt/ctrl: support saving into nvm ([#6068](https://github.com/qmk/qmk_firmware/pull/6068))
* Implement F4 eeprom ([#14195](https://github.com/qmk/qmk_firmware/pull/14195))
* make the full 4096 bytes of EEPROM work on Teensy 3.6 ([#12947](https://github.com/qmk/qmk_firmware/pull/12947))
* Further tidy up of STM32 eeprom emulation ([#14591](https://github.com/qmk/qmk_firmware/pull/14591))
* Enable eeprom with F401xE ld ([#14752](https://github.com/qmk/qmk_firmware/pull/14752))

### Compilation Database {#compile-commands}

A clang-compatible compilation database generator has been added as an option in order to help development environments such as Visual Studio Code.

Running `qmk generate-compilation-database -kb <yourkb> -km <yourkeymap>` from within the QMK firmware directory will generate a `compile_commands.json` file -- using a compatible IDE will likely see this and correctly start detecting the correct locations for source files as well as type and function information that are relevant to your build.

Do note that switching keyboards will require re-generation of this file.

* New CLI subcommand to create clang-compatible compilation database (`compile_commands.json`) ([#14370](https://github.com/qmk/qmk_firmware/pull/14370))
* compiledb: query include paths from gcc directly. ([#14462](https://github.com/qmk/qmk_firmware/pull/14462))

### Codebase restructure and cleanup {#codebase-restructure}

QMK continues on its restructuring journey, in order to make it easier to integrate newer features and add support for new hardware. This quarter's batch of changes include:

* add 'include keyboard_features.mk' into build_keyboard.mk ([#8422](https://github.com/qmk/qmk_firmware/pull/8422))
* Infer more when building features ([#13890](https://github.com/qmk/qmk_firmware/pull/13890))
* Move `tmk_core/common/<plat>` ([#13918](https://github.com/qmk/qmk_firmware/pull/13918))
* Move feature suspend logic out of platform specific code ([#14210](https://github.com/qmk/qmk_firmware/pull/14210))
* Remove bin/qmk ([#14231](https://github.com/qmk/qmk_firmware/pull/14231))
* Move Audio drivers from quantum to platform drivers folder ([#14308](https://github.com/qmk/qmk_firmware/pull/14308))
* Remove Arduino-style `analogRead()` ([#14348](https://github.com/qmk/qmk_firmware/pull/14348))
* Remove unreferenced IBM4704, Sony NEWS, NeXT keyboard code. ([#14380](https://github.com/qmk/qmk_firmware/pull/14380))
* Move Bluetooth config to common_features.mk ([#14404](https://github.com/qmk/qmk_firmware/pull/14404))
* Relocate Adafruit BLE code ([#14530](https://github.com/qmk/qmk_firmware/pull/14530))
* Change `MK66F18` -> `MK66FX1M0` ([#14659](https://github.com/qmk/qmk_firmware/pull/14659))
* Remove sysex API ([#14723](https://github.com/qmk/qmk_firmware/pull/14723))
* Basic keycode overhaul ([#14726](https://github.com/qmk/qmk_firmware/pull/14726))
* Remove SERIAL_LINK feature ([#14727](https://github.com/qmk/qmk_firmware/pull/14727))
* Move converter specific tmk_core protocols ([#14743](https://github.com/qmk/qmk_firmware/pull/14743))
* Align PS/2 GPIO defines ([#14745](https://github.com/qmk/qmk_firmware/pull/14745))
* Clean up LED/RGB Matrix driver config ([#14760](https://github.com/qmk/qmk_firmware/pull/14760))
* Update UART driver API ([#14839](https://github.com/qmk/qmk_firmware/pull/14839))
* Tidy up LCD_ENABLE/visualizer references ([#14855](https://github.com/qmk/qmk_firmware/pull/14855))
* Remove legacy Makefile functionality ([#14858](https://github.com/qmk/qmk_firmware/pull/14858))
* Begin to carve out platform/protocol API - Migrate keyboard_* calls ([#14888](https://github.com/qmk/qmk_firmware/pull/14888))
* Rename platform SRC variable ([#14894](https://github.com/qmk/qmk_firmware/pull/14894))
* Relocate PS2 code ([#14895](https://github.com/qmk/qmk_firmware/pull/14895))
* Move USE_CCACHE logic to common location ([#14899](https://github.com/qmk/qmk_firmware/pull/14899))
* Migrate makefile utilities to sub-directory ([#14917](https://github.com/qmk/qmk_firmware/pull/14917))
* Remove SERIAL_MOUSE ([#14969](https://github.com/qmk/qmk_firmware/pull/14969))
* Relocate protocol files within tmk_core/common/ ([#14972](https://github.com/qmk/qmk_firmware/pull/14972))
* More platform/protocol alignment ([#14976](https://github.com/qmk/qmk_firmware/pull/14976))
* Fix uart function prototypes ([#15162](https://github.com/qmk/qmk_firmware/pull/15162))
* Remove deprecated KEYMAP alias ([#15037](https://github.com/qmk/qmk_firmware/pull/15037))
* Move non-assignment code to post_rules.mk ([#14207](https://github.com/qmk/qmk_firmware/pull/14207))
* Helix use `post_rules.mk` ([#14216](https://github.com/qmk/qmk_firmware/pull/14216))
* Make ChibiOS PAL interactions less STM32 specific - Round 2 ([#14456](https://github.com/qmk/qmk_firmware/pull/14456))

---

## Full changelist

Core:
* massdrop alt/ctrl: support saving into nvm ([#6068](https://github.com/qmk/qmk_firmware/pull/6068))
* Made AVR backlight pwm resolution configurable ([#7521](https://github.com/qmk/qmk_firmware/pull/7521))
* add 'include keyboard_features.mk' into build_keyboard.mk ([#8422](https://github.com/qmk/qmk_firmware/pull/8422))
* New feature: `DYNAMIC_TAPPING_TERM_ENABLE` ([#11036](https://github.com/qmk/qmk_firmware/pull/11036))
* Add Retro Shift (Auto Shift for Tap Hold via Retro Tapping) and Custom Auto Shifts ([#11059](https://github.com/qmk/qmk_firmware/pull/11059))
* Add support for RISC-V builds and GD32VF103 MCU ([#12508](https://github.com/qmk/qmk_firmware/pull/12508))
* Add Fractal RGB matrix effects ([#12670](https://github.com/qmk/qmk_firmware/pull/12670))
* Added power tracking api ([#12691](https://github.com/qmk/qmk_firmware/pull/12691))
* haptic: Feature to disable it when usb port is not configured or suspended. ([#12692](https://github.com/qmk/qmk_firmware/pull/12692))
* make the full 4096 bytes of EEPROM work on Teensy 3.6 ([#12947](https://github.com/qmk/qmk_firmware/pull/12947))
* Add Support for USB programmable buttons ([#12950](https://github.com/qmk/qmk_firmware/pull/12950))
* [Tests] Increase QMK test coverage ([#13789](https://github.com/qmk/qmk_firmware/pull/13789))
* Add support for ISSI drivers on both sides of a split keyboard ([#13842](https://github.com/qmk/qmk_firmware/pull/13842))
* Infer more when building features ([#13890](https://github.com/qmk/qmk_firmware/pull/13890))
* Reimplements WPM feature to be smaller & precise ([#13902](https://github.com/qmk/qmk_firmware/pull/13902))
* Move `tmk_core/common/<plat>` ([#13918](https://github.com/qmk/qmk_firmware/pull/13918))
* Improvements to handling of disconnected split keyboards. ([#14033](https://github.com/qmk/qmk_firmware/pull/14033))
* Add Pixel Rain RGB Matrix effect ([#14155](https://github.com/qmk/qmk_firmware/pull/14155))
* Remove QWIIC_DRIVERS ([#14174](https://github.com/qmk/qmk_firmware/pull/14174))
* Add LM() keys to the list of keys disabled by NO_HAPTIC_MOD ([#14181](https://github.com/qmk/qmk_firmware/pull/14181))
* Implement F4 eeprom ([#14195](https://github.com/qmk/qmk_firmware/pull/14195))
* define to AUTO_SHIFT_DISABLED_AT_STARTUP ([#14201](https://github.com/qmk/qmk_firmware/pull/14201))
* Move feature suspend logic out of platform specific code ([#14210](https://github.com/qmk/qmk_firmware/pull/14210))
* Remove bin/qmk ([#14231](https://github.com/qmk/qmk_firmware/pull/14231))
* Change keyboard level include guards to `pragma once` ([#14248](https://github.com/qmk/qmk_firmware/pull/14248))
* i2c_master: Add support for reading/writing to 16-bit registers ([#14289](https://github.com/qmk/qmk_firmware/pull/14289))
* Move Audio drivers from quantum to platform drivers folder ([#14308](https://github.com/qmk/qmk_firmware/pull/14308))
* Add RGBW support to PWM and SPI drivers for ChibiOS ([#14327](https://github.com/qmk/qmk_firmware/pull/14327))
* Rework and expand Pointing Device support ([#14343](https://github.com/qmk/qmk_firmware/pull/14343))
* Remove Arduino-style `analogRead()` ([#14348](https://github.com/qmk/qmk_firmware/pull/14348))
* Macros in JSON keymaps ([#14374](https://github.com/qmk/qmk_firmware/pull/14374))
* Remove unreferenced IBM4704, Sony NEWS, NeXT keyboard code. ([#14380](https://github.com/qmk/qmk_firmware/pull/14380))
* Add HT32 support to core ([#14388](https://github.com/qmk/qmk_firmware/pull/14388))
* Align ChibiOS I2C defs with other drivers ([#14399](https://github.com/qmk/qmk_firmware/pull/14399))
* Move Bluetooth config to common_features.mk ([#14404](https://github.com/qmk/qmk_firmware/pull/14404))
* Westberrytech pr ([#14422](https://github.com/qmk/qmk_firmware/pull/14422))
* Refactor use of STM32_SYSCLK ([#14430](https://github.com/qmk/qmk_firmware/pull/14430))
* Migrate STM32_EEPROM_ENABLE to use EEPROM_DRIVER ([#14433](https://github.com/qmk/qmk_firmware/pull/14433))
* Refactor use of _STM32_ defines ([#14439](https://github.com/qmk/qmk_firmware/pull/14439))
* Add i2c defaults for Convert to Proton C ([#14470](https://github.com/qmk/qmk_firmware/pull/14470))
* Use opendrain pin with external pullup again ([#14474](https://github.com/qmk/qmk_firmware/pull/14474))
* Add ability to use numpad digits for unicode mode UC_WIN ([#14496](https://github.com/qmk/qmk_firmware/pull/14496))
* Enable de-ghosting for RGB/LED matrix on all ISSI LED drivers ([#14508](https://github.com/qmk/qmk_firmware/pull/14508))
* Relocate Adafruit BLE code ([#14530](https://github.com/qmk/qmk_firmware/pull/14530))
* Initial pass of F405 support ([#14584](https://github.com/qmk/qmk_firmware/pull/14584))
* Further tidy up of STM32 eeprom emulation ([#14591](https://github.com/qmk/qmk_firmware/pull/14591))
* Remove GCC version check from song list inclusion ([#14600](https://github.com/qmk/qmk_firmware/pull/14600))
* Change `MK66F18` -> `MK66FX1M0` ([#14659](https://github.com/qmk/qmk_firmware/pull/14659))
* Add ifndef to WS2812 timing constraints ([#14678](https://github.com/qmk/qmk_firmware/pull/14678))
* Reuse of EEPROM debounce logic ([#14699](https://github.com/qmk/qmk_firmware/pull/14699))
* Remove sysex API ([#14723](https://github.com/qmk/qmk_firmware/pull/14723))
* Basic keycode overhaul ([#14726](https://github.com/qmk/qmk_firmware/pull/14726))
* Remove SERIAL_LINK feature ([#14727](https://github.com/qmk/qmk_firmware/pull/14727))
* Enable CLI flashing via mdloader ([#14729](https://github.com/qmk/qmk_firmware/pull/14729))
* Correct the Turkish F '?' keycode (TR_QUES) ([#14740](https://github.com/qmk/qmk_firmware/pull/14740))
* Move converter specific tmk_core protocols ([#14743](https://github.com/qmk/qmk_firmware/pull/14743))
* Align PS/2 GPIO defines ([#14745](https://github.com/qmk/qmk_firmware/pull/14745))
* Improve Adafruit BLE configuration defines ([#14749](https://github.com/qmk/qmk_firmware/pull/14749))
* Enable eeprom with F401xE ld ([#14752](https://github.com/qmk/qmk_firmware/pull/14752))
* Clean up LED/RGB Matrix driver config ([#14760](https://github.com/qmk/qmk_firmware/pull/14760))
* Initial USB2422 driver ([#14835](https://github.com/qmk/qmk_firmware/pull/14835))
* Update UART driver API ([#14839](https://github.com/qmk/qmk_firmware/pull/14839))
* Split out arm_atsam shift register logic ([#14848](https://github.com/qmk/qmk_firmware/pull/14848))
* Split out HAPTIC_ENABLE to have separate DRIVER option ([#14854](https://github.com/qmk/qmk_firmware/pull/14854))
* Tidy up LCD_ENABLE/visualizer references ([#14855](https://github.com/qmk/qmk_firmware/pull/14855))
* Remove legacy Makefile functionality ([#14858](https://github.com/qmk/qmk_firmware/pull/14858))
* Add support for deferred executors. ([#14859](https://github.com/qmk/qmk_firmware/pull/14859))
* Change OLED task function to be boolean ([#14864](https://github.com/qmk/qmk_firmware/pull/14864))
* Add a new led driver for Keychron's keyboards. ([#14872](https://github.com/qmk/qmk_firmware/pull/14872))
* Begin to carve out platform/protocol API - Migrate keyboard_* calls ([#14888](https://github.com/qmk/qmk_firmware/pull/14888))
* Rename platform SRC variable ([#14894](https://github.com/qmk/qmk_firmware/pull/14894))
* Relocate PS2 code ([#14895](https://github.com/qmk/qmk_firmware/pull/14895))
* Move USE_CCACHE logic to common location ([#14899](https://github.com/qmk/qmk_firmware/pull/14899))
* Migrate makefile utilities to sub-directory ([#14917](https://github.com/qmk/qmk_firmware/pull/14917))
* Remove legacy handling for ErgoDox Infinity handedness ([#14919](https://github.com/qmk/qmk_firmware/pull/14919))
* Align usbasp flashing behaviour ([#14928](https://github.com/qmk/qmk_firmware/pull/14928))
* Optimize matrix scanning by removing variable shifts ([#14947](https://github.com/qmk/qmk_firmware/pull/14947))
* Stop-gap forward-port Drop LED features for CTRL and ALT ([#14967](https://github.com/qmk/qmk_firmware/pull/14967))
* Remove SERIAL_MOUSE ([#14969](https://github.com/qmk/qmk_firmware/pull/14969))
* Relocate protocol files within tmk_core/common/ ([#14972](https://github.com/qmk/qmk_firmware/pull/14972))
* Move LTO logic from common.mk ([#14973](https://github.com/qmk/qmk_firmware/pull/14973))
* More platform/protocol alignment ([#14976](https://github.com/qmk/qmk_firmware/pull/14976))
* Add support to persist MD LED framework settings ([#14980](https://github.com/qmk/qmk_firmware/pull/14980))
* Enable configuration of PWM frequency for IS31FL3733B ([#14983](https://github.com/qmk/qmk_firmware/pull/14983))
* Remove `BOOTMAGIC_ENABLE = lite` option ([#15002](https://github.com/qmk/qmk_firmware/pull/15002))
* Manually format develop ([#15003](https://github.com/qmk/qmk_firmware/pull/15003))
* Require explicit enabling of RGB Matrix modes ([#15018](https://github.com/qmk/qmk_firmware/pull/15018))
* Remove deprecated KEYMAP alias ([#15037](https://github.com/qmk/qmk_firmware/pull/15037))
* Fix uart function prototypes ([#15162](https://github.com/qmk/qmk_firmware/pull/15162))
* Rename RGB fractal ([#15174](https://github.com/qmk/qmk_firmware/pull/15174))
* Format code according to conventions ([#15195](https://github.com/qmk/qmk_firmware/pull/15195))
* Format code according to conventions ([#15196](https://github.com/qmk/qmk_firmware/pull/15196))
* Add uint to char functions ([#15244](https://github.com/qmk/qmk_firmware/pull/15244))
* [Tests] Increase QMK test coverage take 2 ([#15269](https://github.com/qmk/qmk_firmware/pull/15269))
* Tidy up adjustable ws2812 timing ([#15299](https://github.com/qmk/qmk_firmware/pull/15299))
* Add script for performing compilation size regression investigations. ([#15303](https://github.com/qmk/qmk_firmware/pull/15303))
* WB32F3G71 config migration with removal of unnecessary items. ([#15309](https://github.com/qmk/qmk_firmware/pull/15309))
* Re-add encoder tests ([#15312](https://github.com/qmk/qmk_firmware/pull/15312))

CLI:
* Add check for non-assignment code in rules.mk ([#12108](https://github.com/qmk/qmk_firmware/pull/12108))
* Export list of `develop` PRs to be merged into `master` ([#13944](https://github.com/qmk/qmk_firmware/pull/13944))
* remove qmk console, which is now part of the global cli ([#14206](https://github.com/qmk/qmk_firmware/pull/14206))
* New CLI subcommand to create clang-compatible compilation database (`compile_commands.json`) ([#14370](https://github.com/qmk/qmk_firmware/pull/14370))
* compiledb: query include paths from gcc directly. ([#14462](https://github.com/qmk/qmk_firmware/pull/14462))

Submodule updates:
* Update to ChibiOS 20.3.4, support builds against trunk ([#14208](https://github.com/qmk/qmk_firmware/pull/14208))
* Update ChibiOS-Contrib ([#14408](https://github.com/qmk/qmk_firmware/pull/14408))
* Update ChibiOS-Contrib ([#14419](https://github.com/qmk/qmk_firmware/pull/14419))
* Purge uGFX. ([#14720](https://github.com/qmk/qmk_firmware/pull/14720))

Keyboards:
* Add support for PaladinPad, Arya pcb and move keyboards by KapCave into their own directory ([#14194](https://github.com/qmk/qmk_firmware/pull/14194))
* Move non-assignment code to post_rules.mk ([#14207](https://github.com/qmk/qmk_firmware/pull/14207))
* Helix use `post_rules.mk` ([#14216](https://github.com/qmk/qmk_firmware/pull/14216))
* handwired/symmetric70_proto use post_rules.mk ([#14235](https://github.com/qmk/qmk_firmware/pull/14235))
* Add Adelais PCB. Adelais RGB rev.3, Adelais rev. 4 APM32F103, Adelais AVR rev. 1 ([#14252](https://github.com/qmk/qmk_firmware/pull/14252))
* GMMK Pro keymap ([#14389](https://github.com/qmk/qmk_firmware/pull/14389))
* Migrate boston_meetup/2019 away from QWIIC_DRIVERS ([#14413](https://github.com/qmk/qmk_firmware/pull/14413))
* Migrate hadron away from QWIIC_DRIVERS ([#14415](https://github.com/qmk/qmk_firmware/pull/14415))
* Enable Proton C defaults for SplitKB Kyria ([#14490](https://github.com/qmk/qmk_firmware/pull/14490))
* Set USB max power consumption of kint* controllers to 100mA ([#14546](https://github.com/qmk/qmk_firmware/pull/14546))
* Remove complex `fn_actions` macros ([#14662](https://github.com/qmk/qmk_firmware/pull/14662))
* New Keyboard: TGR Jane CE ([#14713](https://github.com/qmk/qmk_firmware/pull/14713))
* Migrate satisfaction75 away from QWIIC_DRIVERS ([#14747](https://github.com/qmk/qmk_firmware/pull/14747))
* add Lefty keyboard ([#14898](https://github.com/qmk/qmk_firmware/pull/14898))
* overnumpad controller: Add support for turning off solenoid enable in low power. ([#15021](https://github.com/qmk/qmk_firmware/pull/15021))
* Reduce compile size for melgeek mach80 ([#15034](https://github.com/qmk/qmk_firmware/pull/15034))
* Update updated KPrepublic boards to be prepared for the update ([#15040](https://github.com/qmk/qmk_firmware/pull/15040))
* rename kprepublic bm keyboards to have a standardized naming format ([#15047](https://github.com/qmk/qmk_firmware/pull/15047))
* matrix/abelx - Update ChibiOS conf files ([#15130](https://github.com/qmk/qmk_firmware/pull/15130))
* Disable console on Keebio foldkb and iris rev3 ([#15260](https://github.com/qmk/qmk_firmware/pull/15260))
* Disable console on Sofle default keymap ([#15261](https://github.com/qmk/qmk_firmware/pull/15261))
* Disable features on SplitKB boards to fit under size ([#15262](https://github.com/qmk/qmk_firmware/pull/15262))
* Enable LTO on viktus/sp_mini via keymap ([#15263](https://github.com/qmk/qmk_firmware/pull/15263))

Keyboard fixes:
* Fix number of elements in info.json does not match errors ([#14213](https://github.com/qmk/qmk_firmware/pull/14213))
* Fix typos from 14248 ([#14261](https://github.com/qmk/qmk_firmware/pull/14261))
* Stream cheap via fixes/updates ([#14325](https://github.com/qmk/qmk_firmware/pull/14325))
* Map `PRODUCT` define to `keyboard_name` ([#14372](https://github.com/qmk/qmk_firmware/pull/14372))
* Fix BT rules for dosa40rgb ([#14497](https://github.com/qmk/qmk_firmware/pull/14497))
* Fix typo in mechloving adelais header files ([#14590](https://github.com/qmk/qmk_firmware/pull/14590))
* Fix for mechlovin/adelais/standard_led/arm/rev4 ([#14639](https://github.com/qmk/qmk_firmware/pull/14639))
* Fix OLED timeout on recent qwiic migrations ([#14775](https://github.com/qmk/qmk_firmware/pull/14775))
* Fix OLED timeout on satisfaction75 after migration from QWIIC ([#14780](https://github.com/qmk/qmk_firmware/pull/14780))
* Fix Compile issues for lefty ([#14982](https://github.com/qmk/qmk_firmware/pull/14982))
* Fix missing return for oled task on Lefty ([#15010](https://github.com/qmk/qmk_firmware/pull/15010))
* Fix missing return for oled task on Arabica37 ([#15011](https://github.com/qmk/qmk_firmware/pull/15011))
* Fix missing return for oled task in drashna userspace ([#15012](https://github.com/qmk/qmk_firmware/pull/15012))
* Fix size issues on pistachio pro via keymap ([#15017](https://github.com/qmk/qmk_firmware/pull/15017))
* Fix keycode collision in craftwalk keymap ([#15055](https://github.com/qmk/qmk_firmware/pull/15055))
* Fix compilation issues for yanghu Unicorne ([#15068](https://github.com/qmk/qmk_firmware/pull/15068))
* Fixup broken build after #15040 ([#15073](https://github.com/qmk/qmk_firmware/pull/15073))
* Fix compilation issues for Lime ([#15116](https://github.com/qmk/qmk_firmware/pull/15116))
* Fix additional board sizes for RGB Matrix ([#15170](https://github.com/qmk/qmk_firmware/pull/15170))
* Fix bandominedoni via keymap compilation ([#15171](https://github.com/qmk/qmk_firmware/pull/15171))
* Fix handful of boards compiling too large due to RGB matrix changes ([#15184](https://github.com/qmk/qmk_firmware/pull/15184))
* Fix oled_task_user for ffkeebs/puca ([#15185](https://github.com/qmk/qmk_firmware/pull/15185))
* More headroom. ([#15301](https://github.com/qmk/qmk_firmware/pull/15301))
* More headroom. ([#15302](https://github.com/qmk/qmk_firmware/pull/15302))

Others:
* Clean up some code block languages ([#14434](https://github.com/qmk/qmk_firmware/pull/14434))
* Clarify "nested" and "rolling" key sequences ([#14655](https://github.com/qmk/qmk_firmware/pull/14655))
* CI: Create GitHub Actions unit test workflow ([#15223](https://github.com/qmk/qmk_firmware/pull/15223))
* Squeezing space out of AVR ([#15243](https://github.com/qmk/qmk_firmware/pull/15243))

Bugs:
* Fix parallel builds w/ LTO on systems where make is not GNU make. ([#13955](https://github.com/qmk/qmk_firmware/pull/13955))
* fix automatic directory for qmk lint ([#14215](https://github.com/qmk/qmk_firmware/pull/14215))
* RN42 Bluetooth typo fix ([#14421](https://github.com/qmk/qmk_firmware/pull/14421))
* fix typo in backlight code from #14439 ([#14442](https://github.com/qmk/qmk_firmware/pull/14442))
* fix compilation issues with USB programmable buttons ([#14454](https://github.com/qmk/qmk_firmware/pull/14454))
* Fix descriptor for USB Programmable Buttons ([#14455](https://github.com/qmk/qmk_firmware/pull/14455))
* Make ChibiOS PAL interactions less STM32 specific - Round 2 ([#14456](https://github.com/qmk/qmk_firmware/pull/14456))
* fix logical minimum in Programmable Button rdesc ([#14464](https://github.com/qmk/qmk_firmware/pull/14464))
* Fix i2c_readReg16 ([#14730](https://github.com/qmk/qmk_firmware/pull/14730))
* Put back eeconfig_update_ functions ([#14751](https://github.com/qmk/qmk_firmware/pull/14751))
* Fix misplaced endif in led_matrix_drivers.c ([#14785](https://github.com/qmk/qmk_firmware/pull/14785))
* Fix builds for ChibiOS + Cortex-M0[+] ([#14879](https://github.com/qmk/qmk_firmware/pull/14879))
* Fix ccache default ([#14906](https://github.com/qmk/qmk_firmware/pull/14906))
* Fix issues with Oneshot disabling ([#14934](https://github.com/qmk/qmk_firmware/pull/14934))
* Fix develop after recent changes ([#14975](https://github.com/qmk/qmk_firmware/pull/14975))
* Fix up issues shown by clang-format of vusb ([#15004](https://github.com/qmk/qmk_firmware/pull/15004))
* Fix unterminated ifdef in ISSI 3733 driver ([#15014](https://github.com/qmk/qmk_firmware/pull/15014))
* Fix build failures caused by #12947. ([#15019](https://github.com/qmk/qmk_firmware/pull/15019))
* Fixup LED matrix. ([#15020](https://github.com/qmk/qmk_firmware/pull/15020))
* Revert to old init order for host driver ([#15029](https://github.com/qmk/qmk_firmware/pull/15029))
* Fixup #15029 ([#15031](https://github.com/qmk/qmk_firmware/pull/15031))
* RISC-V toolchain and picolibc fixes ([#15109](https://github.com/qmk/qmk_firmware/pull/15109))
* gcc10 LTO - Only specify adhlns assembler options at link time ([#15115](https://github.com/qmk/qmk_firmware/pull/15115))
* Add needed include to pointing_device.c ([#15167](https://github.com/qmk/qmk_firmware/pull/15167))
* Fix missing variable for Backlight Breathing ([#15199](https://github.com/qmk/qmk_firmware/pull/15199))
* Revert backlight pins on function call ([#15205](https://github.com/qmk/qmk_firmware/pull/15205))
* Fix timer include in override_wiring.c ([#15221](https://github.com/qmk/qmk_firmware/pull/15221))
* fix broken macro in transport.h ([#15239](https://github.com/qmk/qmk_firmware/pull/15239))
* Short term bodge for PRODUCT warning ([#15240](https://github.com/qmk/qmk_firmware/pull/15240))
* Remove use of __flash due to LTO issues ([#15268](https://github.com/qmk/qmk_firmware/pull/15268))
* Documentation typo fix ([#15298](https://github.com/qmk/qmk_firmware/pull/15298))
* [Core] Hotfix for HOLD_ON_OTHER_KEY_PRESS after #11059 ([#15307](https://github.com/qmk/qmk_firmware/pull/15307))
* Fix call to pointing_device_handle_buttons ([#15313](https://github.com/qmk/qmk_firmware/pull/15313))
* [Develop] Fix ploopy readme typos ([#15316](https://github.com/qmk/qmk_firmware/pull/15316))
