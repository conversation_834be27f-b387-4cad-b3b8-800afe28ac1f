# QMK Breaking Changes - 2024 May 26 Changelog

## Notable Features {#notable-features}

May 2024 brings about another heavy maintenance release of QMK. Of the 209 PRs created this breaking changes cycle against the `develop` branch, 174 behind-the-scenes PRs (83%!) were aimed at converting, consolidating, and cleaning up keyboards and their configuration data. Not the most glamorous work, but it means QMK is in a much more manageable spot than what it was 3 months prior. The work steadily continues!

## Changes Requiring User Action {#changes-requiring-user-action}

### Updated Keyboard Codebases {#updated-keyboard-codebases}

One note with updated keyboard names -- historical keyboard names are still considered valid when using [External Userspace](../newbs_external_userspace) for builds. If you're already using External Userspace, you do not need to move your keymap inside your repository.

| Old Keyboard Name            | New Keyboard Name                 |
|------------------------------|-----------------------------------|
| adkb96                       | adkb96/rev1                       |
| canary/canary60rgb           | canary/canary60rgb/v1             |
| handwired/meck_tkl           | handwired/meck_tkl/blackpill_f401 |
| handwired/qc60               | handwired/qc60/proto              |
| handwired/stef9998/split_5x7 | handwired/stef9998/split_5x7/rev1 |
| junco                        | junco/rev1                        |
| keaboard                     | keaboard/rev1                     |
| kprepublic/jj40              | kprepublic/jj40/rev1              |
| kprepublic/jj50              | kprepublic/jj50/rev1              |
| melgeek/mj65                 | melgeek/mj65/rev3                 |
| melgeek/mojo68               | melgeek/mojo68/rev1               |
| melgeek/mojo75               | melgeek/mojo75/rev1               |
| melgeek/tegic                | melgeek/tegic/rev1                |
| melgeek/z70ultra             | melgeek/z70ultra/rev1             |
| miiiw/blackio83              | miiiw/blackio83/rev_0100          |
| murcielago                   | murcielago/rev1                   |
| polilla                      | polilla/rev1                      |
| qwertyydox                   | qwertyydox/rev1                   |
| spaceholdings/nebula68b      | spaceholdings/nebula68b/solder    |
| splitty                      | splitty/rev1                      |
| xiudi/xd004                  | xiudi/xd004/v1                    |

### Remove deprecated quantum keycodes ([#23407](https://github.com/qmk/qmk_firmware/pull/23407))

A bunch of legacy keycodes have been removed -- check [the affected keycodes](https://github.com/qmk/qmk_firmware/blob/70e34e491c297231a3f987fd69760d38e79dbfa4/quantum/quantum_keycodes_legacy.h) if you run into compilation problems, as it'll show you what the problematic keycodes should be replaced with.

The latest of these were officially deprecated within QMK in the August 2023 breaking changes -- the new keycodes are the way forward.

### P3D Spacey Layout Updates ([#23329](https://github.com/qmk/qmk_firmware/pull/23329)) {#spacey-layout-updates}

This PR removed the `LAYOUT` macro that was configured for the Spacey.
If you have a keymap for this keyboard, you will need to update your
keymap using the following steps:

1. Change your layout macro to `LAYOUT_all`.
2. Remove the two `KC_NO` keycodes following the Space and Delete keys
   on the bottom row.
3. Move the keycode for the encoder pushbutton (customarily Mute) to the
   end of the top row, after the customary Backspace key.
4. Move the keycode for the Right Arrow to the end of the Shift row,
   after the Down Arrow key.

### MechKeys ACR60 Layout Updates ([#23309](https://github.com/qmk/qmk_firmware/pull/23309)) {#acr60-layout-updates}

This PR removed and changed some of the layouts that were configured for the ACR60. If you use one of the following layouts, you will need to update your keymap:

- [`LAYOUT_hhkb`](#layout-hhkb)
- [`LAYOUT_true_hhkb`](#layout-true-hhkb)
- [`LAYOUT_directional`](#layout-directional)
- [`LAYOUT_mitchsplit`](#layout-mitchsplit)

#### `LAYOUT_hhkb` {#acr60-layout-hhkb}

1. Change your layout macro to `LAYOUT_60_hhkb`.
1. Remove any keycodes for the key between Left Shift and QWERTY Z.

#### `LAYOUT_true_hhkb` {#acr60-layout-true-hhkb}

1. Change your layout macro to `LAYOUT_60_true_hhkb`.
1. Remove any keycodes for the key between Left Shift and QWERTY Z.

#### `LAYOUT_directional` {#acr60-layout-directional}

1. Change your layout macro to `LAYOUT_60_ansi_arrow_split_bs`.
1. Remove any keycodes for the key between Left Shift and QWERTY Z.
1. Remove any keycodes for the keys immediately before *and* after the 1.25u key of Split Spacebar.

If you need split spacebars, you may implement `LAYOUT_60_ansi_arrow_split_space_split_bs` and change your layout to it, removing the keycode between Left Shift and QWERTY Z.

#### `LAYOUT_mitchsplit` {#acr60-layout-mitchsplit}

1. Use `LAYOUT_60_ansi_split_space_split_rshift`.

## Notable core changes {#notable-core}

### Introduction of `keyboard.json` ([22891](https://github.com/qmk/qmk_firmware/pull/22891)) {#keyboard-json}

One longer term goal of QMK is increased maintainability.
As part of the continued push towards [Data Driven Configuration](../data_driven_config), the build system has been updated to simplify the existing codebase, and power future workflows.

The `keyboard.json` configuration file allows the support of a single data file for keyboard level config.

Additionally,
* `info.json` now represents potential fragments of config that can be shared across keyboard revisions.
* `rules.mk` is now optional - Completely blank files are no longer required.
* Currently supported keyboards have been migrated to reflect this change. 

Backwards compatibility of the old system has been maintained, but will be removed in a future breaking changes cycle.

### Refactor ChibiOS USB endpoints to be fully async ([#21656](https://github.com/qmk/qmk_firmware/pull/21656))

For most users, this change will mean suspend and resume on ARM-based boards works correctly. Others will notice that their keyboard now works correctly in BIOS/UEFI.

Essentially, changes were made in the internals of how QMK interacts with USB for ARM-based devices. Before this change, whenever a packet was attempted to be sent from the keyboard to the host machine, QMK would wait for the transmission to complete. After this change, those packets are queued and sent when opportune; this results in much better "correctness" as far as the USB protocol is concerned, and means far less likelihood of failure scenarios such as "stuck keys" or "random lockups" and the like.

Compliance checks were run against QMK firmwares for the most popular ARM microcontrollers, as well as suspend/resume tests. As far as we can tell, a whole host of hard-to-reproduce issues are mitigated by this change.

## Deprecation Notices

In line with the [notice period](../support_deprecation_policy#how-much-advance-notice-will-be-given), deprecation notices for larger items are listed here.

### Migration of VIA keymaps to VIA team control

The QMK team has been in discussion with the VIA maintainers and all VIA-related keymaps in the `qmk_firmware` repository will transition to a `qmk_userspace`-style repository under the VIA team's control at the end of the next breaking changes period. This allows the VIA team to support many more custom keyboard configurations, as well as reduces the turnaround time for any changes to the VIA protocol they wish to make.

At the end of the breaking changes cycle ending 2024-08-25, VIA-enabled keymaps will no longer be accepted into the QMK repository. At the time of migration, any open PRs against `qmk_firmware` which include new VIA-related keymaps will be subsequently be asked to remove those keymaps and instead raise a PR against the userspace repository containing all VIA keymaps.

## Full changelist {#full-changelist}

Core:
* Refactor vusb to protocol use pre/post task ([#14944](https://github.com/qmk/qmk_firmware/pull/14944))
* Refactor ChibiOS USB endpoints to be fully async ([#21656](https://github.com/qmk/qmk_firmware/pull/21656))
* Infer eeconfig identifiers ([#22135](https://github.com/qmk/qmk_firmware/pull/22135))
* [Audio] Add support for audio shutdown pin ([#22731](https://github.com/qmk/qmk_firmware/pull/22731))
* Enable 'keyboard.json' as a build target ([#22891](https://github.com/qmk/qmk_firmware/pull/22891))
* Remove unuseful layer_on() call ([#23055](https://github.com/qmk/qmk_firmware/pull/23055))
* Add init function to RGBLight driver struct ([#23076](https://github.com/qmk/qmk_firmware/pull/23076))
* Add utility functions for Pointing Device Auto Mouse feature ([#23144](https://github.com/qmk/qmk_firmware/pull/23144))
* Remove midi_ep_task from ChibiOS ([#23162](https://github.com/qmk/qmk_firmware/pull/23162))
* LED drivers: add support for IS31FL3236 ([#23264](https://github.com/qmk/qmk_firmware/pull/23264))
* Un-`extern` RGBLight `led[]` array ([#23322](https://github.com/qmk/qmk_firmware/pull/23322))
* Update I2C API usage in keyboard code ([#23360](https://github.com/qmk/qmk_firmware/pull/23360))
* Update GPIO expander API naming ([#23375](https://github.com/qmk/qmk_firmware/pull/23375))
* Remove deprecated quantum keycodes ([#23407](https://github.com/qmk/qmk_firmware/pull/23407))
* Add MacOS Czech ISO and ANSI keymaps #23346 ([#23412](https://github.com/qmk/qmk_firmware/pull/23412))
* Rename `process_{led,rgb}_matrix()` ([#23422](https://github.com/qmk/qmk_firmware/pull/23422))
* Separate keycode handling for LED Matrix and Backlight ([#23426](https://github.com/qmk/qmk_firmware/pull/23426))
* Add new set of keycodes for LED Matrix ([#23432](https://github.com/qmk/qmk_firmware/pull/23432))
* Oneshot locked mods split transaction ([#23434](https://github.com/qmk/qmk_firmware/pull/23434))
* Bodge consolidation. ([#23448](https://github.com/qmk/qmk_firmware/pull/23448))
* LED Matrix: replace backlight keycodes with newly added ones ([#23455](https://github.com/qmk/qmk_firmware/pull/23455))
* Add new set of keycodes for RGB Matrix ([#23463](https://github.com/qmk/qmk_firmware/pull/23463))
* Refactoring successive press() release() calls into tap_key() calls ([#23573](https://github.com/qmk/qmk_firmware/pull/23573))
* Rename `RGBW` define to `WS2812_RGBW` ([#23585](https://github.com/qmk/qmk_firmware/pull/23585))
* Normalise RGBLight (underglow) keycodes ([#23656](https://github.com/qmk/qmk_firmware/pull/23656))
* split_util: rename `usbIsActive` to `usb_bus_detected` ([#23657](https://github.com/qmk/qmk_firmware/pull/23657))
* Insert delay between shifted chars in send_string_with_delay for AVR ([#23673](https://github.com/qmk/qmk_firmware/pull/23673))
* Remove useless `LED/RGB_MATRIX_ENABLE` ifdefs ([#23726](https://github.com/qmk/qmk_firmware/pull/23726))

CLI:
* Some metadata on QGF/QFF files ([#20101](https://github.com/qmk/qmk_firmware/pull/20101))
* `qmk new-keyboard` - detach community layout when selecting "none of the above" ([#20405](https://github.com/qmk/qmk_firmware/pull/20405))
* Initial `qmk test-c` functionality ([#23038](https://github.com/qmk/qmk_firmware/pull/23038))
* Reject duplicate matrix locations in LAYOUT macros ([#23273](https://github.com/qmk/qmk_firmware/pull/23273))
* Align 'qmk lint' argument handling ([#23297](https://github.com/qmk/qmk_firmware/pull/23297))
* Produce warning if keyboard is not configured via `keyboard.json` ([#23321](https://github.com/qmk/qmk_firmware/pull/23321))

Submodule updates:
* Update ChibiOS submodules. ([#23405](https://github.com/qmk/qmk_firmware/pull/23405))

Keyboards:
* Move `SPLIT_KEYBOARD` to data driven ([#21410](https://github.com/qmk/qmk_firmware/pull/21410))
* Change to `development_board` ([#21695](https://github.com/qmk/qmk_firmware/pull/21695))
* Add solid_reactive effects for MIIIW BlackIO83 ([#22251](https://github.com/qmk/qmk_firmware/pull/22251))
* Migrate content where only parent info.json exists ([#22895](https://github.com/qmk/qmk_firmware/pull/22895))
* Remove redundant disabling of features ([#22926](https://github.com/qmk/qmk_firmware/pull/22926))
* Update ScottoAlp handwired keyboard to 12 column layout ([#22962](https://github.com/qmk/qmk_firmware/pull/22962))
* Overhaul ploopyco devices ([#22967](https://github.com/qmk/qmk_firmware/pull/22967))
* Add rp2040_ce option to lotus58 ([#23185](https://github.com/qmk/qmk_firmware/pull/23185))
* Migrate features from rules.mk to data driven - 0-9 ([#23202](https://github.com/qmk/qmk_firmware/pull/23202))
* Change default RGB effect for momokai keypads to solid white ([#23217](https://github.com/qmk/qmk_firmware/pull/23217))
* Migrate annepro2 away from custom matrix ([#23221](https://github.com/qmk/qmk_firmware/pull/23221))
* Update BAMFK-1 ([#23236](https://github.com/qmk/qmk_firmware/pull/23236))
* Migrate features from rules.mk to data driven - ABCD ([#23247](https://github.com/qmk/qmk_firmware/pull/23247))
* Migrate features from rules.mk to data driven - EFGH ([#23248](https://github.com/qmk/qmk_firmware/pull/23248))
* Remove 60_ansi_arrow_split_bs_7u_spc Community Layout ([#23259](https://github.com/qmk/qmk_firmware/pull/23259))
* Migrate features from rules.mk to data driven - IJK ([#23276](https://github.com/qmk/qmk_firmware/pull/23276))
* Migrate features from rules.mk to data driven - LMN ([#23277](https://github.com/qmk/qmk_firmware/pull/23277))
* Migrate features from rules.mk to data driven - OPQR ([#23285](https://github.com/qmk/qmk_firmware/pull/23285))
* Migrate features from rules.mk to data driven - ST ([#23286](https://github.com/qmk/qmk_firmware/pull/23286))
* Migrate features from rules.mk to data driven - UVWXYZ ([#23287](https://github.com/qmk/qmk_firmware/pull/23287))
* Swift65 Hotswap Layout Name Standardization ([#23288](https://github.com/qmk/qmk_firmware/pull/23288))
* Swift65 Solder Layout Name Standardization ([#23289](https://github.com/qmk/qmk_firmware/pull/23289))
* Migrate build target markers to keyboard.json ([#23293](https://github.com/qmk/qmk_firmware/pull/23293))
* KPRepublic JJ50 rev1 Refactor ([#23294](https://github.com/qmk/qmk_firmware/pull/23294))
* KPRepublic JJ40 rev1 Refactor ([#23299](https://github.com/qmk/qmk_firmware/pull/23299))
* Migrate features and LTO from rules.mk to data driven ([#23302](https://github.com/qmk/qmk_firmware/pull/23302))
* Add RGB lighting for the PetruziaMini ([#23305](https://github.com/qmk/qmk_firmware/pull/23305))
* Migrate features and LTO from rules.mk to data driven ([#23307](https://github.com/qmk/qmk_firmware/pull/23307))
* MechKeys ACR60 Layout Updates ([#23309](https://github.com/qmk/qmk_firmware/pull/23309))
* Remove RGBLight `led[]` references ([#23311](https://github.com/qmk/qmk_firmware/pull/23311))
* Reduce firmware size of helix/rev3 ([#23324](https://github.com/qmk/qmk_firmware/pull/23324))
* P3D Spacey Layout Updates ([#23329](https://github.com/qmk/qmk_firmware/pull/23329))
* Data-Driven Keyboard Conversions: 0-9 ([#23357](https://github.com/qmk/qmk_firmware/pull/23357))
* Update GPIO API usage in keyboard code ([#23361](https://github.com/qmk/qmk_firmware/pull/23361))
* Remove "w": 1 from keyboards/ ([#23367](https://github.com/qmk/qmk_firmware/pull/23367))
* Remove `quantum.h` includes from keyboard custom `matrix.c`s ([#23371](https://github.com/qmk/qmk_firmware/pull/23371))
* refactor: mechwild/bbs ([#23373](https://github.com/qmk/qmk_firmware/pull/23373))
* Remove 'NO_USB_STARTUP_CHECK = no' from keyboards ([#23376](https://github.com/qmk/qmk_firmware/pull/23376))
* Remove completely redundant DEFAULT_FOLDER from keyboards ([#23377](https://github.com/qmk/qmk_firmware/pull/23377))
* Miscellaneous keyboard.json migrations ([#23378](https://github.com/qmk/qmk_firmware/pull/23378))
* Data-Driven Keyboard Conversions: A ([#23379](https://github.com/qmk/qmk_firmware/pull/23379))
* refactor: flehrad/bigswitch ([#23384](https://github.com/qmk/qmk_firmware/pull/23384))
* add second encoder to matrix info of arrowmechanics/wings ([#23390](https://github.com/qmk/qmk_firmware/pull/23390))
* Change the VID and PID of the file kb38 info.json ([#23393](https://github.com/qmk/qmk_firmware/pull/23393))
* Remove `quantum.h` includes from keyboard code ([#23394](https://github.com/qmk/qmk_firmware/pull/23394))
* [ UPDATE 15PAD & 6PAD ] ([#23397](https://github.com/qmk/qmk_firmware/pull/23397))
* Remove more unnecessary `quantum.h` includes ([#23402](https://github.com/qmk/qmk_firmware/pull/23402))
* KB name change to Part.1-75-HS ([#23403](https://github.com/qmk/qmk_firmware/pull/23403))
* Tidy up keyboards/zvecr ([#23418](https://github.com/qmk/qmk_firmware/pull/23418))
* "features.split" is not a valid key ([#23419](https://github.com/qmk/qmk_firmware/pull/23419))
* Migrate build target markers to keyboard.json - YZ ([#23421](https://github.com/qmk/qmk_firmware/pull/23421))
* refactor: mechwild/waka60 ([#23423](https://github.com/qmk/qmk_firmware/pull/23423))
* Convert some AVR GPIO operations to macros ([#23424](https://github.com/qmk/qmk_firmware/pull/23424))
* Data-Driven Keyboard Conversions: B ([#23425](https://github.com/qmk/qmk_firmware/pull/23425))
* Tidy up default layer handling in keymaps ([#23436](https://github.com/qmk/qmk_firmware/pull/23436))
* Added Chapter1 ([#23452](https://github.com/qmk/qmk_firmware/pull/23452))
* Data-driven Keyboard Conversions: C ([#23453](https://github.com/qmk/qmk_firmware/pull/23453))
* Migrate build target markers to keyboard.json - X ([#23460](https://github.com/qmk/qmk_firmware/pull/23460))
* Data-Driven Keyboard Conversions: D ([#23461](https://github.com/qmk/qmk_firmware/pull/23461))
* Miscellaneous keyboard.json migrations ([#23486](https://github.com/qmk/qmk_firmware/pull/23486))
* Migrate build target markers to keyboard.json - 0AB ([#23488](https://github.com/qmk/qmk_firmware/pull/23488))
* Migrate build target markers to keyboard.json - W ([#23511](https://github.com/qmk/qmk_firmware/pull/23511))
* Data-Driven Keyboard Conversions: E ([#23512](https://github.com/qmk/qmk_firmware/pull/23512))
* Migrate build target markers to keyboard.json - TUV ([#23514](https://github.com/qmk/qmk_firmware/pull/23514))
* Migrate build target markers to keyboard.json - DE ([#23515](https://github.com/qmk/qmk_firmware/pull/23515))
* Data-Driven Keyboard Conversions: F ([#23516](https://github.com/qmk/qmk_firmware/pull/23516))
* Data-Driven Keyboard Conversions: G ([#23522](https://github.com/qmk/qmk_firmware/pull/23522))
* Data-Driven Keyboard Conversions: H, Part 1 ([#23524](https://github.com/qmk/qmk_firmware/pull/23524))
* Data-Driven Keyboard Conversions: H, Part 2 ([#23525](https://github.com/qmk/qmk_firmware/pull/23525))
* Migrate build target markers to keyboard.json - C ([#23529](https://github.com/qmk/qmk_firmware/pull/23529))
* Data-Driven Keyboard Conversions: H, Part 3 ([#23530](https://github.com/qmk/qmk_firmware/pull/23530))
* Migrate build target markers to keyboard.json - S ([#23532](https://github.com/qmk/qmk_firmware/pull/23532))
* Data-Driven Keyboard Conversions: I ([#23533](https://github.com/qmk/qmk_firmware/pull/23533))
* Migrate build target markers to keyboard.json - FG ([#23534](https://github.com/qmk/qmk_firmware/pull/23534))
* Migrate build target markers to keyboard.json - HI ([#23540](https://github.com/qmk/qmk_firmware/pull/23540))
* Remove *_SUPPORTED = yes ([#23541](https://github.com/qmk/qmk_firmware/pull/23541))
* Migrate build target markers to keyboard.json - R ([#23542](https://github.com/qmk/qmk_firmware/pull/23542))
* Data-Driven Keyboard Conversions: J ([#23547](https://github.com/qmk/qmk_firmware/pull/23547))
* Data-Driven Keyboard Conversions: K, Part 1 ([#23556](https://github.com/qmk/qmk_firmware/pull/23556))
* Tidy use of raw hid within keyboards ([#23557](https://github.com/qmk/qmk_firmware/pull/23557))
* Data-Driven Keyboard Conversions: K, Part 2 ([#23562](https://github.com/qmk/qmk_firmware/pull/23562))
* Migrate build target markers to keyboard.json - OQ ([#23564](https://github.com/qmk/qmk_firmware/pull/23564))
* Migrate build target markers to keyboard.json - P ([#23565](https://github.com/qmk/qmk_firmware/pull/23565))
* Data-Driven Keyboard Conversions: K, Part 3 ([#23566](https://github.com/qmk/qmk_firmware/pull/23566))
* Data-Driven Keyboard Conversions: K, Part 4 ([#23567](https://github.com/qmk/qmk_firmware/pull/23567))
* Data-Driven Keyboard Conversions: K, Part 5 ([#23569](https://github.com/qmk/qmk_firmware/pull/23569))
* Data-Driven Keyboard Conversions: L ([#23576](https://github.com/qmk/qmk_firmware/pull/23576))
* Migrate build target markers to keyboard.json - JK ([#23588](https://github.com/qmk/qmk_firmware/pull/23588))
* Migrate build target markers to keyboard.json - N ([#23589](https://github.com/qmk/qmk_firmware/pull/23589))
* Data-Driven Keyboard Conversions: M, Part 1 ([#23590](https://github.com/qmk/qmk_firmware/pull/23590))
* Add haptic driver to keyboard.json schema ([#23591](https://github.com/qmk/qmk_firmware/pull/23591))
* Migrate build target markers to keyboard.json - Keychron ([#23593](https://github.com/qmk/qmk_firmware/pull/23593))
* Remove RGBLIGHT_SPLIT in rules.mk ([#23599](https://github.com/qmk/qmk_firmware/pull/23599))
* Data-Driven Keyboard Conversions: M, Part 2 ([#23601](https://github.com/qmk/qmk_firmware/pull/23601))
* Align NO_SUSPEND_POWER_DOWN keyboard config ([#23606](https://github.com/qmk/qmk_firmware/pull/23606))
* Migrate build target markers to keyboard.json - L ([#23607](https://github.com/qmk/qmk_firmware/pull/23607))
* Migrate build target markers to keyboard.json - Misc ([#23609](https://github.com/qmk/qmk_firmware/pull/23609))
* Migrate build target markers to keyboard.json - Misc ([#23612](https://github.com/qmk/qmk_firmware/pull/23612))
* Data-Driven Keyboard Conversions: M, Part 3 ([#23614](https://github.com/qmk/qmk_firmware/pull/23614))
* Add audio driver to keyboard.json schema ([#23616](https://github.com/qmk/qmk_firmware/pull/23616))
* Data-Driven Keyboard Conversions: BastardKB ([#23622](https://github.com/qmk/qmk_firmware/pull/23622))
* Data-Driven Keyboard Conversions: Mechlovin ([#23624](https://github.com/qmk/qmk_firmware/pull/23624))
* Migrate build target markers to keyboard.json - BM ([#23627](https://github.com/qmk/qmk_firmware/pull/23627))
* gh80_3000 - Enable indicator LED functionality ([#23633](https://github.com/qmk/qmk_firmware/pull/23633))
* Iris keymap update ([#23635](https://github.com/qmk/qmk_firmware/pull/23635))
* Migrate build target markers to keyboard.json - Misc ([#23653](https://github.com/qmk/qmk_firmware/pull/23653))
* Add via support for craftwalk ([#23658](https://github.com/qmk/qmk_firmware/pull/23658))
* Align RGBKB keyboards to current standards ([#23663](https://github.com/qmk/qmk_firmware/pull/23663))
* Remove 'split.transport.protocol=serial_usart' ([#23668](https://github.com/qmk/qmk_firmware/pull/23668))
* Remove redundant keymap templates ([#23685](https://github.com/qmk/qmk_firmware/pull/23685))
* Change all RGB mode keycodes to short aliases ([#23691](https://github.com/qmk/qmk_firmware/pull/23691))
* Adjust keycode alignment around `QK_BOOT` ([#23697](https://github.com/qmk/qmk_firmware/pull/23697))
* Remove RGB keycodes from boards with no RGB config ([#23709](https://github.com/qmk/qmk_firmware/pull/23709))
* Miscellaneous Data-Driven Keyboard Conversions ([#23712](https://github.com/qmk/qmk_firmware/pull/23712))
* Delete trivial keymap readmes ([#23714](https://github.com/qmk/qmk_firmware/pull/23714))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: 0-9 ([#23716](https://github.com/qmk/qmk_firmware/pull/23716))
* Add media key support to Riot Pad ([#23719](https://github.com/qmk/qmk_firmware/pull/23719))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: A-C, Part 1 ([#23745](https://github.com/qmk/qmk_firmware/pull/23745))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: A-C, Part 2 ([#23746](https://github.com/qmk/qmk_firmware/pull/23746))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: A-C, Part 3 ([#23747](https://github.com/qmk/qmk_firmware/pull/23747))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: D, Part 1 ([#23749](https://github.com/qmk/qmk_firmware/pull/23749))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: D, Part 2 ([#23750](https://github.com/qmk/qmk_firmware/pull/23750))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: E ([#23751](https://github.com/qmk/qmk_firmware/pull/23751))
* Move VIA config to keymap level ([#23754](https://github.com/qmk/qmk_firmware/pull/23754))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: F ([#23757](https://github.com/qmk/qmk_firmware/pull/23757))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: G ([#23758](https://github.com/qmk/qmk_firmware/pull/23758))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: H, Part 1 ([#23759](https://github.com/qmk/qmk_firmware/pull/23759))
* Remove includes of config.h ([#23760](https://github.com/qmk/qmk_firmware/pull/23760))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: H, Part 2 ([#23762](https://github.com/qmk/qmk_firmware/pull/23762))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: H, Part 3 ([#23763](https://github.com/qmk/qmk_firmware/pull/23763))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: H, Part 4 ([#23764](https://github.com/qmk/qmk_firmware/pull/23764))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: I-J ([#23767](https://github.com/qmk/qmk_firmware/pull/23767))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: K, Part 1 ([#23768](https://github.com/qmk/qmk_firmware/pull/23768))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: K, Part 2 ([#23769](https://github.com/qmk/qmk_firmware/pull/23769))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: K, Part 3 ([#23770](https://github.com/qmk/qmk_firmware/pull/23770))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: L ([#23771](https://github.com/qmk/qmk_firmware/pull/23771))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: M, Part 1 ([#23772](https://github.com/qmk/qmk_firmware/pull/23772))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: M, Part 2 ([#23773](https://github.com/qmk/qmk_firmware/pull/23773))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: N ([#23774](https://github.com/qmk/qmk_firmware/pull/23774))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: O ([#23778](https://github.com/qmk/qmk_firmware/pull/23778))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: P, Part 1 ([#23779](https://github.com/qmk/qmk_firmware/pull/23779))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: P, Part 2 ([#23780](https://github.com/qmk/qmk_firmware/pull/23780))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: Q-R ([#23781](https://github.com/qmk/qmk_firmware/pull/23781))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: S, Part 1 ([#23783](https://github.com/qmk/qmk_firmware/pull/23783))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: S, Part 2 ([#23784](https://github.com/qmk/qmk_firmware/pull/23784))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: T ([#23785](https://github.com/qmk/qmk_firmware/pull/23785))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: U-V ([#23786](https://github.com/qmk/qmk_firmware/pull/23786))
* Remove some useless code from keymaps ([#23787](https://github.com/qmk/qmk_firmware/pull/23787))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: W, Part 1 ([#23788](https://github.com/qmk/qmk_firmware/pull/23788))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: W, Part 2 ([#23789](https://github.com/qmk/qmk_firmware/pull/23789))
* Migrate `LOCKING_*_ENABLE` to Data-Driven: X-Z ([#23790](https://github.com/qmk/qmk_firmware/pull/23790))
* Update GPIO macros in keymaps ([#23792](https://github.com/qmk/qmk_firmware/pull/23792))
* noroadsleft's 0.25.0 Changelogs and Touch-Ups ([#23793](https://github.com/qmk/qmk_firmware/pull/23793))

Keyboard fixes:
* Fix mapping of GUI/ALT for Win/Mac layers ([#22662](https://github.com/qmk/qmk_firmware/pull/22662))
* Adding standard keymap for wave keyboard to fix #22695 ([#22741](https://github.com/qmk/qmk_firmware/pull/22741))
* Fixup qk100 (firmware size) ([#23169](https://github.com/qmk/qmk_firmware/pull/23169))
* Fixup mechlovin/octagon ([#23179](https://github.com/qmk/qmk_firmware/pull/23179))
* Fix up scanning for Djinn, post-asyncUSB. ([#23188](https://github.com/qmk/qmk_firmware/pull/23188))
* Fixup annepro2 ([#23206](https://github.com/qmk/qmk_firmware/pull/23206))
* Fixed keychron q1v1 led config for iso layout ([#23222](https://github.com/qmk/qmk_firmware/pull/23222))
* Fixes for idobao vendor keymaps ([#23246](https://github.com/qmk/qmk_firmware/pull/23246))
* Fixup work_board ([#23266](https://github.com/qmk/qmk_firmware/pull/23266))
* Linworks FAve 87H Keymap Refactor/Bugfix ([#23292](https://github.com/qmk/qmk_firmware/pull/23292))
* Align encoder layout validation with encoder.h logic ([#23330](https://github.com/qmk/qmk_firmware/pull/23330))
* 0xcb/splaytoraid: remove `CONVERT_TO` at keyboard level ([#23395](https://github.com/qmk/qmk_firmware/pull/23395))
* 40percentclub/gherkin: remove `CONVERT_TO` at keyboard level ([#23396](https://github.com/qmk/qmk_firmware/pull/23396))
* Fix spaceholdings/nebula68b ([#23399](https://github.com/qmk/qmk_firmware/pull/23399))
* Fix failing keyboards on develop ([#23406](https://github.com/qmk/qmk_firmware/pull/23406))
* Corrections to split keyboard migrations ([#23462](https://github.com/qmk/qmk_firmware/pull/23462))
* Fix iris via keymap ([#23652](https://github.com/qmk/qmk_firmware/pull/23652))
* xiudi/xd75 - Fix backlight compilation issues ([#23655](https://github.com/qmk/qmk_firmware/pull/23655))

Bugs:
* WS2812 PWM: prefix for DMA defines ([#23111](https://github.com/qmk/qmk_firmware/pull/23111))
* Fix rgblight init ([#23335](https://github.com/qmk/qmk_firmware/pull/23335))
* Fix WAIT_FOR_USB handling ([#23598](https://github.com/qmk/qmk_firmware/pull/23598))
* Fix PS/2 Trackpoint mouse clicks (#22265) ([#23694](https://github.com/qmk/qmk_firmware/pull/23694))
