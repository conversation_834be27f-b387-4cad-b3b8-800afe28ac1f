# QMK Breaking Changes - 2025 May 25 Changelog

## Notable Features

### Flow Tap ([#25125](https://github.com/qmk/qmk_firmware/pull/25125))

Adds Flow Tap as a core tap-hold option to disable HRMs during fast typing, aka Global Quick Tap, Require Prior Idle.

Flow Tap modifies mod-tap MT and layer-tap LT keys such that when pressed within a short timeout of the preceding key, the tapping behavior is triggered. It basically disables the hold behavior during fast typing, creating a "flow of taps." It also helps to reduce the input lag of tap-hold keys during fast typing, since the tapped behavior is sent immediately.

See the [Flow Tap documentation](../tap_hold#flow-tap) for more information.

### Community Modules `1.1.1` ([#25050](https://github.com/qmk/qmk_firmware/pull/25050), [#25187](https://github.com/qmk/qmk_firmware/pull/25187))

Version `1.1.1` introduces support for module defined RGB matrix effects and indicator callbacks, as well as pointing and layer state callbacks.

See the [Community Modules documentation](../features/community_modules) for more information, including the full list of available hooks.

## Changes Requiring User Action

### Updated Keyboard Codebases

| Old Keyboard Name      | New Keyboard Name    |
|------------------------|----------------------|
| chew                   | chew/split           |
| deemen17/de60fs        | deemen17/de60/r1     |
| keyten/kt60hs_t        | keyten/kt60hs_t/v1   |
| plywrks/ply8x          | plywrks/ply8x/solder |
| rookiebwoy/late9/rev1  | ivndbt/late9/rev1    |
| rookiebwoy/neopad/rev1 | ivndbt/neopad/rev1   |

## Deprecation Notices

In line with the [notice period](../support_deprecation_policy#how-much-advance-notice-will-be-given), deprecation notices for larger items are listed here.

### Deprecation of `qmk generate-compilation-database` ([#25237](https://github.com/qmk/qmk_firmware/pull/25237))

This command has been deprecated as it cannot take into account configurables such as [converters](/feature_converters) or environment variables normally specified on the command line; please use the `--compiledb` flag with `qmk compile` instead.

### Deprecation of `usb.force_nkro`/`FORCE_NKRO` ([#25262](https://github.com/qmk/qmk_firmware/pull/25262))

Unpicking the assumption that only USB can do NKRO, forcing of NKRO on every boot has been deprecated. As this setting persists, it produces unnecessary user confusion when the various NKRO keycodes (for example `NK_TOGG`) do not behave as expected.

The new defaults can be configured in the following ways:

:::::tabs

==== keyboard.json

```json [keyboard.json]
{
    "host": { // [!code focus]
        "default": { // [!code focus]
            "nkro": true // [!code focus]
        } // [!code focus]
    } // [!code focus]
}

```

==== keymap.json

```json [keymap.json]
{
    "config": {
        "host": { // [!code focus]
            "default": { // [!code focus]
                "nkro": true // [!code focus]
            } // [!code focus]
        } // [!code focus]
    }
}

```

==== config.h

```c [config.h]
#pragma once

#define NKRO_DEFAULT_ON true // [!code focus]
```

:::::

The deprecated options will be removed in a future breaking changes cycle.

### `CTPC`/`CONVERT_TO_PROTON_C` removal ([#25111](https://github.com/qmk/qmk_firmware/pull/25111))

Deprecated build options `CTPC` and `CONVERT_TO_PROTON_C` have been removed. Users should of these should migrate to `CONVERT_TO=proton_c`.

see the [Converters Feature](../feature_converters)  documentation for more information.

### `DEFAULT_FOLDER` removal ([#23281](https://github.com/qmk/qmk_firmware/pull/23281))

`DEFAULT_FOLDER` was originally introduced to work around limitations within the build system.
Parent folders containing common configuration would create invalid build targets.

With the introduction of [`keyboard.json`](./20240526#keyboard-json) as a configuration file, the build system now has a consistent method to detect build targets.
The `DEFAULT_FOLDER` functionality is now redundant and the intent is for `rules.mk` to become pure configuration.

Backwards compatibility of build targets has been maintained where possible.

### Converter `Pin Compatible` updates ([#20330](https://github.com/qmk/qmk_firmware/pull/20330))

Converter support will be further limited to only function if a keyboard declares that is is compatible.

This can be configured in the following ways:

:::::tabs

==== keyboard.json

```json [keyboard.json]
{
    "development_board": "promicro", // [!code focus]
}
```

==== rules.mk

```make [rules.mk]
PIN_COMPATIBLE = promicro
```

:::::

see the [Converters Feature](../feature_converters)  documentation for more information.

### Deprecation of `encoder_update_{kb|user}`

These callbacks are now considered end-of-life and will be removed over the next breaking changes cycle, ending August 2025. PRs containing these callbacks will be asked to change to use [encoder mapping](/features/encoders#encoder-map).

`ENCODER_MAP_ENABLE` will subsequently be changed to "default-on" when encoders are enabled, and future breaking changes cycles will remove this flag entirely.

To migrate usage of `encoder_update_user` to encoder map you'll need to handle all of the following changes in your `keymap.c`:

:::::tabs

=== 1. Add keycode definitions

Define new keycodes:

```c
enum {
    MY_ENCODER_LEFT = QK_USER, // [!code focus]
    MY_ENCODER_RIGHT, // [!code focus]
};
```

=== 2. Add encoder mapping

Add the keycodes to a new encoder map (optionally with transparent layers above, if you want identical functionality of layer-independence):

```c
#if defined(ENCODER_MAP_ENABLE)
const uint16_t PROGMEM encoder_map[][NUM_ENCODERS][NUM_DIRECTIONS] = {
    [0] = { ENCODER_CCW_CW(MY_ENCODER_LEFT, MY_ENCODER_RIGHT) }, // [!code focus]
    [1] = { ENCODER_CCW_CW(KC_TRNS, KC_TRNS) }, // [!code focus]
    [2] = { ENCODER_CCW_CW(KC_TRNS, KC_TRNS) }, // [!code focus]
    [3] = { ENCODER_CCW_CW(KC_TRNS, KC_TRNS) }, // [!code focus]
};
#endif
```

=== 3. Add keycode processing

Handle the new keycodes within `process_record_user`, much like any other keycode in your keymap:

```c
bool process_record_user(uint16_t keycode, keyrecord_t *record) {
  switch (keycode) {
    case MY_ENCODER_LEFT: // [!code focus]
      if (record->event.pressed) { // [!code focus]
        // Add the same code you had in your `encoder_update_user` for the left-rotation code // [!code focus]
      } // [!code focus]
      return false; // Skip all further processing of this keycode // [!code focus]
    case MY_ENCODER_RIGHT: // [!code focus]
      if (record->event.pressed) { // [!code focus]
        // Add the same code you had in your `encoder_update_user` for the right-rotation code // [!code focus]
      } // [!code focus]
      return false; // Skip all further processing of this keycode // [!code focus]
  }
}
```

=== 4. Remove old code

Remove your implementation of `encoder_update_user` from your `keymap.c`.

::::::

If your board has multiple encoders, each encoder will need its own pair of keycodes defined as per above.

## Full changelist

Core:
* Non-volatile memory data repository pattern ([#24356](https://github.com/qmk/qmk_firmware/pull/24356))
* High resolution scrolling (without feature report parsing) ([#24423](https://github.com/qmk/qmk_firmware/pull/24423))
* Implement battery level interface ([#24666](https://github.com/qmk/qmk_firmware/pull/24666))
* get_keycode_string(): function to format keycodes as strings, for more readable debug logging. ([#24787](https://github.com/qmk/qmk_firmware/pull/24787))
* [Cleanup] Handling of optional `*.mk` files ([#24952](https://github.com/qmk/qmk_firmware/pull/24952))
* Add EOL to non-keyboard files ([#24990](https://github.com/qmk/qmk_firmware/pull/24990))
* use `keycode_string` in unit tests ([#25042](https://github.com/qmk/qmk_firmware/pull/25042))
* Add additional hooks for Community modules ([#25050](https://github.com/qmk/qmk_firmware/pull/25050))
* Remove `CTPC`/`CONVERT_TO_PROTON_C` options ([#25111](https://github.com/qmk/qmk_firmware/pull/25111))
* Flow Tap tap-hold option to disable HRMs during fast typing (aka Global Quick Tap, Require Prior Idle). ([#25125](https://github.com/qmk/qmk_firmware/pull/25125))
* Remove `bluefruit_le_read_battery_voltage` function ([#25129](https://github.com/qmk/qmk_firmware/pull/25129))
* Avoid duplication in generated community modules `rules.mk` ([#25135](https://github.com/qmk/qmk_firmware/pull/25135))
* [chore]: move and rename mouse/scroll min/max defines ([#25141](https://github.com/qmk/qmk_firmware/pull/25141))
* Ignore the Layer Lock key in Repeat Key and Caps Word. ([#25171](https://github.com/qmk/qmk_firmware/pull/25171))
* Allow for disabling EEPROM subsystem entirely. ([#25173](https://github.com/qmk/qmk_firmware/pull/25173))
* Implement connection keycode logic ([#25176](https://github.com/qmk/qmk_firmware/pull/25176))
* Align ChibiOS `USB_WAIT_FOR_ENUMERATION` implementation ([#25184](https://github.com/qmk/qmk_firmware/pull/25184))
* Enable community modules to define LED matrix and RGB matrix effects. ([#25187](https://github.com/qmk/qmk_firmware/pull/25187))
* Bind Bluetooth driver to `host_driver_t` ([#25199](https://github.com/qmk/qmk_firmware/pull/25199))
* Enhance Flow Tap to work better for rolls over multiple tap-hold keys. ([#25200](https://github.com/qmk/qmk_firmware/pull/25200))
* Remove force disable of NKRO when Bluetooth enabled ([#25201](https://github.com/qmk/qmk_firmware/pull/25201))
* [New Feature/Core] New RGB Matrix Animation "Starlight Smooth" ([#25203](https://github.com/qmk/qmk_firmware/pull/25203))
* Add battery changed callbacks ([#25207](https://github.com/qmk/qmk_firmware/pull/25207))
* Generate versions to keycode headers ([#25219](https://github.com/qmk/qmk_firmware/pull/25219))
* Add raw_hid support to host driver ([#25255](https://github.com/qmk/qmk_firmware/pull/25255))
* Deprecate `usb.force_nkro`/`FORCE_NKRO` ([#25262](https://github.com/qmk/qmk_firmware/pull/25262))
* [Chore] use {rgblight,rgb_matrix}_hsv_to_rgb overrides ([#25271](https://github.com/qmk/qmk_firmware/pull/25271))
* Remove outdated `nix` support due to bit-rot. ([#25280](https://github.com/qmk/qmk_firmware/pull/25280))

CLI:
* Align to latest CLI dependencies ([#24553](https://github.com/qmk/qmk_firmware/pull/24553))
* Exclude external userspace from lint checking ([#24680](https://github.com/qmk/qmk_firmware/pull/24680))
* [Modules] Provide access to current path in `rules.mk`. ([#25061](https://github.com/qmk/qmk_firmware/pull/25061))
* Add "license" field to Community Module JSON schema. ([#25085](https://github.com/qmk/qmk_firmware/pull/25085))
* Prompt for converter when creating new keymap ([#25116](https://github.com/qmk/qmk_firmware/pull/25116))
* Extend lint checks to reject duplication of defaults ([#25149](https://github.com/qmk/qmk_firmware/pull/25149))
* Add lint warning for empty url ([#25182](https://github.com/qmk/qmk_firmware/pull/25182))
* Deprecate `qmk generate-compilation-database`. ([#25237](https://github.com/qmk/qmk_firmware/pull/25237))
* Use relative paths for schemas, instead of $id. Enables VScode validation. ([#25251](https://github.com/qmk/qmk_firmware/pull/25251))

Submodule updates:
* STM32G0x1 support ([#24301](https://github.com/qmk/qmk_firmware/pull/24301))
* Update develop branch to Pico SDK 1.5.1 ([#25178](https://github.com/qmk/qmk_firmware/pull/25178))
* Add `compiler_support.h` ([#25274](https://github.com/qmk/qmk_firmware/pull/25274))

Keyboards:
* add 75_(ansi|iso) Community Layouts to mechlovin/olly/octagon ([#22459](https://github.com/qmk/qmk_firmware/pull/22459))
* Add the plywrks ply8x hotswap variant. ([#23558](https://github.com/qmk/qmk_firmware/pull/23558))
* Add Community Layout support to daskeyboard4 ([#23884](https://github.com/qmk/qmk_firmware/pull/23884))
* New standard layout for Savage65 (65_ansi_blocker_tsangan_split_bs) ([#24690](https://github.com/qmk/qmk_firmware/pull/24690))
* Add Icebreaker keyboard ([#24723](https://github.com/qmk/qmk_firmware/pull/24723))
* Update Tractyl Manuform and add F405 (weact) variant ([#24764](https://github.com/qmk/qmk_firmware/pull/24764))
* Chew folders ([#24785](https://github.com/qmk/qmk_firmware/pull/24785))
* modelh: add prerequisites for via support ([#24932](https://github.com/qmk/qmk_firmware/pull/24932))
* Only configure `STM32_HSECLK` within `board.h` ([#25001](https://github.com/qmk/qmk_firmware/pull/25001))
* Allow LVGL onekey keymap to be able compile for other board ([#25005](https://github.com/qmk/qmk_firmware/pull/25005))
* Remove Sofle `rgb_default` keymap & tidy readme's ([#25010](https://github.com/qmk/qmk_firmware/pull/25010))
* Migrate remaining `split.soft_serial_pin` to `split.serial.pin` ([#25046](https://github.com/qmk/qmk_firmware/pull/25046))
* Update keymap for keycult 1800 ([#25070](https://github.com/qmk/qmk_firmware/pull/25070))
* Add kt60HS-T v2 PCB ([#25080](https://github.com/qmk/qmk_firmware/pull/25080))
* Refactor Deemen17 Works DE60 ([#25088](https://github.com/qmk/qmk_firmware/pull/25088))
* Rookiebwoy to ivndbt ([#25142](https://github.com/qmk/qmk_firmware/pull/25142))
* Remove duplication of RGB Matrix defaults ([#25146](https://github.com/qmk/qmk_firmware/pull/25146))
* ymdk/id75/rp2040 ([#25157](https://github.com/qmk/qmk_firmware/pull/25157))
* Remove duplication of RGBLight defaults ([#25169](https://github.com/qmk/qmk_firmware/pull/25169))
* Remove empty `url` fields ([#25181](https://github.com/qmk/qmk_firmware/pull/25181))
* Remove more duplication of defaults ([#25189](https://github.com/qmk/qmk_firmware/pull/25189))
* Remove `"console":false` from keyboards ([#25190](https://github.com/qmk/qmk_firmware/pull/25190))
* Remove `"command":false` from keyboards ([#25193](https://github.com/qmk/qmk_firmware/pull/25193))
* Remove redundant keyboard headers ([#25208](https://github.com/qmk/qmk_firmware/pull/25208))
* Add debounce to duplicated defaults check ([#25246](https://github.com/qmk/qmk_firmware/pull/25246))
* Remove duplicate of SPI default config from keyboards ([#25266](https://github.com/qmk/qmk_firmware/pull/25266))
* Resolve miscellaneous keyboard lint warnings ([#25268](https://github.com/qmk/qmk_firmware/pull/25268))
* Configure boards to use development_board - 0-9 ([#25287](https://github.com/qmk/qmk_firmware/pull/25287))
* Configure boards to use development_board - UVWXYZ ([#25288](https://github.com/qmk/qmk_firmware/pull/25288))
* Configure boards to use development_board - S ([#25293](https://github.com/qmk/qmk_firmware/pull/25293))
* Configure boards to use development_board - T ([#25294](https://github.com/qmk/qmk_firmware/pull/25294))

Keyboard fixes:
* Fix `boardsource/beiwagon` RGB Matrix coordinates ([#25018](https://github.com/qmk/qmk_firmware/pull/25018))
* amptrics/0422 - Prevent OOB in `update_leds_for_layer` ([#25209](https://github.com/qmk/qmk_firmware/pull/25209))
* salicylic_acid3/getta25 - Fix oled keymap ([#25295](https://github.com/qmk/qmk_firmware/pull/25295))

Others:
* Require 'x'/'y' properties for LED/RGB Matrix layout ([#24997](https://github.com/qmk/qmk_firmware/pull/24997))
* Align `new-keyboard` template to current standards ([#25191](https://github.com/qmk/qmk_firmware/pull/25191))

Bugs:
* Fix OS_DETECTION_KEYBOARD_RESET ([#25015](https://github.com/qmk/qmk_firmware/pull/25015))
* Fix outdated GPIO control function usage ([#25060](https://github.com/qmk/qmk_firmware/pull/25060))
* Cater for use of `__errno_r()` in ChibiOS syscalls.c with newer picolibc revisions ([#25121](https://github.com/qmk/qmk_firmware/pull/25121))
* Fixup eeconfig lighting reset. ([#25166](https://github.com/qmk/qmk_firmware/pull/25166))
* Fix for Flow Tap: fix handling of distinct taps and timer updates. ([#25175](https://github.com/qmk/qmk_firmware/pull/25175))
* Minimise force-included files ([#25194](https://github.com/qmk/qmk_firmware/pull/25194))
* Ensure `qmk_userspace_paths` maintains detected order ([#25204](https://github.com/qmk/qmk_firmware/pull/25204))
* Resolve alias for `qmk new-keymap` keyboard prompts ([#25210](https://github.com/qmk/qmk_firmware/pull/25210))
* gcc15 AVR compilation fixes ([#25238](https://github.com/qmk/qmk_firmware/pull/25238))
* Fix typos introduced by PR #25050 ([#25250](https://github.com/qmk/qmk_firmware/pull/25250))
* Fix Wear Leveling compilation ([#25254](https://github.com/qmk/qmk_firmware/pull/25254))
* Remove more USB only branches from NKRO handling ([#25263](https://github.com/qmk/qmk_firmware/pull/25263))
* [Fix] lib8tion: enable fixed scale8 and blend functions ([#25272](https://github.com/qmk/qmk_firmware/pull/25272))
* Fix tap_hold code blocks ([#25298](https://github.com/qmk/qmk_firmware/pull/25298))
