# QMK Breaking Changes - 2024 November 24 Changelog

## Notable Features

November 2024 brings a relatively lightweight QMK release. As with previous cycles, almost all PRs were related to cleanup or re-organization of aspects of QMK.

One notable item is the removal of `arm_atsam` -- this affects users with `massdrop/ctrl` or `massdrop/alt` keyboards. These have no current replacement, but support for an alternative to the `arm_atsam` backend is nearing completion. Unfortunately the alternative backend didn't make this quarter's list of merges, so there's a temporary removal of these boards. Users who need to build for these targets should stay on the `0.26.x` version of QMK until these boards have been restored.

## Changes Requiring User Action

### Updated Keyboard Codebases

One note with updated keyboard names -- historical keyboard names are still considered valid when using [External Userspace](../newbs_external_userspace) for builds. If you're already using External Userspace, you do not need to move your keymap inside your repository.

| Old Keyboard Name | New Keyboard Name       |
|-------------------|-------------------------|
| saevus/cor        | concreteflowers/cor     |
| saevus/cor_tkl    | concreteflowers/cor_tkl |

## Deprecation Notices

In line with the [notice period](../support_deprecation_policy#how-much-advance-notice-will-be-given), deprecation notices for larger items are listed here.

### RGB Keycode Overhaul ([#23679](https://github.com/qmk/qmk_firmware/pull/23679), [#24484](https://github.com/qmk/qmk_firmware/pull/24484), [#24490](https://github.com/qmk/qmk_firmware/pull/24490))

RGB keycodes have been reworked to allow for both rgblight and rgb_matrix to coexist for the handful of boards in the repo with both enabled. Previously, `RGB_xxx` style keycodes were available, but now for rgblight we have `UG_xxx` (underglow), and `RM_xxx` for rgb_matrix. Default keymaps have been updated to reflect whichever system the respective board enables.

Longer-term, `RGB_xxx` style keycodes will be removed, but for now they act as if they're `UG_xxx`, and `UG_xxx` keycodes act upon both rgblight and rgb_matrix simultaneously. Put simply, the existing `RGB_xxx` keycodes act as they always have, giving users time to transition across to the new keycodes instead.

### ADNS9800 and PMW33xx sensor firmware ROM removal ([#24428](https://github.com/qmk/qmk_firmware/pull/24428))

Following on from the last Breaking Changes cycle, Binary blobs for optical sensors have been removed from the QMK codebase. Hooks allowing users to supply their own have been put in place; users wishing to source firmware blobs and integrate them into their keymap may do so. Doing so renders their firmware un-distributable due to licensing incompatibility with the GPL -- as such user firmware containing such blobs must not be distributed.

Please get in touch with the QMK team on Discord if your sensor is no longer functional.

## Full changelist

Core:
* Refactor F4 ld files to use common files ([#18059](https://github.com/qmk/qmk_firmware/pull/18059))
* Add LD7032 support to QP. ([#20828](https://github.com/qmk/qmk_firmware/pull/20828))
* Add combo key repress feature ([#22858](https://github.com/qmk/qmk_firmware/pull/22858))
* Add Layer Lock feature ([#23430](https://github.com/qmk/qmk_firmware/pull/23430))
* Added MCU support for ArteryTek AT32F415 ([#23445](https://github.com/qmk/qmk_firmware/pull/23445))
* Allow codegen of `keymap.json` => `keymap.c` without requiring layers/layout etc. ([#23451](https://github.com/qmk/qmk_firmware/pull/23451))
* Separate RGBLight/RGB Matrix keycode handling ([#23679](https://github.com/qmk/qmk_firmware/pull/23679))
* Allow for inverted SPI CS logic ([#23699](https://github.com/qmk/qmk_firmware/pull/23699))
* Add timer_save and _restore functions. ([#23887](https://github.com/qmk/qmk_firmware/pull/23887))
* Allow for `get_hardware_id()` to be used for serial number. ([#24053](https://github.com/qmk/qmk_firmware/pull/24053))
* Segregrate keycode at pre-process record quantum ([#24194](https://github.com/qmk/qmk_firmware/pull/24194))
* Add ability to poweroff ADNS5050 sensor ([#24223](https://github.com/qmk/qmk_firmware/pull/24223))
* quantum: util: add bit and bitmask helpers ([#24229](https://github.com/qmk/qmk_firmware/pull/24229))
* Add new connection keycodes for Bluetooth, 2.4GHz. ([#24251](https://github.com/qmk/qmk_firmware/pull/24251))
* `usb_device_state`: consolidate usb state handling across implementations ([#24258](https://github.com/qmk/qmk_firmware/pull/24258))
* Remove global k_rgb_matrix_split ([#24348](https://github.com/qmk/qmk_firmware/pull/24348))
* util: uf2families: sync with upstream ([#24351](https://github.com/qmk/qmk_firmware/pull/24351))
* [Maintenance] builddefs: common_rules: overhaul debug information generation ([#24352](https://github.com/qmk/qmk_firmware/pull/24352))
* Add easier way to set default single layer ([#24376](https://github.com/qmk/qmk_firmware/pull/24376))
* Tweak OS detect, add OS_DETECTION_SINGLE_REPORT ([#24379](https://github.com/qmk/qmk_firmware/pull/24379))
* Reinstate global `k_rgb_matrix_split` ([#24388](https://github.com/qmk/qmk_firmware/pull/24388))
* Bring supported STM32F4 configs in line with F4x1 ([#24413](https://github.com/qmk/qmk_firmware/pull/24413))
* Extended wheel reports ([#24422](https://github.com/qmk/qmk_firmware/pull/24422))
* Remove binary blobs from optical sensors. ([#24428](https://github.com/qmk/qmk_firmware/pull/24428))
* Remove `STM32_PWM_USE_ADVANCED` references ([#24432](https://github.com/qmk/qmk_firmware/pull/24432))
* Remove `RING_BUFFERED_6KRO_REPORT_ENABLE` due to disuse. ([#24433](https://github.com/qmk/qmk_firmware/pull/24433))
* Move pointing device driver code ([#24445](https://github.com/qmk/qmk_firmware/pull/24445))
* Add svlinky converter ([#24449](https://github.com/qmk/qmk_firmware/pull/24449))
* Update combo user function variable ([#24467](https://github.com/qmk/qmk_firmware/pull/24467))
* `qmk find`: expand operator support ([#24468](https://github.com/qmk/qmk_firmware/pull/24468))
* Rename RGB and HSV structs ([#24471](https://github.com/qmk/qmk_firmware/pull/24471))
* RGBLight: Improve RGB Test animation ([#24477](https://github.com/qmk/qmk_firmware/pull/24477))
* Change default ARM hardware PWM WS2812 tick frequency to 800kHz ([#24508](https://github.com/qmk/qmk_firmware/pull/24508))
* Add pointing tests ([#24513](https://github.com/qmk/qmk_firmware/pull/24513))
* Joystick: add support for 8-way hat switch ([#24515](https://github.com/qmk/qmk_firmware/pull/24515))
* Refactor Riverflow matrix effect with runner ([#24520](https://github.com/qmk/qmk_firmware/pull/24520))
* Update Pixel Rain to respect LED range limits ([#24532](https://github.com/qmk/qmk_firmware/pull/24532))
* Update Jellybean Raindrops to respect LED range limits ([#24534](https://github.com/qmk/qmk_firmware/pull/24534))
* Refactor Breathing effect with runner ([#24535](https://github.com/qmk/qmk_firmware/pull/24535))
* Refactor LED Breathing effect with runner ([#24540](https://github.com/qmk/qmk_firmware/pull/24540))
* Expose rgb_matrix_update_pwm_buffers to be available in keymaps ([#24573](https://github.com/qmk/qmk_firmware/pull/24573))
* Simple Python script to show polling rate ([#24622](https://github.com/qmk/qmk_firmware/pull/24622))
* Add keycode PDF(layer) to set the default layer in EEPROM ([#24630](https://github.com/qmk/qmk_firmware/pull/24630))
* Provide method to save a single default layer in the full range of 0-31 ([#24639](https://github.com/qmk/qmk_firmware/pull/24639))

CLI:
* Refactor painter arguments to table instead of commandline ([#24456](https://github.com/qmk/qmk_firmware/pull/24456))
* `qmk new-keyboard`: separate dev board and MCU selection ([#24548](https://github.com/qmk/qmk_firmware/pull/24548))
* Bump minimum required Python version ([#24554](https://github.com/qmk/qmk_firmware/pull/24554))

Submodule updates:
* Remove `arm_atsam` platform ([#24337](https://github.com/qmk/qmk_firmware/pull/24337))

Keyboards:
* add ergodox Community Layout for LAYOUT_ergodox keyboards ([#22963](https://github.com/qmk/qmk_firmware/pull/22963))
* Cutie Club Fidelity Layout Additions ([#23838](https://github.com/qmk/qmk_firmware/pull/23838))
* Add Ploopyco functions for host state control ([#23953](https://github.com/qmk/qmk_firmware/pull/23953))
* Corne rev4 ([#24084](https://github.com/qmk/qmk_firmware/pull/24084))
* Rename saevus to concreteflowers ([#24249](https://github.com/qmk/qmk_firmware/pull/24249))
* Remove `60_tsangan_hhkb` community layout ([#24355](https://github.com/qmk/qmk_firmware/pull/24355))
* add USART configuration to config.h for PS/2 mouse support ([#24398](https://github.com/qmk/qmk_firmware/pull/24398))
* Add SteelSeries Prime+ mouse ([#24408](https://github.com/qmk/qmk_firmware/pull/24408))
* Rename RGB/HSV structs: keyboard-level code ([#24476](https://github.com/qmk/qmk_firmware/pull/24476))
* xiudi/xd002: Remove broken oversized `multilayer_rgb` keymap ([#24480](https://github.com/qmk/qmk_firmware/pull/24480))
* Update keycodes for keyboards with RGB Matrix ([#24484](https://github.com/qmk/qmk_firmware/pull/24484))
* Cipulot Updates ([#24539](https://github.com/qmk/qmk_firmware/pull/24539))
* Update keymaps to use PDF keycode ([#24633](https://github.com/qmk/qmk_firmware/pull/24633))
* Remove keyboard use of eeconfig_read_default_layer ([#24635](https://github.com/qmk/qmk_firmware/pull/24635))

Keyboard fixes:
* Fix rendering of `keymap_config.no_gui` within `led_update_kb` ([#24473](https://github.com/qmk/qmk_firmware/pull/24473))
* Fix duplication of core `GU_TOGG` keycode ([#24474](https://github.com/qmk/qmk_firmware/pull/24474))
* keebio/nyquist: Remove invalid I2C config ([#24479](https://github.com/qmk/qmk_firmware/pull/24479))
* horrortroll/nyx/rev1: Fix compilation of custom RGB effect ([#24481](https://github.com/qmk/qmk_firmware/pull/24481))

Others:
* Improve consistency of syntax within `data/constants/keycodes` ([#24528](https://github.com/qmk/qmk_firmware/pull/24528))

Bugs:
* WS2812 API rework ([#24364](https://github.com/qmk/qmk_firmware/pull/24364))
* Backward compatibility for new RGB keycode handling ([#24490](https://github.com/qmk/qmk_firmware/pull/24490))
* Fix compiliation issues with OS Detection ([#24502](https://github.com/qmk/qmk_firmware/pull/24502))
* Revert removal of `QK_OUTPUT_AUTO`, fixup docs to reflect. ([#24593](https://github.com/qmk/qmk_firmware/pull/24593))
* Review fixes for layer lock feature ([#24627](https://github.com/qmk/qmk_firmware/pull/24627))
